#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
记忆管理模块
参考My-Dream-Moments-WeChat-wxauto项目的记忆管理功能
"""

import json
import time
import threading
import xxhash
from datetime import datetime, timedelta
from collections import deque
from database import Session, Memory, ChatMessage, GroupContentCache
from openai import OpenAI
import config
from utils.logger import log


class MemoryManager:
    """记忆管理类"""

    def __init__(self):
        self.Session = Session
        self.memory_cache = {}  # 内存缓存字典
        self.cache_lock = threading.Lock()
        self.cache_expiry = 3600  # 缓存有效期1小时
        self.max_cache_size = 200  # 最大缓存条目数
        self.message_hash_cache = {}  # 消息哈希缓存，用于重复检测
        self.max_hash_cache = 50  # 最大哈希缓存数量

        # 初始化OpenAI客户端用于生成摘要
        self.api_client = OpenAI(
            api_key=config.API_KEY,
            base_url=config.BASE_URL
        )

    def _update_cache(self, memory_key, data):
        """更新缓存"""
        with self.cache_lock:
            # LRU淘汰策略
            if len(self.memory_cache) >= self.max_cache_size:
                self.memory_cache.pop(next(iter(self.memory_cache)))
            self.memory_cache[memory_key] = {
                'data': data,
                'timestamp': time.time()
            }

    def _get_from_cache(self, memory_key):
        """从缓存获取数据"""
        with self.cache_lock:
            entry = self.memory_cache.get(memory_key)
            if entry and (time.time() - entry['timestamp']) < self.cache_expiry:
                # 更新访问时间
                entry['timestamp'] = time.time()
                return entry['data']
            if entry:  # 过期缓存删除
                del self.memory_cache[memory_key]
            return None

    def get_memory_key(self, chat_id, is_group=False):
        """生成记忆键"""
        if is_group:
            return f"group_{chat_id}"
        else:
            return f"user_{chat_id}"

    def is_duplicate_message(self, content, memory_key):
        """检查是否为重复消息"""
        # 白名单关键词 - 包含这些词的消息跳过重复检查
        whitelist_keywords = [
            '你好', '在吗', '？', '?', '！', '!',
            '早上好', '晚安', '再见', '谢谢'
        ]

        if any(keyword in content for keyword in whitelist_keywords):
            return False

        # 生成消息内容的稳定哈希
        content_hash = xxhash.xxh64(content.encode('utf-8')).hexdigest()

        # 初始化用户的哈希队列
        if memory_key not in self.message_hash_cache:
            self.message_hash_cache[memory_key] = deque(maxlen=self.max_hash_cache)

        # 精确匹配检查
        if content_hash in self.message_hash_cache[memory_key]:
            return True

        # 记录新哈希
        self.message_hash_cache[memory_key].append(content_hash)
        return False

    def save_message(self, sender_id, sender_name, message, reply, is_group=False, group_id=None):
        """保存聊天记录到数据库"""
        try:
            session = self.Session()
            chat_message = ChatMessage(
                sender_id=sender_id,
                sender_name=sender_name,
                message=message,
                reply=reply,
                is_group=is_group,
                group_id=group_id,
            )
            session.add(chat_message)
            session.commit()
            session.close()
            log(f"聊天记录已保存: {sender_name}")
        except Exception as e:
            log(f"保存聊天记录失败: {str(e)}", "ERROR")

    def save_ai_message(self, sender, message, chat_name, is_group=False):
        """
        保存AI主动发送的消息到数据库

        Args:
            sender: 发送者名称（通常是AI的名字）
            message: 消息内容
            chat_name: 聊天对象名称
            is_group: 是否为群聊
        """
        session = None
        try:
            session = self.Session()

            # 为AI主动发送的消息创建记录
            chat_message = ChatMessage(
                sender_id=sender,  # AI的ID就是名字
                sender_name=sender,
                message="",  # 用户消息为空，因为这是AI主动发送
                reply=message,  # AI的回复就是主动发送的消息
                is_group=is_group,
                group_id=chat_name if is_group else None,
            )
            session.add(chat_message)
            session.commit()
            log(f"AI主动发送消息已保存: {chat_name}")

        except Exception as e:
            log(f"保存AI主动发送消息失败: {str(e)}", "ERROR")
            if session:
                session.rollback()
        finally:
            if session:
                session.close()

    def save_memory(self, memory_key, conversation):
        """保存记忆到数据库"""
        try:
            session = self.Session()

            if memory_key.startswith("group_"):  # 群聊记忆
                group_id = memory_key.split("_", 1)[1]  # 提取真实群ID
                memory = session.query(Memory).filter(
                    Memory.group_id == group_id,
                    Memory.is_group == True
                ).first()
                if not memory:
                    memory = Memory(
                        user_id="",  # 清空用户ID字段
                        group_id=group_id,  # 设置群ID
                        is_group=True  # 标记为群记忆
                    )
            else:  # 私聊记忆
                user_id = memory_key.split("_", 1)[1] if "_" in memory_key else memory_key
                memory = session.query(Memory).filter(
                    Memory.user_id == user_id,
                    Memory.is_group == False
                ).first()
                if not memory:
                    memory = Memory(
                        user_id=user_id,
                        is_group=False
                    )

            # 合并历史记录
            existing_history = json.loads(memory.history) if memory.history else []

            if memory.is_group:
                # 群聊去重逻辑
                new_contents = [msg["content"] for msg in conversation]
                existing_contents = [msg["content"] for msg in existing_history]
                merged_history = existing_history + [
                    msg for msg in conversation
                    if msg["content"] not in existing_contents
                ]
            else:
                # 私聊直接合并
                merged_history = existing_history + conversation

            # 控制历史记录长度
            max_history = config.CONTEXT_MEMORY_SIZE * 2  # 保存更多历史用于生成摘要
            merged_history = merged_history[-max_history:]

            # 生成摘要
            summary = self.generate_summary(merged_history)
            if memory.is_group:
                participants = list(set([msg.get("sender", "") for msg in merged_history if msg.get("sender")]))
                summary = f"【群聊摘要】参与者：{', '.join(participants)}\n{summary}"

            # 更新记忆字段
            memory.history = json.dumps(merged_history, ensure_ascii=False)
            memory.last_summary = summary
            memory.keywords = json.dumps(
                self.extract_keywords(merged_history),
                ensure_ascii=False
            )
            memory.last_update = datetime.now()

            session.merge(memory)
            session.commit()

            # 更新缓存
            cache_data = {
                "history": merged_history,
                "last_summary": summary,
                "keywords": json.loads(memory.keywords),
                "is_group": memory.is_group
            }
            self._update_cache(memory_key, cache_data)

            log(f"记忆已保存: {memory_key}")

        except Exception as e:
            log(f"记忆保存失败: {str(e)}", "ERROR")
        finally:
            session.close()

    def load_memory(self, memory_key):
        """加载记忆"""
        # 先尝试从缓存获取
        cached_data = self._get_from_cache(memory_key)
        if cached_data:
            return cached_data

        try:
            session = self.Session()

            if memory_key.startswith("group_"):  # 群聊记忆
                group_id = memory_key.split("_", 1)[1]
                memory = session.query(Memory).filter(
                    Memory.group_id == group_id,
                    Memory.is_group == True
                ).first()
            else:  # 私聊记忆
                user_id = memory_key.split("_", 1)[1] if "_" in memory_key else memory_key
                memory = session.query(Memory).filter(
                    Memory.user_id == user_id,
                    Memory.is_group == False
                ).first()

            if memory:
                data = {
                    "history": json.loads(memory.history) if memory.history else [],
                    "last_summary": memory.last_summary or "",
                    "keywords": json.loads(memory.keywords) if memory.keywords else [],
                    "is_group": memory.is_group
                }
                # 写入缓存
                self._update_cache(memory_key, data)
                return data
            return None

        except Exception as e:
            log(f"读取记忆失败: {str(e)}", "ERROR")
            return None
        finally:
            session.close()

    def load_user_group_memories(self, user_name):
        """加载用户参与的所有群聊记忆"""
        try:
            session = self.Session()

            # 查询包含该用户的所有群聊记忆
            group_memories = session.query(Memory).filter(
                Memory.is_group == True,
                Memory.history.like(f'%{user_name}%')  # 简单的用户名匹配
            ).all()

            memories_data = []
            for memory in group_memories:
                # 解析历史记录，只保留包含该用户的对话
                history = json.loads(memory.history) if memory.history else []
                user_related_history = []

                for msg in history:
                    # 检查消息是否与该用户相关
                    if (msg.get('sender') == user_name or
                        user_name in msg.get('content', '') or
                        msg.get('role') == 'assistant'):  # 保留AI的回复作为上下文
                        user_related_history.append(msg)

                if user_related_history:  # 只有包含用户相关内容的群聊才添加
                    memories_data.append({
                        'group_id': memory.group_id,
                        'history': user_related_history,
                        'last_summary': memory.last_summary or "",
                        'keywords': json.loads(memory.keywords) if memory.keywords else []
                    })

            log(f"为用户 {user_name} 加载了 {len(memories_data)} 个群聊记忆")
            return memories_data

        except Exception as e:
            log(f"加载用户群聊记忆失败: {str(e)}", "ERROR")
            return []
        finally:
            session.close()

    def load_user_private_memories(self, user_name):
        """加载用户的私聊记忆"""
        try:
            session = self.Session()

            # 查询该用户的私聊记忆
            private_memory = session.query(Memory).filter(
                Memory.user_id == user_name,
                Memory.is_group == False
            ).first()

            if private_memory:
                # 解析历史记录
                history = json.loads(private_memory.history) if private_memory.history else []

                if history:  # 只有包含历史记录的私聊才返回
                    memory_data = {
                        'user_id': private_memory.user_id,
                        'history': history,
                        'last_summary': private_memory.last_summary or "",
                        'keywords': json.loads(private_memory.keywords) if private_memory.keywords else []
                    }

                    log(f"为用户 {user_name} 加载了私聊记忆，共 {len(history)} 条记录")
                    return [memory_data]  # 返回列表格式以保持一致性

            log(f"用户 {user_name} 没有私聊记忆")
            return []

        except Exception as e:
            log(f"加载用户私聊记忆失败: {str(e)}", "ERROR")
            return []
        finally:
            session.close()

    def generate_summary(self, conversation):
        """生成对话摘要"""
        if not conversation:
            return "暂无对话历史"

        try:
            # 调用API生成摘要
            prompt = "请用150字以内总结以下对话的核心信息，注意保留重要细节：\n"
            history = "\n".join([f"{msg['role']}: {msg['content']}" for msg in conversation[-8:]])

            response = self.api_client.chat.completions.create(
                model=config.MODEL,
                messages=[{"role": "user", "content": prompt + history}],
                temperature=0.3,
                max_tokens=200
            )
            return response.choices[0].message.content
        except Exception as e:
            log(f"生成摘要失败: {str(e)}", "ERROR")
            return "摘要生成失败"

    def extract_keywords(self, conversation):
        """提取关键词"""
        if not conversation:
            return []

        try:
            # 简单的关键词提取
            import re
            text = " ".join([msg["content"] for msg in conversation])
            # 提取中文词汇和英文单词
            chinese_words = re.findall(r'[\u4e00-\u9fff]+', text)
            english_words = re.findall(r'\b[a-zA-Z]{3,}\b', text)

            # 合并并去重，取前10个
            keywords = list(set(chinese_words + english_words))[:10]
            return keywords
        except Exception as e:
            log(f"提取关键词失败: {str(e)}", "ERROR")
            return []

    def cleanup_old_memories(self, max_days=30):
        """清理旧记忆"""
        try:
            session = self.Session()
            # 计算截止日期
            cutoff_date = datetime.now() - timedelta(days=max_days)

            # 执行删除操作
            deleted_count = session.query(Memory).filter(
                Memory.last_update < cutoff_date
            ).delete()

            session.commit()
            log(f"已清理{deleted_count}条过期记忆数据")

        except Exception as e:
            session.rollback()
            log(f"记忆清理失败: {str(e)}", "ERROR")
        finally:
            session.close()

    def get_memory_stats(self):
        """获取记忆统计信息"""
        try:
            session = self.Session()

            total_memories = session.query(Memory).count()
            user_memories = session.query(Memory).filter(Memory.is_group == False).count()
            group_memories = session.query(Memory).filter(Memory.is_group == True).count()
            total_messages = session.query(ChatMessage).count()

            return {
                "total_memories": total_memories,
                "user_memories": user_memories,
                "group_memories": group_memories,
                "total_messages": total_messages,
                "cache_size": len(self.memory_cache)
            }

        except Exception as e:
            log(f"获取记忆统计失败: {str(e)}", "ERROR")
            return {}
        finally:
            session.close()

    def clear_memory(self, memory_key):
        """清除指定的记忆"""
        try:
            session = self.Session()

            if memory_key.startswith("group_"):  # 群聊记忆
                group_id = memory_key.split("_", 1)[1]
                deleted_count = session.query(Memory).filter(
                    Memory.group_id == group_id,
                    Memory.is_group == True
                ).delete()
            else:  # 私聊记忆
                user_id = memory_key.split("_", 1)[1] if "_" in memory_key else memory_key
                deleted_count = session.query(Memory).filter(
                    Memory.user_id == user_id,
                    Memory.is_group == False
                ).delete()

            session.commit()

            # 清除缓存
            if memory_key in self.memory_cache:
                del self.memory_cache[memory_key]

            log(f"已清除记忆: {memory_key}, 删除{deleted_count}条记录")
            return deleted_count > 0

        except Exception as e:
            session.rollback()
            log(f"清除记忆失败: {str(e)}", "ERROR")
            return False
        finally:
            session.close()

    def save_group_content_cache(self, group_id, sender_name, message_content):
        """保存群聊内容到缓存"""
        if not getattr(config, 'ENABLE_GROUP_CONTENT_CACHE', True):
            return

        try:
            session = self.Session()

            # 创建群聊内容缓存记录
            cache_entry = GroupContentCache(
                group_id=group_id,
                sender_name=sender_name,
                message_content=message_content
            )
            session.add(cache_entry)

            # 清理旧的缓存记录，保持缓存大小限制
            cache_size = getattr(config, 'GROUP_CONTENT_CACHE_SIZE', 50)
            old_entries = session.query(GroupContentCache).filter(
                GroupContentCache.group_id == group_id
            ).order_by(GroupContentCache.created_at.desc()).offset(cache_size).all()

            for entry in old_entries:
                session.delete(entry)

            # 清理过期的缓存记录
            cache_hours = getattr(config, 'GROUP_CONTENT_CACHE_HOURS', 24)
            expire_time = datetime.now() - timedelta(hours=cache_hours)
            expired_entries = session.query(GroupContentCache).filter(
                GroupContentCache.group_id == group_id,
                GroupContentCache.created_at < expire_time
            ).all()

            for entry in expired_entries:
                session.delete(entry)

            session.commit()
            log(f"群聊内容缓存已保存: {group_id} - {sender_name}")

        except Exception as e:
            session.rollback()
            log(f"保存群聊内容缓存失败: {str(e)}", "ERROR")
        finally:
            session.close()

    def get_group_content_cache(self, group_id, limit=None):
        """获取群聊内容缓存"""
        if not getattr(config, 'ENABLE_GROUP_CONTENT_CACHE', True):
            return []

        try:
            session = self.Session()

            # 设置默认限制
            if limit is None:
                limit = getattr(config, 'GROUP_CACHE_CONTEXT_SIZE', 20)

            # 获取最近的群聊内容
            cache_entries = session.query(GroupContentCache).filter(
                GroupContentCache.group_id == group_id
            ).order_by(GroupContentCache.created_at.desc()).limit(limit).all()

            # 转换为字典格式并按时间正序排列
            cache_data = [entry.to_dict() for entry in reversed(cache_entries)]

            log(f"获取群聊内容缓存: {group_id}, 共{len(cache_data)}条")
            return cache_data

        except Exception as e:
            log(f"获取群聊内容缓存失败: {str(e)}", "ERROR")
            return []
        finally:
            session.close()

    def format_group_cache_for_ai(self, group_id, limit=None):
        """格式化群聊缓存内容供AI使用"""
        cache_data = self.get_group_content_cache(group_id, limit)

        if not cache_data:
            return ""

        # 格式化为AI可理解的上下文
        formatted_lines = ["[群聊最近对话内容]"]
        for entry in cache_data:
            formatted_lines.append(f"{entry['message_time']} {entry['sender_name']}: {entry['message_content']}")

        formatted_lines.append("[以上是群聊最近的对话内容，供你参考]")
        return "\n".join(formatted_lines)

    def clear_group_content_cache(self, group_id):
        """清除指定群聊的内容缓存"""
        try:
            session = self.Session()

            deleted_count = session.query(GroupContentCache).filter(
                GroupContentCache.group_id == group_id
            ).delete()

            session.commit()
            log(f"已清除群聊内容缓存: {group_id}, 删除{deleted_count}条记录")
            return deleted_count > 0

        except Exception as e:
            session.rollback()
            log(f"清除群聊内容缓存失败: {str(e)}", "ERROR")
            return False
        finally:
            session.close()

    def get_user_interaction_count(self, sender_name, memory_key):
        """获取用户的交互次数"""
        try:
            session = self.Session()

            # 根据memory_key类型确定查询条件
            if memory_key.startswith("group_"):
                # 群聊：统计该用户在指定群聊中的消息数量
                group_id = memory_key.split("_", 1)[1]
                count = session.query(ChatMessage).filter(
                    ChatMessage.sender_name == sender_name,
                    ChatMessage.group_id == group_id,
                    ChatMessage.is_group == True
                ).count()
            else:
                # 私聊：统计该用户的私聊消息数量
                user_id = memory_key.split("_", 1)[1] if "_" in memory_key else memory_key
                count = session.query(ChatMessage).filter(
                    ChatMessage.sender_name == sender_name,
                    ChatMessage.sender_id == user_id,
                    ChatMessage.is_group == False
                ).count()

            return count

        except Exception as e:
            log(f"获取用户交互次数失败: {str(e)}", "ERROR")
            return 0
        finally:
            session.close()
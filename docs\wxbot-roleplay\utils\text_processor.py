#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本处理模块
参考My-Dream-Moments-WeChat-wxauto项目的文本处理功能
包含括号内容移除、句子分割等功能
"""

import re
import time
import config
from utils.logger import log
import json


def remove_brackets_content(text: str) -> str:
    """
    删除所有括号及其内容，包括中文括号和英文括号
    支持嵌套括号的处理
    """
    if not text:
        return ""

    try:
        # 同时处理中文括号和英文括号，包括嵌套情况
        text = re.sub(r'（[^）]*（[^）]*）[^）]*）', '', text)  # 先处理嵌套中文括号
        text = re.sub(r'\([^)]*\([^)]*\)[^)]*\)', '', text)   # 处理嵌套英文括号

        # 处理单层括号
        text = re.sub(r'（[^）]*）', '', text)  # 中文圆括号
        text = re.sub(r'\([^)]*\)', '', text)   # 英文圆括号
        text = re.sub(r'【[^】]*】', '', text)  # 中文方括号
        text = re.sub(r'\[[^\]]*\]', '', text)  # 英文方括号

        # 处理其他类型的括号
        text = re.sub(r'〈[^〉]*〉', '', text)   # 中文尖括号
        text = re.sub(r'<[^>]*>', '', text)     # 英文尖括号
        text = re.sub(r'｛[^｝]*｝', '', text)   # 全角花括号
        text = re.sub(r'\{[^}]*\}', '', text)   # 英文花括号

        return text.strip()
    except Exception as e:
        log(f"移除括号内容失败: {str(e)}", "ERROR")
        return text


def filter_brackets_content(text: str) -> str:
    """
    过滤所有括号及其内容（包括中文和英文括号）
    这是remove_brackets_content的别名函数，保持兼容性
    """
    return remove_brackets_content(text)


def format_response(reply: str) -> str:
    """
    在指定标点后添加反斜杠进行句子分割，同时处理已有分隔符和图片标记
    优化逻辑：
    1. 先处理图片部分和非图片部分
    2. 只在非图片内容中添加分隔符
    3. 智能合并相邻分隔符
    """
    if not reply:
        return ""

    try:
        # 先将逗号和分号替换为分隔符
        reply = re.sub(r'[，,；;]', r'\\', reply)

        # 定义匹配模式（包含中日文标点）
        pattern = r'([…]{2,}|\.{2,}|。{2,}|[~。～！!？\)\]）】]|——+)'

        # 分割图片部分和普通内容
        image_blocks = re.split(r'(\[IMAGE\].*?\[\/IMAGE\])', reply, flags=re.DOTALL)

        processed = []
        for block in image_blocks:
            # 处理非图片部分
            if not block.startswith('[IMAGE]'):
                # 移除中英文引号
                block = re.sub(r'["""]', '', block)
                # 在指定标点后添加分隔符
                modified = re.sub(pattern, r'\1\\', block)
                # 合并相邻分隔符（超过2个时保留一个）
                modified = re.sub(r'\\{2,}', r'\\', modified)
                # 移除行末多余分隔符（保留行首的）
                modified = re.sub(r'(?<!\\)\\$', '', modified)
                processed.append(modified)
            else:
                processed.append(block)

        # 最终处理
        final_reply = ''.join(processed)
        final_reply = final_reply.replace('[IMAGE]\\', '[IMAGE]').replace('\\[/IMAGE]', '[/IMAGE]')

        # 特殊处理连续点后的分隔符
        final_reply = re.sub(r'(\.{2,})(\\*)', r'\1\\', final_reply)
        final_reply = re.sub(r'(。{2,})(\\*)', r'\1\\', final_reply)
        # 处理特殊破折号情况
        final_reply = re.sub(r'(——+)(\\*)', r'\1\\', final_reply)
        final_reply = re.sub(r'([….]{3,})(\\*)', r'\1\\', final_reply)

        return final_reply
    except Exception as e:
        log(f"格式化回复失败: {str(e)}", "ERROR")
        return reply


def split_sentences(text: str) -> list:
    """
    将文本按照分隔符分割成句子列表
    """
    if not text:
        return []

    try:
        # 使用反斜杠分割句子
        sentences = [s.strip() for s in text.split('\\') if s.strip()]
        return sentences
    except Exception as e:
        log(f"分割句子失败: {str(e)}", "ERROR")
        return [text]


def apply_message_delay(sentence_index: int, total_sentences: int):
    """
    统一的消息延迟函数

    Args:
        sentence_index: 当前句子索引（从0开始）
        total_sentences: 总句子数量
    """
    try:
        # 检查是否启用延迟且不是第一句
        if (config.ENABLE_MESSAGE_DELAY and sentence_index > 0):
            time.sleep(config.MESSAGE_SENTENCE_DELAY)
            log(f"应用消息延迟: {config.MESSAGE_SENTENCE_DELAY}秒")
    except Exception as e:
        log(f"应用消息延迟失败: {str(e)}", "ERROR")


def clean_group_message(text: str) -> str:
    """
    清理群聊消息格式
    移除群聊前缀和特殊字符
    """
    if not text:
        return ""

    try:
        # 修正正则表达式（原模式无法匹配中文括号）
        cleaned = re.sub(r'^$.*?$ 群聊 <.*?> .+?:', '', text)
        return re.sub(r'[◇◆【】]', '', cleaned).strip()
    except Exception as e:
        log(f"清理群聊消息失败: {str(e)}", "ERROR")
        return text


def clean_text_content(text: str) -> str:
    """
    综合文本清理函数
    移除括号内容并格式化文本
    """
    if not text:
        return ""

    try:
        # 先移除括号内容
        cleaned = remove_brackets_content(text)
        # 再格式化回复
        formatted = format_response(cleaned)
        return formatted
    except Exception as e:
        log(f"清理文本内容失败: {str(e)}", "ERROR")
        return text


def extract_image_tags(text: str) -> tuple:
    """
    提取文本中的图片标签
    返回 (纯文本, 图片路径列表)
    """
    if not text:
        return "", []

    try:
        # 提取所有图片标签
        image_pattern = r'\[IMAGE\](.*?)\[/IMAGE\]'
        images = re.findall(image_pattern, text, re.DOTALL)

        # 移除图片标签，保留纯文本
        clean_text = re.sub(image_pattern, '', text, flags=re.DOTALL)
        clean_text = clean_text.strip()

        return clean_text, images
    except Exception as e:
        log(f"提取图片标签失败: {str(e)}", "ERROR")
        return text, []


class SentimentAnalyzer:
    """
    情感分析器 - 基于关键词、规则和上下文的情感强度分级
    新增功能：
    1. 缓存机制提升性能
    2. 上下文感知分析
    3. 情感强度动态调整
    4. 更精准的网络用语识别
    """

    def __init__(self):
        # 分析结果缓存（提升性能）
        self._cache = {}
        self._cache_max_size = 1000
        # 情感关键词字典 - 优化版本，按频率和准确性重新排序
        self.emotion_keywords = {
            'positive': {
                'high': [
                    # 高频高准确度积极词汇（按使用频率排序）
                    '牛逼', '666', '哈哈哈', '太棒了', '爱了', '绝了', '完美', '超赞', '厉害',
                    '笑死', '给力', '优秀', '无敌', '神了', '满分', '爽', '开心到飞起',
                    # 网络流行语（高情感强度）
                    '绝绝子', '爱了爱了', '冲冲冲', '奥利给', '真香', '香死了', '顶呱呱',
                    '杠杠的', '巴适', '安逸', '爽歪歪', '笑死我了', '笑哭', '笑疯了',
                    '大佬', '大神', '赞赞赞', '点赞', '棒棒哒', '元气满满', '美滋滋',
                    # 传统强烈积极词汇
                    '超级开心', '非常高兴', '爱死了', '激动', '兴奋', '狂欢', '太好了',
                    '强无敌', '神仙', '天才', '棒棒的', '秀啊', '满血复活', '甜死了', '暖心'
                ],
                'medium': [
                    # 中频中等积极词汇
                    '开心', '高兴', '不错', '好的', '满意', '喜欢', '赞', '棒', '很好',
                    '可以', 'ok', 'OK', '好耶', '耶', '嘿嘿', '嘻嘻', '哈哈', '呵呵',
                    '舒服了', '安排', '稳', '妥了', '搞定', '没问题', '小意思', '轻松',
                    '挺好', '挺不错', '蛮好', '还行吧', '过得去', '支持', '赞同', '同意',
                    '认同', '理解', '懂了', '明白了', 'get到了', 'get', '有趣', '好玩',
                    '好笑', '逗', '逗比', '可爱', '萌', '萌萌哒', '乖', '听话'
                ],
                'low': [
                    # 传统低强度积极词汇
                    '还行', '可以', '行', '好吧', '嗯', '哦', '还可以', '凑合',
                    # 轻微积极表达
                    '嗯嗯', '嗯哼', '嗯呐', '嗯啊', '哦哦', '哦呀', '哦吼', '呀', '呀呀',
                    '还好', '还好吧', '还成', '还中', '马马虎虥', '一般般吧', '过得去吧',
                    '算了算了', '就这样吧', '随便', '随意', '都行', '都可以', '看着办'
                ]
            },
            'negative': {
                'high': [
                    # 高频高强度消极词汇（包含脏话，按严重程度排序）
                    'sb', 'SB', '傻逼', '草', '卧槽', '我草', '艹', '操', '妈的', '滚',
                    '垃圾', '辣鸡', '拉胯', '废物', '气死了', '恶心', '恶心死了', '🤮',
                    '气炸了', '炸了', '爆炸', '崩溃', '绝望', '完蛋了', '破防了',
                    # 网络负面用语
                    '沙雕', '智障', '脑残', '白痴', '蠢货', '草泥马', '他妈的', '去死',
                    '滚蛋', '滚犊子', '菜鸡', '菜狗', '菜逼', '弱鸡', '渣渣', '想吐',
                    '吐了', '呕', '恶心人', '膈应', '膈应死了', '气死我了', '暴怒',
                    '狂怒', '火大', '火冒三丈', '绝望了', '完了', '死了', '要死了',
                    '崩了', '裂开', '心态崩了', '恨死了', '讨厌透了', '烦死了',
                    '烦透了', '受不了', '忍不了', '无语了', '无语死了'
                ],
                'medium': [
                    # 传统中等消极词汇
                    '难过', '生气', '不开心', '郁闷', '烦', '讨厌', '糟糕', '失望', '沮丧', '不爽',
                    # 网络用语和现代表达
                    '烦人', '烦死', '烦躁', '焦虑', '焦躁', '急死了', '急', '着急', '担心', '害怕',
                    '郁闷死了', '郁闷啊', '闷', '闷死了', '压抑', '压抑死了', '憋屈', '憋得慌',
                    '不爽啊', '不爽死了', '不舒服', '难受', '难受死了', '痛苦啊', '痛', '疼',
                    '失落', '失落啊', '落寞', '孤独', '寂寞', '空虚', '空虚寂寞冷', '冷漠',
                    '无奈', '无奈啊', '没办法', '没辙', '束手无策', '头疼', '头大', '头痛',
                    '累', '累死了', '疲惫', '疲惫不堪', '精疲力尽', '心累', '身心俱疲',
                    '后悔', '后悔死了', '遗憾', '可惜', '惋惜', '心疼', '心痛', '心酸'
                ],
                'low': [
                    # 传统低强度消极词汇
                    '有点烦', '不太好', '一般般', '算了', '无聊', '没意思', '唉',
                    # 轻微消极表达
                    '唉呀', '哎', '哎呀', '哎哟', '嗯...', '呃...', '额...', '这...',
                    '有点累', '有点烦躁', '有点郁闷', '有点不爽', '有点失望', '有点难过',
                    '不太行', '不太好吧', '不怎么样', '不咋地', '一般', '普通', '平庸',
                    '没劲', '没意思', '没趣', '乏味', '单调', '枯燥', '无聊死了',
                    '懒得', '懒得说', '懒得理', '不想说', '不想理', '随便吧', '爱咋咋地'
                ]
            },
            'neutral': [
                # 纯中性确认词汇（只有这些才会被强制判为中性）
                '知道了', '收到', '明白了', '了解了', '清楚了', '懂了', '晓得了',
                '是的', '对的', '嗯嗯', '哦哦', '原来如此', '我看看', '让我想想'
            ]
        }

        # 初始化优化组件
        self._init_optimization_components()
        self._init_modifiers_and_punctuation()

    def _init_optimization_components(self):
        """初始化优化组件"""
        # 上下文关键词（用于提升分析准确性）
        self.context_keywords = {
            'irony': ['呵呵', '呵呵呵', '好的呢', '是吗', '哦是吗', '真的吗', '厉害了'],  # 讽刺语境
            'emphasis': ['真的', '确实', '就是', '特别', '尤其', '非常', '超级'],  # 强调语境
            'question': ['吗', '呢', '吧', '？', '?', '什么', '怎么', '为什么'],  # 疑问语境
            'negation': ['不', '没', '别', '非', '无', '未', '否', '勿']  # 否定语境
        }

        # 情感强度权重（用于动态调整）
        self.intensity_weights = {
            'high': 1.0,
            'medium': 0.7,
            'low': 0.4
        }

    def _clean_cache(self):
        """清理缓存（当缓存过大时）"""
        if len(self._cache) > self._cache_max_size:
            # 保留最近使用的一半缓存
            items = list(self._cache.items())
            self._cache = dict(items[-self._cache_max_size//2:])

    def _get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        return f"{hash(text)}_{len(text)}"

    def _init_modifiers_and_punctuation(self):
        """初始化修饰词和标点符号（在__init__中调用）"""
        # 情感修饰词 - 扩展版本
        self.intensity_modifiers = {
            'high': [
                # 传统高强度修饰词
                '超级', '非常', '特别', '极其', '太', '超', '巨', '狂', '疯狂', '死了',
                # 网络用语和现代表达
                '贼', '贼他妈', '老', '老子', '巨他妈', '巨tm', 'tm', 'TM', '他妈',
                '超他妈', '超tm', '炸裂', '爆', '爆炸', '炸', '无敌', '绝对', '完全',
                '彻底', '彻彻底底', '百分百', '百分之百', '绝', '绝绝', '真的',
                '真他妈', '真tm', '实在', '实在是', '简直', '简直了', '要命',
                '要死', '要死了', '快死了', '差点死', '险些', '几乎', '差不多要'
            ],
            'medium': [
                # 传统中等强度修饰词
                '很', '挺', '比较', '还', '蛮', '相当',
                # 扩展表达
                '挺他妈', '还挺', '还蛮', '还比较', '还相当', '还算', '算是',
                '多少', '多少有点', '多多少少', '或多或少', '多多少少有点',
                '不少', '不太少', '不算少', '颇', '颇为', '颇有', '颇具',
                '略', '略有', '略微有点', '稍', '稍有', '稍稍', '稍稍有点'
            ],
            'low': [
                # 传统低强度修饰词
                '有点', '稍微', '略微', '一点', '些许',
                # 扩展表达
                '一丢丢', '一点点', '一丁点', '一星半点', '微微', '淡淡',
                '轻微', '轻轻', '浅浅', '淡淡的', '微微的', '轻微的',
                '似乎', '好像', '仿佛', '感觉', '感觉有点', '貌似', '看起来',
                '差不多', '大概', '大致', '基本', '基本上', '差不多有点'
            ]
        }

        # 标点符号情感强度 - 扩展版本
        self.punctuation_intensity = {
            # 强烈积极情感
            '!!!': 0.8, '!!': 0.6, '!': 0.4,
            '！！！': 0.8, '！！': 0.6, '！': 0.4,
            '哈哈哈哈哈': 0.8, '哈哈哈哈': 0.7, '哈哈哈': 0.6, '哈哈': 0.4,
            '嘻嘻嘻': 0.6, '嘻嘻': 0.4, '嘿嘿嘿': 0.6, '嘿嘿': 0.4,
            '呵呵呵': 0.3, '呵呵': 0.2,  # 呵呵可能是讽刺，强度较低

            # 疑问/困惑
            '？？？': 0.6, '？？': 0.4, '？': 0.2,
            '???': 0.6, '??': 0.4, '?': 0.2,

            # 消极/犹豫/无奈
            '...': -0.2, '。。。': -0.2, '……': -0.3, '…': -0.2,
            '唉...': -0.4, '哎...': -0.4, '唉……': -0.4,
            '呃...': -0.1, '额...': -0.1, '嗯...': -0.1,

            # 轻松/调皮
            '~~~': 0.3, '~~': 0.2, '~': 0.1,
            '啦啦啦': 0.5, '啦啦': 0.3, '啦': 0.2,
            '呀呀呀': 0.4, '呀呀': 0.3, '呀': 0.2,

            # 表情符号相关
            '😂': 0.6, '🤣': 0.7, '😄': 0.5, '😊': 0.4, '😁': 0.5,
            '😭': -0.5, '😢': -0.4, '😞': -0.3, '😔': -0.3,
            '😡': -0.7, '😠': -0.6, '🤬': -0.8, '😤': -0.5,
            '🤮': -0.6, '🤢': -0.5, '😷': -0.2, '🤒': -0.3,
            '❤️': 0.6, '💕': 0.5, '💖': 0.6, '💗': 0.5, '💘': 0.5,
            '👍': 0.4, '👎': -0.4, '🙄': -0.3, '😒': -0.3,

            # 重复字符
            '草草草': -0.7, '草草': -0.5, '艹艹艹': -0.7, '艹艹': -0.5,
            '666': 0.6, '6666': 0.7, '66666': 0.8,
            '233': 0.4, '2333': 0.5, '23333': 0.6,  # 网络笑声
            '555': -0.4, '5555': -0.5, '呜呜呜': -0.5, '呜呜': -0.4,

            # 其他网络表达
            'hhh': 0.4, 'hhhh': 0.5, 'hhhhh': 0.6,
            'www': 0.3, 'wwww': 0.4, 'wwwww': 0.5,  # 日式笑声
            'orz': -0.3, 'OTZ': -0.3, '_(:з」∠)_': -0.2,  # 无奈表情
        }

    def analyze_sentiment_intensity(self, text: str) -> dict:
        """
        优化版情感强度分析
        返回格式: {
            "label": "positive/negative/neutral",
            "intensity": "high/medium/low",
            "score": 0.0-1.0,
            "details": "分析详情"
        }
        """
        if not text or not text.strip():
            return {
                "label": "neutral",
                "intensity": "low",
                "score": 0.0,
                "details": "空文本"
            }

        text = text.strip()

        # 检查缓存
        cache_key = self._get_cache_key(text)
        if cache_key in self._cache:
            return self._cache[cache_key].copy()

        # 清理缓存（如果需要）
        self._clean_cache()

        # 初始化分析结果
        positive_score = 0.0
        negative_score = 0.0
        intensity_score = 0.0
        analysis_details = []

        # 上下文分析
        context_info = self._analyze_context(text)
        if context_info['has_context']:
            analysis_details.append(f"上下文: {context_info['type']}")
            # 根据上下文调整后续分析

        # 1. 关键词分析
        for emotion_type, intensity_dict in self.emotion_keywords.items():
            if emotion_type == 'neutral':
                for keyword in intensity_dict:
                    if keyword in text:
                        analysis_details.append(f"中性词: {keyword}")
                        return {
                            "label": "neutral",
                            "intensity": "low",
                            "score": 0.1,
                            "details": "; ".join(analysis_details)
                        }
            else:
                for intensity_level, keywords in intensity_dict.items():
                    for keyword in keywords:
                        if keyword in text:
                            base_score = {'high': 0.8, 'medium': 0.5, 'low': 0.3}[intensity_level]
                            if emotion_type == 'positive':
                                positive_score += base_score
                            else:
                                negative_score += base_score
                            analysis_details.append(f"{emotion_type}-{intensity_level}: {keyword}")

        # 2. 修饰词分析
        for intensity_level, modifiers in self.intensity_modifiers.items():
            for modifier in modifiers:
                if modifier in text:
                    modifier_score = {'high': 0.3, 'medium': 0.2, 'low': 0.1}[intensity_level]
                    intensity_score += modifier_score
                    analysis_details.append(f"修饰词-{intensity_level}: {modifier}")

        # 3. 标点符号分析
        for punct, score in self.punctuation_intensity.items():
            if punct in text:
                if score > 0:
                    positive_score += score * 0.3
                else:
                    negative_score += abs(score) * 0.3
                analysis_details.append(f"标点: {punct}")

        # 4. 特殊模式识别
        special_patterns_score = 0.0
        special_details = []

        # 全大写检测（表示强烈情感）
        if text.isupper() and len(text) > 2:
            special_patterns_score += 0.3
            special_details.append("全大写-强烈情感")

        # 重复字符检测
        import re
        repeated_chars = re.findall(r'(.)\1{2,}', text)  # 连续3个以上相同字符
        if repeated_chars:
            special_patterns_score += len(repeated_chars) * 0.1
            special_details.append(f"重复字符: {''.join(set(repeated_chars))}")

        # 连续标点检测
        repeated_puncts = re.findall(r'([!！?？.。~])\1{1,}', text)
        if repeated_puncts:
            special_patterns_score += len(repeated_puncts) * 0.15
            special_details.append(f"连续标点: {''.join(set(repeated_puncts))}")

        # 网络数字表情检测
        number_emotions = re.findall(r'(666+|233+|555+|888+)', text)
        if number_emotions:
            for num_emotion in number_emotions:
                if num_emotion.startswith('6'):
                    positive_score += 0.4
                    special_details.append(f"数字表情: {num_emotion} (积极)")
                elif num_emotion.startswith('2'):
                    positive_score += 0.3
                    special_details.append(f"数字表情: {num_emotion} (笑声)")
                elif num_emotion.startswith('5'):
                    negative_score += 0.3
                    special_details.append(f"数字表情: {num_emotion} (哭声)")
                elif num_emotion.startswith('8'):
                    positive_score += 0.2
                    special_details.append(f"数字表情: {num_emotion} (发财)")

        # 颜文字检测
        emoticons = re.findall(r'[（(][^)）]*[)）]|[oO][rR][zZ]|[T_][T_]|[>_<]+|[=:;][)(\]D\[pP]+|_\(:з」∠\)_', text)
        if emoticons:
            # 简单的颜文字情感判断
            for emoticon in emoticons:
                if any(char in emoticon for char in ')D]'):
                    positive_score += 0.2
                    special_details.append(f"颜文字: {emoticon} (积极)")
                elif any(char in emoticon for char in '([<'):
                    negative_score += 0.2
                    special_details.append(f"颜文字: {emoticon} (消极)")
                else:
                    special_details.append(f"颜文字: {emoticon} (中性)")

        # 合并特殊模式分析结果
        if special_details:
            analysis_details.extend(special_details)

        # 5. 文本长度影响（长文本情感更强烈）
        length_factor = min(len(text) / 50, 1.0) * 0.2

        # 6. 计算最终分数
        final_positive = min(positive_score + intensity_score + special_patterns_score + length_factor, 1.0)
        final_negative = min(negative_score + intensity_score + special_patterns_score + length_factor, 1.0)

        # 7. 确定情感标签和强度并缓存结果
        result = None
        if final_positive > final_negative:
            if final_positive >= 0.7:
                result = {
                    "label": "positive",
                    "intensity": "high",
                    "score": final_positive,
                    "details": "; ".join(analysis_details) if analysis_details else "积极情感-高强度"
                }
            elif final_positive >= 0.4:
                result = {
                    "label": "positive",
                    "intensity": "medium",
                    "score": final_positive,
                    "details": "; ".join(analysis_details) if analysis_details else "积极情感-中强度"
                }
            else:
                result = {
                    "label": "positive",
                    "intensity": "low",
                    "score": final_positive,
                    "details": "; ".join(analysis_details) if analysis_details else "积极情感-低强度"
                }
        elif final_negative > final_positive:
            if final_negative >= 0.7:
                result = {
                    "label": "negative",
                    "intensity": "high",
                    "score": final_negative,
                    "details": "; ".join(analysis_details) if analysis_details else "消极情感-高强度"
                }
            elif final_negative >= 0.4:
                result = {
                    "label": "negative",
                    "intensity": "medium",
                    "score": final_negative,
                    "details": "; ".join(analysis_details) if analysis_details else "消极情感-中强度"
                }
            else:
                result = {
                    "label": "negative",
                    "intensity": "low",
                    "score": final_negative,
                    "details": "; ".join(analysis_details) if analysis_details else "消极情感-低强度"
                }
        else:
            result = {
                "label": "neutral",
                "intensity": "low",
                "score": max(final_positive, final_negative, 0.1),
                "details": "; ".join(analysis_details) if analysis_details else "中性情感"
            }

        # 缓存结果
        self._cache[cache_key] = result.copy()
        return result

    def _analyze_context(self, text: str) -> dict:
        """
        分析文本上下文，提升情感分析准确性
        """
        context_info = {
            'has_context': False,
            'type': 'normal',
            'modifier': 1.0
        }

        # 检查讽刺语境
        if any(keyword in text for keyword in self.context_keywords['irony']):
            context_info.update({
                'has_context': True,
                'type': 'irony',
                'modifier': -0.5  # 讽刺会反转情感
            })

        # 检查强调语境
        elif any(keyword in text for keyword in self.context_keywords['emphasis']):
            context_info.update({
                'has_context': True,
                'type': 'emphasis',
                'modifier': 1.3  # 强调会增强情感
            })

        # 检查疑问语境
        elif any(keyword in text for keyword in self.context_keywords['question']):
            context_info.update({
                'has_context': True,
                'type': 'question',
                'modifier': 0.8  # 疑问会减弱情感强度
            })

        # 检查否定语境
        elif any(keyword in text for keyword in self.context_keywords['negation']):
            context_info.update({
                'has_context': True,
                'type': 'negation',
                'modifier': -0.3  # 否定会部分反转情感
            })

        return context_info

    def get_emotion_prompt(self, sentiment_result: dict) -> str:
        """
        根据情感分析结果生成对应的回复提示词
        """
        label = sentiment_result.get('label', 'neutral')
        intensity = sentiment_result.get('intensity', 'low')

        if label == 'positive':
            if intensity == 'high':
                return "用户情绪非常积极兴奋，请用超级热情活泼的语气回复，可以使用感叹号和表情"
            elif intensity == 'medium':
                return "用户情绪比较积极，请用热情友好的语气回复"
            else:
                return "用户情绪略显积极，请用温和友善的语气回复"
        elif label == 'negative':
            if intensity == 'high':
                return "用户情绪非常低落或愤怒，请用非常温柔关怀的语气回复，提供强烈的情感支持和安慰"
            elif intensity == 'medium':
                return "用户情绪较为低落，请用温和关怀的语气回复，适当提供情感支持"
            else:
                return "用户情绪略显低落，请用温和理解的语气回复"
        else:
            return "用户情绪平和，请用自然亲切的语气回复"

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self._cache),
            'cache_max_size': self._cache_max_size,
            'cache_usage': f"{len(self._cache)}/{self._cache_max_size} ({len(self._cache)/self._cache_max_size*100:.1f}%)"
        }

    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()

    def batch_analyze(self, texts: list) -> list:
        """批量分析文本情感（优化版）"""
        results = []
        for text in texts:
            result = self.analyze_sentiment_intensity(text)
            results.append(result)
        return results

    def analyze_with_confidence(self, text: str) -> dict:
        """
        带置信度的情感分析
        返回额外的置信度信息
        """
        result = self.analyze_sentiment_intensity(text)

        # 计算置信度
        confidence = self._calculate_confidence(text, result)
        result['confidence'] = confidence
        result['reliability'] = 'high' if confidence > 0.8 else 'medium' if confidence > 0.5 else 'low'

        return result

    def _calculate_confidence(self, text: str, result: dict) -> float:
        """计算分析结果的置信度"""
        confidence = 0.5  # 基础置信度

        # 文本长度影响置信度
        if len(text) > 10:
            confidence += 0.1
        if len(text) > 30:
            confidence += 0.1

        # 情感强度影响置信度
        if result['intensity'] == 'high':
            confidence += 0.2
        elif result['intensity'] == 'medium':
            confidence += 0.1

        # 分析详情数量影响置信度
        details_count = len(result['details'].split(';')) if result['details'] else 0
        confidence += min(details_count * 0.05, 0.2)

        return min(confidence, 1.0)


# 创建全局情感分析器实例
sentiment_analyzer = SentimentAnalyzer()


def has_think_tags(text: str) -> bool:
    """
    检查文本是否包含思考标签
    """
    if not text:
        return False

    return "</think>" in text


def remove_think_tags(text: str) -> str:
    """
    移除思考标签及其内容
    """
    if not text:
        return ""

    try:
        if "</think>" in text:
            # 移除思考标签及其之前的内容
            text = text.split("</think>", 1)[1].strip()
        return text
    except Exception as e:
        log(f"移除思考标签失败: {str(e)}", "ERROR")
        return text
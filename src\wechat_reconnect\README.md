# 微信重连模块

这是一个独立的微信重连监控模块，可以自动检测微信连接状态，在断线时自动重连，并在需要时通过邮件发送二维码。

## 功能特性

- ✅ **自动状态检测**：定期检查微信在线状态
- ✅ **智能重连**：使用现有的微信登录点击器自动重连
- ✅ **二维码邮件**：重连失败时自动获取二维码并发送到邮箱
- ✅ **重试机制**：支持多次重试，避免临时网络问题
- ✅ **独立运行**：可作为独立进程运行，不影响主程序
- ✅ **配置向导**：提供友好的配置向导
- ✅ **多平台支持**：支持Windows、Linux、Mac

## 快速开始

### 1. 配置向导

首次使用建议运行配置向导：

```bash
python src/wechat_reconnect/config_wizard.py
```

配置向导会帮助您：
- 设置检查间隔和重试次数
- 配置邮箱信息（用于发送二维码）
- 测试邮件配置

### 2. 启动监控

#### Windows
```bash
start_wechat_monitor.bat
```

#### Linux/Mac
```bash
chmod +x start_wechat_monitor.sh
./start_wechat_monitor.sh
```

#### 直接运行
```bash
python src/wechat_reconnect/standalone_monitor.py
```

## 配置说明

配置文件位于 `src/config/config.json` 中的 `wechat_reconnect` 部分：

```json
{
  "wechat_reconnect": {
    "title": "微信重连配置",
    "settings": {
      "enable_auto_reconnect": {
        "value": true,
        "description": "启用微信自动重连功能"
      },
      "check_interval": {
        "value": 60,
        "description": "微信状态检查间隔（秒）"
      },
      "max_retry_attempts": {
        "value": 3,
        "description": "最大重连尝试次数"
      },
      "qrcode_retry_interval": {
        "value": 300,
        "description": "二维码重新发送间隔（秒）"
      },
      "email_enabled": {
        "value": false,
        "description": "启用邮件发送二维码功能"
      },
      "smtp_server": {
        "value": "smtp.qq.com",
        "description": "SMTP服务器地址"
      },
      "smtp_port": {
        "value": 587,
        "description": "SMTP端口"
      },
      "sender_email": {
        "value": "",
        "description": "发送方邮箱地址"
      },
      "sender_password": {
        "value": "",
        "description": "发送方邮箱密码/授权码"
      },
      "recipient_email": {
        "value": "",
        "description": "接收方邮箱地址"
      }
    }
  }
}
```

## 邮箱配置

### QQ邮箱（推荐）
1. 登录QQ邮箱
2. 设置 → 账户 → 开启SMTP服务
3. 获取授权码（不是登录密码）
4. 配置：
   - SMTP服务器：smtp.qq.com
   - 端口：587
   - 密码：使用授权码

### Gmail
1. 开启两步验证
2. 生成应用专用密码
3. 配置：
   - SMTP服务器：smtp.gmail.com
   - 端口：587
   - 密码：使用应用专用密码

### 163邮箱
1. 开启SMTP服务
2. 配置：
   - SMTP服务器：smtp.163.com
   - 端口：587

## 使用方法

### 命令行选项

```bash
# 启动监控（默认）
python src/wechat_reconnect/standalone_monitor.py

# 测试邮件配置
python src/wechat_reconnect/standalone_monitor.py --test-email

# 强制重连
python src/wechat_reconnect/standalone_monitor.py --force-reconnect

# 后台运行
python src/wechat_reconnect/standalone_monitor.py --daemon
```

### 启动脚本功能

启动脚本提供了友好的菜单界面：

1. **启动微信重连监控**：前台运行监控
2. **测试邮件配置**：验证邮箱设置是否正确
3. **强制重连微信**：手动触发重连
4. **后台运行监控**（仅Linux/Mac）：以守护进程方式运行
5. **停止后台监控**（仅Linux/Mac）：停止后台进程
6. **查看监控状态**（仅Linux/Mac）：查看运行状态和日志

## 工作流程

1. **状态检测**：每隔指定时间检查微信是否在线
2. **自动重连**：检测到断线时，尝试使用微信登录点击器重连
3. **重试机制**：重连失败时，等待一段时间后重试
4. **二维码处理**：所有重连尝试失败后，获取登录二维码
5. **邮件发送**：将二维码发送到指定邮箱
6. **扫码监控**：监控二维码扫描状态，成功后继续监控

## 日志文件

- 主日志：`logs/wechat_reconnect.log`
- 后台运行日志：`logs/wechat_monitor.log`

## 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查邮箱地址和密码/授权码
   - 确认SMTP服务器和端口设置
   - 检查网络连接

2. **微信状态检测失败**
   - 确保微信已正常登录
   - 检查wxauto库是否正确安装
   - 确认微信版本兼容性

3. **自动重连失败**
   - 检查微信登录点击器是否正常工作
   - 确认微信窗口可见且未被其他程序遮挡

### 调试模式

可以通过查看日志文件来诊断问题：

```bash
# 查看最新日志
tail -f logs/wechat_reconnect.log

# 查看后台运行日志
tail -f logs/wechat_monitor.log
```

## 安全注意事项

1. **邮箱密码安全**：
   - 使用授权码而非登录密码
   - 定期更换授权码
   - 不要在公共场所配置

2. **二维码安全**：
   - 二维码有效期约2分钟
   - 及时扫描，避免泄露
   - 系统会自动清理过期二维码

3. **网络安全**：
   - 确保SMTP连接使用TLS加密
   - 避免在不安全的网络环境下使用

## 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 运行配置向导重新配置
3. 使用测试功能验证各项配置
4. 检查微信和wxauto库的版本兼容性

## 更新日志

### v1.0.0
- 初始版本发布
- 支持自动状态检测和重连
- 支持二维码邮件发送
- 提供配置向导和启动脚本

"""
消息处理模块
负责处理聊天消息，包括:
- 消息队列管理
- 消息分发处理
- API响应处理
- 多媒体消息处理
"""
import logging
import threading
import time
import re
import tempfile
from datetime import datetime
from typing import Dict, List, Optional
from openai import OpenAI
from wxauto import WeChat
# 数据库相关导入已移除
import random
import os
import json
from src.services.ai.llm_service import LLMService
from src.services.ai.network_search_service import NetworkSearchService
from src.config import config, WEBLENS_ENABLED, NETWORK_SEARCH_ENABLED, INTELLIGENT_SEARCH_ENABLED
from modules.memory.memory_service import MemoryService
from modules.memory.content_generator import ContentGenerator
from modules.memory_manager import MemoryManager
from modules.reminder.time_recognition import TimeRecognitionService
from modules.reminder import ReminderService
from .debug import DebugCommandHandler
# 新增：电话调度与天气服务
from src.voice.call_scheduler import CallScheduler
from src.services.weather import WeatherService
from src.weather.outdoor_reminder import OutdoorWeatherReminder

# 导入emoji库用于处理表情符号
import emoji

# 导入括号过滤功能
from src.utils.brackets_filter import brackets_filter, filter_brackets

# 修改logger获取方式，确保与main模块一致
logger = logging.getLogger('main')

class MessageHandler:
    def __init__(self, root_dir, api_key, base_url, model, max_token, temperature,
                 max_groups, robot_name, prompt_content, image_handler, emoji_handler, voice_handler, memory_service, wx_instance, content_generator=None, image_recognition_service=None):
        self.root_dir = root_dir
        self.api_key = api_key
        self.model = model
        self.max_token = max_token
        self.temperature = temperature
        self.max_groups = max_groups
        self.robot_name = robot_name
        self.prompt_content = prompt_content

        # 不再需要对话计数器，改为按时间总结

        # 使用 DeepSeekAI 替换直接的 OpenAI 客户端
        self.deepseek = LLMService(
            api_key=api_key,
            base_url=base_url,
            model=model,
            max_token=max_token,
            temperature=temperature,
            max_groups=max_groups
        )

        # 消息队列相关
        self.message_queues = {}  # 存储每个用户的消息队列，格式：{queue_key: queue_data}
        self.queue_timers = {}    # 存储每个用户的定时器，格式：{queue_key: timer}
        # 从全局导入的config中获取队列等待时间（秒）
        from src.config import config
        self.QUEUE_TIMEOUT = config.behavior.message_queue.timeout
        self.queue_lock = threading.Lock()
        self.chat_contexts = {}

        # 微信实例
        self.wx = wx_instance

        # 添加 handlers
        self.image_handler = image_handler
        self.emoji_handler = emoji_handler
        self.voice_handler = voice_handler
        self.image_recognition_service = image_recognition_service
        # 使用记忆管理器（兼容旧接口）
        if isinstance(memory_service, MemoryManager):
            self.memory_manager = memory_service
            self.memory_service = memory_service  # 保持兼容性
        else:
            # 如果传入的是旧的记忆服务，创建记忆管理器
            self.memory_manager = MemoryManager(
                root_dir=root_dir,
                api_key=api_key,
                base_url=base_url,
                model=model,
                max_token=max_token,
                temperature=temperature,
                max_groups=max_groups
            )
            self.memory_service = memory_service
        # 保存当前角色名
        avatar_path = os.path.join(self.root_dir, config.behavior.context.avatar_dir)
        self.current_avatar = os.path.basename(avatar_path)
        
        # 获取当前使用的人设文件名
        avatar_file_name = getattr(config.behavior.context, 'avatar_file', 'avatar.md')
        
        # 构建完整的人设文件路径
        avatar_file_path = os.path.join(avatar_path, avatar_file_name)
        
        # 从人设文件中提取真实名字
        self.avatar_real_names = self._extract_avatar_names(avatar_path)
        logger.info(f"当前使用角色: {self.current_avatar}, 人设文件: {avatar_file_name}, 识别名字: {self.avatar_real_names}")
        
        # 如果指定的人设文件不存在，使用默认的 avatar.md
        if not os.path.exists(avatar_file_path):
            logger.warning(f"指定的人设文件不存在: {avatar_file_path}，使用默认 avatar.md")
            avatar_file_path = os.path.join(avatar_path, "avatar.md")
            if not os.path.exists(avatar_file_path):
                logger.error(f"默认人设文件也不存在: {avatar_file_path}")
                return
        
        # 加载人设内容
        try:
            with open(avatar_file_path, "r", encoding="utf-8") as file:
                self.prompt_content = file.read()
            logger.info(f"已加载人设文件: {avatar_file_path}")
        except Exception as e:
            logger.error(f"加载人设文件失败: {str(e)}")
            return

        # 使用传入的内容生成器实例，或创建新实例
        self.content_generator = content_generator

        # 如果没有提供内容生成器，尝试创建新实例
        if self.content_generator is None:
            try:
                from modules.memory.content_generator import ContentGenerator
                from src.config import config
                self.content_generator = ContentGenerator(
                    root_dir=root_dir,
                    api_key=config.llm.api_key,
                    base_url=config.llm.base_url,
                    model=config.llm.model,
                    max_token=config.llm.max_tokens,
                    temperature=config.llm.temperature
                )
                logger.info("已创建内容生成器实例")
            except Exception as e:
                logger.error(f"创建内容生成器实例失败: {str(e)}")
                self.content_generator = None

        # 初始化调试命令处理器
        self.debug_handler = DebugCommandHandler(
            root_dir=root_dir,
            memory_service=memory_service,
            llm_service=self.deepseek,
            content_generator=self.content_generator
        )

        # 需要保留原始格式的命令列表
        # 包含 None 以处理网页内容提取等非命令的特殊情况
        self.preserve_format_commands = [None, '/diary', '/state', '/letter', '/list', '/pyq', '/gift', '/shopping']
        logger.info("调试命令处理器已初始化")

        # 初始化时间识别服务（使用已有的 deepseek 实例）
        self.time_recognition = TimeRecognitionService(self.deepseek)
        logger.info("时间识别服务已初始化")

        # 初始化提醒服务（传入自身实例）
        self.reminder_service = ReminderService(self)
        logger.info("提醒服务已初始化")

        # 初始化网络搜索服务
        self.network_search_service = NetworkSearchService(self.deepseek)
        logger.info("网络搜索服务已初始化")

        # 初始化天气服务
        self.weather_service = WeatherService(self.network_search_service, config.weather.default_location)
        logger.info("天气服务已初始化")

        # 外出天气提醒
        self.outdoor_reminder = OutdoorWeatherReminder(self.weather_service)
        logger.info("外出天气提醒模块已初始化")

        # 初始化语音拨号调度器
        self.call_scheduler = CallScheduler(wx_instance)
        logger.info("语音拨号调度器已初始化")

        # 数据库相关初始化已移除
        logger.info("消息处理器初始化完成")

    def switch_avatar_temporarily(self, avatar_path: str):
        """临时切换人设（不修改全局配置，仅用于群聊）"""
        try:
            # 重新加载人设文件
            full_avatar_path = os.path.join(self.root_dir, avatar_path)
            
            # 获取当前使用的人设文件名
            from src.config import config
            avatar_file_name = getattr(config.behavior.context, 'avatar_file', 'avatar.md')
            prompt_path = os.path.join(full_avatar_path, avatar_file_name)
            
            # 如果指定的人设文件不存在，使用默认的 avatar.md
            if not os.path.exists(prompt_path):
                logger.warning(f"指定的人设文件不存在: {prompt_path}，使用默认 avatar.md")
                prompt_path = os.path.join(full_avatar_path, "avatar.md")
            
            if os.path.exists(prompt_path):
                with open(prompt_path, "r", encoding="utf-8") as file:
                    self.prompt_content = file.read()
                
                # 更新当前人设名
                self.current_avatar = os.path.basename(full_avatar_path)
                
                # 重新提取人设名字
                self.avatar_real_names = self._extract_avatar_names(full_avatar_path)
                
                logger.info(f"临时切换人设到: {self.current_avatar}, 人设文件: {avatar_file_name}, 识别名字: {self.avatar_real_names}")
            else:
                logger.error(f"人设文件不存在: {prompt_path}")
                
        except Exception as e:
            logger.error(f"临时切换人设失败: {str(e)}")

    def restore_default_avatar(self):
        """恢复到默认人设"""
        try:
            from src.config import config
            default_avatar_path = config.behavior.context.avatar_dir
            
            # 重新加载默认人设文件
            full_avatar_path = os.path.join(self.root_dir, default_avatar_path)
            
            # 恢复到默认的 avatar.md
            prompt_path = os.path.join(full_avatar_path, "avatar.md")
            
            if os.path.exists(prompt_path):
                with open(prompt_path, "r", encoding="utf-8") as file:
                    self.prompt_content = file.read()
                
                # 更新当前人设名
                self.current_avatar = os.path.basename(full_avatar_path)
                
                # 重新提取人设名字
                self.avatar_real_names = self._extract_avatar_names(full_avatar_path)
                
                # 重置配置中的文件名为默认值
                config.behavior.context.avatar_file = "avatar.md"
                
                logger.info(f"恢复到默认人设: {self.current_avatar}, 人设文件: avatar.md, 识别名字: {self.avatar_real_names}")
            else:
                logger.error(f"默认人设文件不存在: {prompt_path}")
                
        except Exception as e:
            logger.error(f"恢复默认人设失败: {str(e)}")

    def switch_avatar(self, avatar_path: str):
        """切换人设"""
        try:
            from src.config import config
            
            # 更新当前人设路径
            config.behavior.context.avatar_dir = avatar_path
            
            # 重新加载人设文件
            full_avatar_path = os.path.join(self.root_dir, avatar_path)
            
            # 获取当前使用的人设文件名
            avatar_file_name = getattr(config.behavior.context, 'avatar_file', 'avatar.md')
            prompt_path = os.path.join(full_avatar_path, avatar_file_name)
            
            # 如果指定的人设文件不存在，使用默认的 avatar.md
            if not os.path.exists(prompt_path):
                logger.warning(f"指定的人设文件不存在: {prompt_path}，使用默认 avatar.md")
                prompt_path = os.path.join(full_avatar_path, "avatar.md")
                config.behavior.context.avatar_file = "avatar.md"
            
            if os.path.exists(prompt_path):
                with open(prompt_path, "r", encoding="utf-8") as file:
                    self.prompt_content = file.read()
                
                # 更新当前人设名
                self.current_avatar = os.path.basename(full_avatar_path)
                
                # 重新提取人设名字
                self.avatar_real_names = self._extract_avatar_names(full_avatar_path)
                
                logger.info(f"成功切换人设到: {self.current_avatar}, 人设文件: {avatar_file_name}, 识别名字: {self.avatar_real_names}")
            else:
                logger.error(f"人设文件不存在: {prompt_path}")
                
        except Exception as e:
            logger.error(f"切换人设失败: {str(e)}")

    def _extract_avatar_names(self, avatar_path: str) -> list:
        """从人设文件中提取可能的名字"""
        names = []  # 不包含目录名，避免ATRI这样的英文名干扰
        
        try:
            avatar_file = os.path.join(avatar_path, "avatar.md")
            if os.path.exists(avatar_file):
                with open(avatar_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 使用正则表达式提取可能的名字
                import re
                
                # 提取"你是xxx"模式的名字（最重要的模式）
                matches = re.findall(r'你是([^，,。！!？?\s]+)', content)
                for match in matches:
                    # 过滤掉明显不是名字的词
                    if match not in names and len(match) <= 6 and '机器' not in match:
                        names.append(match)
                
                # 提取"名字[：:]\s*xxx"模式的名字
                matches = re.findall(r'名字[：:]\s*([^，,。！!？?\s\n]+)', content)
                for match in matches:
                    if match not in names and len(match) <= 6:
                        names.append(match)
                        
                # 提取"扮演xxx"模式的名字
                matches = re.findall(r'扮演([^，,。！!？?\s]+)', content)
                for match in matches:
                    # 只要中文名字，过滤掉长词
                    if match not in names and len(match) <= 6 and any('\u4e00' <= c <= '\u9fff' for c in match):
                        names.append(match)
                        
        except Exception as e:
            logger.warning(f"提取人设名字失败: {str(e)}")
            
        # 如果没有提取到任何名字，使用目录名作为备选
        if not names:
            names = [self.current_avatar]
            
        return names

    def _get_queue_key(self, chat_id: str, sender_name: str, is_group: bool) -> str:
        """生成队列键值
        在群聊中使用 chat_id + sender_name 作为键，在私聊中仅使用 chat_id"""
        return f"{chat_id}_{sender_name}" if is_group else chat_id

    def _get_user_relationship_info(self, sender_name: str) -> str:
        """获取用户关系信息，用于群聊环境判断"""
        try:
            avatar_name = self.current_avatar
            
            # 检查是否有该用户的私聊记忆
            has_private_memory = self.memory_service.has_user_memory(avatar_name, sender_name)
            
            # 检查特殊关系设定（从核心记忆中查找）
            special_relationship = self._get_special_relationship(avatar_name, sender_name)
            
            if has_private_memory:
                base_info = f"发送者 {sender_name} 与你有私聊记忆。"
                if special_relationship:
                    return f"## 当前发送者关系状态：\n{base_info} 特殊关系：{special_relationship}。"
                else:
                    return f"## 当前发送者关系状态：\n{base_info}"
            else:
                base_info = f"发送者 {sender_name} 没有私聊记忆。"
                if special_relationship:
                    return f"## 当前发送者关系状态：\n{base_info} 特殊关系：{special_relationship}。"
                else:
                    return f"## 当前发送者关系状态：\n{base_info}"
                
        except Exception as e:
            logger.error(f"获取用户关系信息失败: {str(e)}")
            return f"## 当前发送者关系状态：\n发送者 {sender_name} 关系状态未知，请保持礼貌友好的态度。"

    def _get_special_relationship(self, avatar_name: str, user_name: str) -> str:
        """从核心记忆中查找特殊关系设定"""
        try:
            # 获取所有用户的核心记忆，查找关于特定用户的关系设定
            avatars_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "memory")
            if not os.path.exists(avatars_dir):
                return ""
            
            # 遍历所有用户的记忆文件
            for user_dir in os.listdir(avatars_dir):
                core_memory_path = os.path.join(avatars_dir, user_dir, "core_memory.json")
                if os.path.exists(core_memory_path):
                    try:
                        with open(core_memory_path, "r", encoding="utf-8") as f:
                            core_memory = json.load(f)
                            content = core_memory.get("content", "")
                            
                            # 查找关于特定用户的关系描述
                            if user_name in content:
                                # 简单的关键词匹配
                                relationship_keywords = {
                                    "朋友": f"{user_name}是朋友",
                                    "敌人": f"{user_name}是敌人", 
                                    "兄弟": f"{user_name}是兄弟",
                                    "姐妹": f"{user_name}是姐妹",
                                    "同事": f"{user_name}是同事",
                                    "老师": f"{user_name}是老师",
                                    "学生": f"{user_name}是学生"
                                }
                                
                                for keyword, description in relationship_keywords.items():
                                    if keyword in content and user_name in content:
                                        return description
                    except Exception as e:
                        logger.debug(f"读取核心记忆文件失败: {str(e)}")
                        continue
            
            return ""
            
        except Exception as e:
            logger.error(f"查找特殊关系失败: {str(e)}")
            return ""

    def save_message(self, sender_id: str, sender_name: str, message: str, reply: str, is_system_message: bool = False):
        """保存聊天记录到记忆系统（使用记忆管理器）"""
        try:
            # 清理回复中的@前缀，防止幻觉
            clean_reply = reply
            if reply.startswith(f"@{sender_name} "):
                clean_reply = reply[len(f"@{sender_name} "):]

            avatar_name = self.current_avatar
            # 使用记忆管理器添加对话
            self.memory_manager.add_conversation(avatar_name, message, clean_reply, sender_id, is_system_message)

        except Exception as e:
            logger.error(f"保存消息失败: {str(e)}")

    def get_api_response(self, message: str, user_id: str, is_group: bool = False) -> str:
        """获取 API 回复 - 使用智能记忆查询系统"""
        # 使用类中已初始化的当前角色名
        avatar_name = self.current_avatar

        try:
            # 使用已加载的人设内容（支持临时切换）
            avatar_content = self.prompt_content
            logger.debug(f"角色提示文件大小: {len(avatar_content)} bytes")

            # === 新的智能记忆查询系统 ===
            # 步骤1：智能判断是否需要查询记忆
            memory_decision = self.memory_manager.should_query_memory_intelligently(message, user_id, avatar_name)
            logger.debug(f"智能记忆判断: {memory_decision}")
            
            # 步骤2：获取智能记忆工具
            intelligent_tools = self.memory_manager.get_intelligent_memory_tools()
            tools_config = None
            tool_executor = None
            
            # 如果需要查询记忆，配置工具
            if memory_decision.get('should_query', False):
                tools_config = intelligent_tools.get_tools_definitions()
                tool_executor = intelligent_tools
                logger.info(f"启用智能记忆查询，原因: {memory_decision.get('reason')}")
            else:
                logger.debug(f"无需查询记忆，原因: {memory_decision.get('reason')}")
            
            # 步骤3：始终获取核心记忆（保持角色一致性）
            core_memory = self.memory_manager.get_core_memory(avatar_name, user_id)
            core_memory_prompt = f"# 核心记忆\n{core_memory}" if core_memory else ""
            logger.debug(f"核心记忆长度: {len(core_memory)}")

            # 步骤4：获取相关剧情上下文（如果启用）
            story_context = self.memory_manager.get_story_context(avatar_name, message)
            if story_context:
                logger.debug(f"获取到剧情上下文，长度: {len(story_context)}")

            # 步骤5：仅在程序重启时获取历史上下文（避免与LLM内置上下文重复）
            recent_context = None
            if user_id not in self.deepseek.chat_contexts:
                # 程序重启时，获取少量历史上下文用于初始化
                recent_context = self.memory_manager.get_recent_context(avatar_name, user_id, context_size=6)
                if recent_context:
                    logger.debug(f"程序重启恢复：为用户 {user_id} 加载 {len(recent_context)} 条历史上下文")
                else:
                    logger.debug(f"程序重启：用户 {user_id} 无历史上下文可恢复")
            else:
                logger.debug(f"用户 {user_id} 已有LLM上下文，跳过历史上下文加载")



            # 如果是群聊场景，添加群聊环境提示
            if is_group:
                group_prompt_path = os.path.join(self.root_dir, "data", "base", "group.md")
                with open(group_prompt_path, "r", encoding="utf-8") as f:
                    group_chat_prompt = f.read().strip()

                # 检查当前发送者是否有私聊记忆来判断关系
                relationship_info = self._get_user_relationship_info(user_id)

                combined_system_prompt = f"{group_chat_prompt}\n\n{relationship_info}\n\n{avatar_content}"
            else:
                combined_system_prompt = avatar_content

            # 添加剧情上下文到系统提示词
            if story_context:
                combined_system_prompt = f"{combined_system_prompt}{story_context}"

            # 注意：移除了自动记忆回忆，现在由AI智能决定是否需要查询记忆



            # 获取系统提示词（如果有）
            if hasattr(self, 'system_prompts') and user_id in self.system_prompts and self.system_prompts[user_id]:
                # 将最近的系统提示词合并为一个字符串
                additional_prompt = "\n\n".join(self.system_prompts[user_id])
                logger.info(f"使用系统提示词: {additional_prompt[:100]}...")

                # 将系统提示词添加到角色提示词中
                combined_system_prompt = f"{combined_system_prompt}\n\n参考信息:\n{additional_prompt}"

                # 使用后清除系统提示词，避免重复使用
                self.system_prompts[user_id] = []


            # 检查消息中是否包含图像
            if "[IMAGE:" in message and "]" in message:
                # 提取图像路径和文字内容
                image_path, text_content = self._extract_image_and_text(message)
                if image_path and os.path.exists(image_path):
                    # 使用多模态LLM
                    response = self._call_multimodal_llm(
                        text_content=text_content,
                        image_path=image_path,
                        user_id=user_id,
                        system_prompt=combined_system_prompt,
                        previous_context=recent_context,
                        core_memory=core_memory_prompt
                    )
                    return response

            # 普通文字消息 - 使用智能记忆查询
            response = self.deepseek.get_response(
                message=message,
                user_id=user_id,
                system_prompt=combined_system_prompt,
                previous_context=recent_context,
                core_memory=core_memory_prompt,
                tools=tools_config,
                tool_executor=tool_executor
            )
            
            # 检查响应是否包含智能记忆错误信息
            if isinstance(response, dict) and "memory_error" in response:
                # 返回格式：{"content": "正常回复", "memory_error": "错误信息"}
                logger.warning(f"智能记忆工具调用失败，但正常聊天已完成: {response['memory_error']}")
                
                # 将正常回复和错误信息一起返回
                normal_content = response["content"]
                error_info = response["memory_error"]
                
                # 在正常回复后面附加错误信息
                return f"{normal_content}\n\nError: {error_info}"
            
            return response

        except ValueError as ve:
            # 智能记忆工具调用失败的特定处理
            if "智能记忆工具调用失败" in str(ve):
                logger.warning(f"智能记忆工具调用失败，降级到无记忆模式: {str(ve)}")
                try:
                    # 降级处理：不使用智能记忆工具，但保留核心记忆和基础上下文
                    return self.deepseek.get_response(
                        message=message,
                        user_id=user_id,
                        system_prompt=combined_system_prompt,
                        previous_context=recent_context,
                        core_memory=core_memory_prompt,
                        tools=None,
                        tool_executor=None
                    )
                except Exception as fallback_error:
                    logger.error(f"无记忆模式也失败，使用最基础的回复: {str(fallback_error)}")
                    return self.deepseek.get_response(message, user_id, self.prompt_content)
            else:
                # 其他 ValueError，直接使用最基础的回复
                logger.error(f"API响应发生其他错误: {str(ve)}")
                return self.deepseek.get_response(message, user_id, self.prompt_content)
        except Exception as e:
            logger.error(f"获取API响应失败: {str(e)}")
            # 降级处理：使用原始提示，不添加记忆
            return self.deepseek.get_response(message, user_id, self.prompt_content)

    def _extract_image_and_text(self, message: str) -> tuple:
        """从消息中提取图像路径和文字内容"""
        import re

        # 查找图像标记
        image_pattern = r'\[IMAGE:([^\]]+)\]'
        match = re.search(image_pattern, message)

        if match:
            image_path = match.group(1)
            # 移除图像标记，获取纯文字内容
            text_content = re.sub(image_pattern, '', message).strip()
            return image_path, text_content

        return None, message

    def _call_multimodal_llm(self, text_content: str, image_path: str, user_id: str,
                           system_prompt: str, previous_context=None, core_memory=None) -> str:
        """调用支持多模态的LLM"""
        import time
        max_retries = 3
        retry_delay = 2  # 秒

        for attempt in range(max_retries):
            try:
                import base64
                import requests
                from src.config import config

                # 读取图像文件并转换为base64
                with open(image_path, 'rb') as image_file:
                    image_data = image_file.read()
                    image_base64 = base64.b64encode(image_data).decode('utf-8')

                # 构建多模态消息
                multimodal_message = []

                # 添加图像
                multimodal_message.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_base64}"
                    }
                })

                # 构建专门的图片识别提示词（基于你提供的模板）
                image_prompt = ""
                if text_content:
                    # 如果有文字内容，说明是文字+图片的组合消息
                    image_prompt = f"{text_content}\n\n这是一张微信聊天的图片截图，请根据你的角色人设回应用户的消息和这个聊天窗口左边的聊天用户发送的最后一张表情，不要去识别聊天用户的头像"
                else:
                    # 纯图片消息，使用你提供的提示词
                    image_prompt = "这是一张微信聊天的图片截图，请根据你的角色人设回应用户的消息和这个聊天窗口左边的聊天用户发送的最后一张表情，不要去识别聊天用户的头像"

                # 添加图片识别提示词
                multimodal_message.append({
                    "type": "text",
                    "text": image_prompt
                })

                # 构建完整的消息列表
                messages = []

                # 添加系统提示词
                if system_prompt:
                    messages.append({
                        "role": "system",
                        "content": system_prompt
                    })

                # 添加历史上下文
                if previous_context:
                    messages.extend(previous_context)

                # 添加当前消息
                messages.append({
                    "role": "user",
                    "content": multimodal_message
                })

                # 发送API请求
                headers = {
                    "Authorization": f"Bearer {config.llm.api_key}",
                    "Content-Type": "application/json"
                }

                data = {
                    "model": config.llm.model,
                    "messages": messages,
                    "max_tokens": config.llm.max_tokens,
                    "temperature": config.llm.temperature
                }

                response = requests.post(
                    f"{config.llm.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=30
                )

                if response.status_code == 200:
                    result_data = response.json()
                    content = result_data['choices'][0]['message']['content']
                    logger.info(f"AI回复: {content}")
                    return content
                elif response.status_code == 503 and attempt < max_retries - 1:
                    # 503错误（服务过载）且还有重试机会，等待后重试
                    logger.warning(f"多模态LLM服务过载 (503)，{retry_delay}秒后进行第{attempt + 2}次尝试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    logger.error(f"多模态LLM API请求失败: {response.status_code} - {response.text}")
                    raise Exception(f"API请求失败: {response.status_code}")

            except Exception as e:
                if attempt < max_retries - 1 and "503" in str(e):
                    logger.warning(f"多模态LLM请求异常，{retry_delay}秒后重试: {str(e)}")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    logger.error(f"多模态LLM处理失败: {str(e)}")
                    break
            # 降级到Moonshot图片识别服务
            if self.image_recognition_service:
                logger.info("降级到Moonshot图片识别服务")
                try:
                    # 判断是否为表情包（通过文字内容或图片路径判断）
                    is_emoji = (text_content and "表情包" in text_content) or "screenshot" in image_path.lower()
                    recognized_text = self.image_recognition_service.recognize_image(image_path, is_emoji)

                    # 如果有文字内容，将识别结果与文字内容结合
                    if text_content:
                        combined_message = f"{text_content} {recognized_text}"
                    else:
                        combined_message = recognized_text

                    # 使用普通LLM处理组合后的消息
                    return self.deepseek.get_response(combined_message, user_id, system_prompt)
                except Exception as fallback_error:
                    logger.error(f"Moonshot图片识别降级也失败: {str(fallback_error)}")

            # 最终降级到纯文字处理
            fallback_message = text_content if text_content else "用户发送了一张图片"
            return self.deepseek.get_response(fallback_message, user_id, system_prompt)

    def handle_user_message(self, content: str, chat_id: str, sender_name: str,
                     username: str, is_group: bool = False, is_image_recognition: bool = False):
        """统一的消息处理入口"""
        try:
            logger.info(f"收到消息 - 来自: {sender_name}" + (" (群聊)" if is_group else ""))
            logger.debug(f"消息内容: {content}")

            # 应用括号过滤到用户输入
            filtered_content = filter_brackets(content, "user_input")
            if filtered_content != content:
                logger.info(f"用户输入已应用括号过滤: 移除了括号内容")
                content = filtered_content

            # 处理调试命令
            if self.debug_handler.is_debug_command(content):
                logger.info(f"检测到调试命令: {content}")
                # 定义回调函数，用于异步处理生成的内容
                def command_callback(command, reply, chat_id):
                    try:
                        if is_group:
                            # 检查回复是否已经包含@用户名，避免重复添加
                            if not reply.startswith(f"@{sender_name} "):
                                reply = f"@{sender_name} {reply}"

                        # 使用命令响应发送方法
                        self._send_command_response(command, reply, chat_id)
                        logger.info(f"异步处理命令完成: {command}")
                    except Exception as e:
                        logger.error(f"异步处理命令失败: {str(e)}")

                intercept, response = self.debug_handler.process_command(
                    command=content,
                    current_avatar=self.current_avatar,
                    user_id=chat_id,
                    chat_id=chat_id,
                    callback=command_callback
                )

                if intercept:
                    # 只有当有响应时才发送（异步生成内容的命令不会有初始响应）
                    if response:
                        if is_group:
                            # 检查回复是否已经包含@用户名，避免重复添加
                            if not response.startswith(f"@{sender_name} "):
                                response = f"@{sender_name} {response}"
                        # self.wx.SendMsg(msg=response, who=chat_id)
                        self._send_raw_message(response, chat_id)

                    # 不记录调试命令的对话
                    logger.info(f"已处理调试命令: {content}")
                    return None

            # 处理人设切换命令
            if self.debug_handler.is_avatar_command(content):
                logger.info(f"检测到人设切换命令: {content}")
                
                intercept, response = self.debug_handler.process_avatar_command(
                    command=content,
                    current_avatar=self.current_avatar,
                    user_id=chat_id
                )
                
                if intercept:
                    if response:
                        if is_group:
                            # 检查回复是否已经包含@用户名，避免重复添加
                            if not response.startswith(f"@{sender_name} "):
                                response = f"@{sender_name} {response}"
                        self._send_raw_message(response, chat_id)
                    
                    # 不记录人设切换命令的对话
                    logger.info(f"已处理人设切换命令: {content}")
                    return None

            # 无论消息中是否包含链接，都将消息添加到队列
            # 如果有链接，在队列处理过程中提取内容并替换
            self._add_to_message_queue(content, chat_id, sender_name, username, is_group, is_image_recognition)

        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}", exc_info=True)
            return None

    def _add_to_message_queue(self, content: str, chat_id: str, sender_name: str,
                            username: str, is_group: bool, is_image_recognition: bool):
        """添加消息到队列并设置定时器"""
        # 检测消息中是否包含链接，但不立即处理
        # 排除图像路径标记，避免误判
        has_link = False
        if WEBLENS_ENABLED and not content.startswith("[IMAGE:"):
            urls = self.network_search_service.detect_urls(content)
            if urls:
                has_link = True
                logger.info(f"[消息队列] 检测到链接: {urls[0]}，将在队列处理时提取内容")

        # 检查是否是表情包消息
        is_emoji_message = "用户发送了一张表情包" in content or "[IMAGE:" in content

        with self.queue_lock:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            queue_key = self._get_queue_key(chat_id, sender_name, is_group)

            # 初始化或更新队列
            if queue_key not in self.message_queues:
                logger.info(f"[消息队列] 创建新队列 - 用户: {sender_name}" + (" (群聊)" if is_group else ""))
                self.message_queues[queue_key] = {
                    'messages': [f"[{current_time}]\n{content}"],  # 第一条消息带时间戳
                    'chat_id': chat_id,  # 保存原始chat_id用于发送消息
                    'sender_name': sender_name,
                    'username': username,
                    'is_group': is_group,
                    'is_image_recognition': is_image_recognition,
                    'last_update': time.time(),
                    'has_link': has_link,  # 标记消息中是否包含链接
                    'urls': urls if has_link else [],  # 如果有链接，保存URL列表
                    'has_emoji': is_emoji_message,  # 标记是否包含表情包
                    'message_types': ['emoji' if is_emoji_message else 'text']  # 记录消息类型序列
                }
                logger.debug(f"[消息队列] 首条消息: {content[:50]}...")
            else:
                # 添加新消息到现有队列，后续消息不带时间戳
                self.message_queues[queue_key]['messages'].append(content)
                self.message_queues[queue_key]['last_update'] = time.time()
                self.message_queues[queue_key]['has_link'] = (has_link | self.message_queues[queue_key]['has_link'])
                self.message_queues[queue_key]['has_emoji'] = (is_emoji_message | self.message_queues[queue_key].get('has_emoji', False))
                # 记录消息类型序列
                self.message_queues[queue_key]['message_types'].append('emoji' if is_emoji_message else 'text')
                if has_link:
                    self.message_queues[queue_key]['urls'].append(urls[0])
                msg_count = len(self.message_queues[queue_key]['messages'])
                logger.info(f"[消息队列] 追加消息 - 用户: {sender_name}, 当前消息数: {msg_count}")
                logger.debug(f"[消息队列] 新增消息: {content[:50]}...")

            # 取消现有的定时器
            if queue_key in self.queue_timers and self.queue_timers[queue_key]:
                try:
                    self.queue_timers[queue_key].cancel()
                    logger.debug(f"[消息队列] 重置定时器 - 用户: {sender_name}")
                except Exception as e:
                    logger.error(f"[消息队列] 取消定时器失败: {str(e)}")
                self.queue_timers[queue_key] = None

            # 智能决定等待时间
            timeout = self._calculate_smart_timeout(queue_key, is_emoji_message)

            # 创建新的定时器
            timer = threading.Timer(
                timeout,
                self._process_message_queue,
                args=[queue_key]
            )
            timer.daemon = True
            timer.start()
            self.queue_timers[queue_key] = timer
            logger.info(f"[消息队列] 设置新定时器 - 用户: {sender_name}, {timeout}秒后处理")

    def _smart_merge_messages(self, messages: list) -> str:
        """智能合并消息，优化图片和文字的组合处理"""
        if not messages:
            return ""

        # 如果只有一条消息，直接返回
        if len(messages) == 1:
            return messages[0]

        # 分离文字消息和图片消息
        text_parts = []
        image_parts = []

        for msg in messages:
            if "[IMAGE:" in msg and "]" in msg:
                # 提取图片路径和可能的文字内容
                import re
                image_pattern = r'\[IMAGE:([^\]]+)\]'
                matches = re.findall(image_pattern, msg)
                if matches:
                    image_parts.extend(matches)
                # 移除图片标记，保留文字部分
                text_part = re.sub(image_pattern, '', msg).strip()
                if text_part:
                    text_parts.append(text_part)
            else:
                text_parts.append(msg)

        # 合并文字部分
        combined_text = "\n".join(text_parts).strip()

        # 如果有图片，将图片路径添加到文字后面
        if image_parts:
            # 只保留最后一张图片（通常是最相关的）
            last_image = image_parts[-1]
            if combined_text:
                return f"{combined_text}[IMAGE:{last_image}]"
            else:
                return f"[IMAGE:{last_image}]"

        return combined_text

    def _calculate_smart_timeout(self, queue_key: str, is_emoji_message: bool) -> float:
        """智能计算队列超时时间，优化文字+表情包的合并"""
        if queue_key not in self.message_queues:
            return self.QUEUE_TIMEOUT

        queue_data = self.message_queues[queue_key]
        message_types = queue_data.get('message_types', [])

        # 如果当前是表情包消息
        if is_emoji_message:
            # 如果前面有文字消息，快速处理（文字+表情包组合）
            if message_types and message_types[-1] == 'text':
                logger.debug("[消息队列] 检测到文字+表情包组合，缩短等待时间")
                return 2.0  # 2秒快速处理
            else:
                # 纯表情包消息，正常等待
                return self.QUEUE_TIMEOUT
        else:
            # 如果当前是文字消息，使用配置的标准等待时间
            # 不再延长等待时间，避免用户感觉响应慢
            logger.debug("[消息队列] 文字消息，使用标准等待时间")
            return self.QUEUE_TIMEOUT

    def _process_message_queue(self, queue_key: str):
        """处理消息队列"""
        avatar_name = self.current_avatar
        try:
            with self.queue_lock:
                if queue_key not in self.message_queues:
                    logger.debug("[消息队列] 队列不存在，跳过处理")
                    return

                # 检查是否到达处理时间
                current_time = time.time()
                queue_data = self.message_queues[queue_key]
                last_update = queue_data['last_update']
                sender_name = queue_data['sender_name']

                if current_time - last_update < self.QUEUE_TIMEOUT - 0.1:
                    logger.info(f"[消息队列] 等待更多消息 - 用户: {sender_name}, 剩余时间: {self.QUEUE_TIMEOUT - (current_time - last_update):.1f}秒")
                    return

                # 获取并清理队列数据
                queue_data = self.message_queues.pop(queue_key)
                if queue_key in self.queue_timers:
                    self.queue_timers.pop(queue_key)

                messages = queue_data['messages']
                chat_id = queue_data['chat_id']  # 使用保存的原始chat_id
                username = queue_data['username']
                sender_name = queue_data['sender_name']
                is_group = queue_data['is_group']
                is_image_recognition = queue_data['is_image_recognition']

                # 智能合并消息，优化图片和文字的组合
                combined_message = self._smart_merge_messages(messages)

                # 打印日志信息
                logger.info(f"[消息队列] 开始处理 - 用户: {sender_name}, 消息数: {len(messages)}")
                logger.info("----------------------------------------")
                logger.info("原始消息列表:")
                for idx, msg in enumerate(messages, 1):
                    logger.info(f"{idx}. {msg}")
                logger.info("\n合并后的消息:")
                logger.info(f"{combined_message}")
                logger.info("----------------------------------------")

                # 处理队列中的链接
                processed_message = combined_message
                if queue_data.get('has_link', False) and WEBLENS_ENABLED:
                    urls = queue_data.get('urls', [])
                    if urls:
                        logger.info(f"处理队列中的链接: {urls[0]}")
                        # 提取网页内容
                        web_results = self.network_search_service.extract_web_content(urls[0])
                        if web_results and web_results['original']:
                            # 将网页内容添加到消息中
                            processed_message = f"{combined_message}\n\n{web_results['original']}"
                            logger.info("已获取URL内容并添加至本次Prompt中")    
                            logger.info(processed_message)

                # 检查合并后的消息是否包含时间提醒和联网搜索需求 - 已注释
                # 如果已处理搜索需求，则不需要继续处理消息
                # search_handled = self._check_time_reminder_and_search(processed_message, sender_name)
                # if search_handled:
                #     logger.info(f"搜索需求已处理，直接回复")
                #     return self._handle_text_message(processed_message, chat_id, sender_name, username, is_group)

                # 在处理消息前，如果启用了联网搜索，先检查是否需要联网搜索
                search_results = None

                # 注释掉联网搜索和时间识别功能
                # if NETWORK_SEARCH_ENABLED:
                #     # 使用时间识别服务检测搜索意图
                #     search_intent = self.time_recognition.recognize_time_and_search(
                #         message=combined_message,
                #         user_name=sender_name,
                #         avatar_name=self.current_avatar,
                #         network_search_enabled=True
                #     )

                #     # 如果检测到搜索需求，先执行搜索
                #     if search_intent['search_required'] and search_intent['search_query']:
                #         logger.info(f"在timeout期间检测到搜索需求 - 查询: {search_intent['search_query']}")

                #         # 执行搜索
                #         search_results = self.network_search_service.search_internet(
                #             query=search_intent['search_query'],
                #         )

                #         # 如果搜索成功，将结果添加到消息中
                #         if search_results and search_results['original']:
                #             logger.info("搜索成功，将结果添加到消息中")
                #             processed_message = f"{combined_message}\n\n{search_results['original']}"
                #             logger.info(processed_message)
                #         else:
                #             logger.warning("搜索失败或结果为空，继续正常处理请求")

                #     # 若检测到定时任务
                #     if search_intent['reminders']:
                #         logger.info("检测到定时任务设定需求，正在添加定时任务...")
                #         for target_time, reminder_content in search_intent['reminders']:
                #             logger.info(f"检测到提醒请求 - 用户: {sender_name}")
                #             logger.info(f"提醒时间: {target_time}, 内容: {reminder_content}")

                #             # 使用 reminder_service 创建提醒
                #             success = self.reminder_service.add_reminder(
                #                 chat_id=chat_id,
                #                 target_time=target_time,
                #                 content=reminder_content,
                #                 sender_name=sender_name,
                #                 silent=True
                #             )

                #             if success:
                #                 logger.info("提醒任务创建成功")
                #             else:
                #                 logger.error("提醒任务创建失败")

                # 检查是否为特殊请求(注释掉了生图功能)
                if self.voice_handler.is_voice_request(processed_message):
                    return self._handle_voice_request(processed_message, chat_id, sender_name, username, is_group)
                elif self.image_handler.is_random_image_request(processed_message):
                    return self._handle_random_image_request(processed_message, chat_id, sender_name, username, is_group)
               # elif not is_image_recognition and self.image_handler.is_image_generation_request(combined_message):
                    #return self._handle_image_generation_request(combined_message, chat_id, sender_name, username, is_group)
                else:
                    return self._handle_text_message(processed_message, chat_id, sender_name, username, is_group)

        except Exception as e:
            logger.error(f"处理消息队列失败: {str(e)}")
            return None

    def _process_text_for_display(self, text: str) -> str:
        """处理文本以确保表情符号正确显示"""
        try:
            # 先将Unicode表情符号转换为别名再转回，确保标准化
            return emoji.emojize(emoji.demojize(text))
        except Exception:
            return text

    def _filter_user_tags(self, text: str) -> str:
        """过滤消息中的用户标签
        
        Args:
            text: 原始文本
            
        Returns:
            str: 过滤后的文本
        """
        import re
        # 过滤掉 <用户 xxx> 和 </用户> 标签
        text = re.sub(r'<用户\s+[^>]+>\s*', '', text)
        text = re.sub(r'\s*</用户>', '', text)
        return text.strip()

    def _send_message_with_dollar(self, reply, chat_id):
        """以$为分隔符分批发送回复"""
        # 应用括号过滤到AI回复
        filtered_reply = filter_brackets(reply, "ai_response")
        if filtered_reply != reply:
            logger.info(f"AI回复已应用括号过滤: 移除了括号内容")
            reply = filtered_reply
        
        # 过滤用户标签
        reply = self._filter_user_tags(reply)
        
        # 首先处理文本中的emoji表情符号
        reply = self._process_text_for_display(reply)

        if '$' in reply or '＄' in reply:
            parts = [p.strip() for p in reply.replace("＄", "$").split("$") if p.strip()]
            for part in parts:
                # 检查当前部分是否包含表情标签
                emotion_tags = self.emoji_handler.extract_emotion_tags(part)
                if emotion_tags:
                    logger.debug(f"消息片段包含表情: {emotion_tags}")

                # 清理表情标签并发送文本
                clean_part = part
                for tag in emotion_tags:
                    clean_part = clean_part.replace(f'[{tag}]', '')

                if clean_part.strip():
                    self.wx.SendMsg(msg=clean_part.strip(), who=chat_id)
                    logger.debug(f"发送消息: {clean_part[:20]}...")

                # 发送该部分包含的表情
                for emotion_type in emotion_tags:
                    try:
                        emoji_path = self.emoji_handler.get_emoji_for_emotion(emotion_type)
                        if emoji_path:
                            self.wx.SendFiles(filepath=emoji_path, who=chat_id)
                            logger.debug(f"已发送表情: {emotion_type}")
                            time.sleep(1)
                    except Exception as e:
                        logger.error(f"发送表情失败 - {emotion_type}: {str(e)}")

                time.sleep(random.randint(2, 4))
        else:
            # 处理不包含分隔符的消息
            emotion_tags = self.emoji_handler.extract_emotion_tags(reply)
            if emotion_tags:
                logger.debug(f"消息包含表情: {emotion_tags}")

            clean_reply = reply
            for tag in emotion_tags:
                clean_reply = clean_reply.replace(f'[{tag}]', '')

            if clean_reply.strip():
                self.wx.SendMsg(msg=clean_reply.strip(), who=chat_id)
                logger.debug(f"发送消息: {clean_reply[:20]}...")

            # 发送表情
            for emotion_type in emotion_tags:
                try:
                    emoji_path = self.emoji_handler.get_emoji_for_emotion(emotion_type)
                    if emoji_path:
                        self.wx.SendFiles(filepath=emoji_path, who=chat_id)
                        logger.debug(f"已发送表情: {emotion_type}")
                        time.sleep(1)
                except Exception as e:
                    logger.error(f"发送表情失败 - {emotion_type}: {str(e)}")

    def _send_raw_message(self, text: str, chat_id: str):
        """直接发送原始文本消息，保留所有换行符和格式

        Args:
            text: 要发送的原始文本
            chat_id: 接收消息的聊天ID
        """
        try:
            # 应用括号过滤到AI回复
            filtered_text = filter_brackets(text, "ai_response")
            if filtered_text != text:
                logger.info(f"AI回复已应用括号过滤: 移除了括号内容")
                text = filtered_text
            
            # 过滤用户标签
            text = self._filter_user_tags(text)
            
            # 只处理表情符号，不做其他格式处理
            text = self._process_text_for_display(text)

            # 提取表情标签
            emotion_tags = self.emoji_handler.extract_emotion_tags(text)

            # 清理表情标签
            clean_text = text
            for tag in emotion_tags:
                clean_text = clean_text.replace(f'[{tag}]', '')

            # 直接发送消息，只做必要的处理
            if clean_text:
                clean_text = clean_text.replace('$', '')
                clean_text = clean_text.replace('＄', '')  # 全角$符号
                clean_text = clean_text.replace(r'\n', '\r\n\r\n')
                #logger.info(clean_text)
                self.wx.SendMsg(msg=clean_text, who=chat_id)
                #logger.info(f"已发送经过处理的文件内容: {file_content}")

        except Exception as e:
            logger.error(f"发送原始格式消息失败: {str(e)}")

    def _clear_tts_text(self, text: str) -> str:
        """用于清洗回复,使得其适合进行TTS"""
        # 完全移除emoji表情符号
        try:
            # 将emoji转换为空字符串
            text = emoji.replace_emoji(text, replace='')
        except Exception:
            pass

        text = text.replace('$',',')
        text = re.sub(r'\[.*?\]','', text)
        return text.strip()

    def _handle_voice_request(self, content, chat_id, sender_name, username, is_group):
        """处理语音请求"""
        logger.info("处理语音请求")
        # 对于群聊消息，使用更清晰的对话格式
        if is_group:
            api_content = f"<用户 {sender_name}>\n{content}\n</用户>"
        else:
            api_content = content

        reply = self.get_api_response(api_content, chat_id, is_group)
        logger.info(f"AI回复: {reply}")
        logger.info(f"TTS内容: {self._clear_tts_text(reply)}")

        # 处理回复中的思考过程
        if "</think>" in reply:
            think_content, reply = reply.split("</think>", 1)
            logger.debug(f"思考过程: {think_content.strip()}")

        # 群聊环境下添加@标签
        if is_group:
            # 检查回复是否已经包含@用户名，避免重复添加
            if not reply.startswith(f"@{sender_name} "):
                reply = f"@{sender_name} {reply}"
                logger.debug("群聊环境下发送消息，已添加@标签")
            else:
                logger.debug("回复已包含@标签，跳过添加")
            self._send_message_with_dollar(reply, chat_id)
        else:
            reply = reply.strip()
            voice_path = self.voice_handler.generate_voice(self._clear_tts_text(reply))
            if voice_path:
                try:
                    self.wx.SendFiles(filepath=voice_path, who=chat_id)
                except Exception as e:
                    logger.error(f"发送语音失败: {str(e)}")
                    self._send_message_with_dollar(reply, chat_id)
                finally:
                    try:
                        os.remove(voice_path)
                    except Exception as e:
                        logger.error(f"删除临时语音文件失败: {str(e)}")
            else:
                self._send_message_with_dollar(reply, chat_id)

        # 判断是否是系统消息
        is_system_message = sender_name == "System" or username == "System"

        # 异步保存消息记录
        # 保存实际用户发送的内容，群聊中保留发送者信息
        save_content = api_content if is_group else content
        threading.Thread(target=self.save_message,
                       args=(chat_id, sender_name, save_content, reply, is_system_message)).start()
        return reply

    def _handle_random_image_request(self, content, chat_id, sender_name, username, is_group):
        """处理随机图片请求"""
        logger.info("处理随机图片请求")
        # 对于群聊消息，使用更清晰的对话格式   
        if is_group:
            api_content = f"<用户 {sender_name}>\n{content}\n</用户>"
        else:
            api_content = content

        image_path = self.image_handler.get_random_image()
        if image_path:
            try:
                self.wx.SendFiles(filepath=image_path, who=chat_id)
                reply = "给主人你找了一张好看的图片哦~"
            except Exception as e:
                logger.error(f"发送图片失败: {str(e)}")
                reply = "抱歉主人，图片发送失败了..."
            finally:
                try:
                    if os.path.exists(image_path):
                        os.remove(image_path)
                except Exception as e:
                    logger.error(f"删除临时图片失败: {str(e)}")

            # 群聊环境下添加@标签
            if is_group:
                # 检查回复是否已经包含@用户名，避免重复添加
                if not reply.startswith(f"@{sender_name} "):
                    reply = f"@{sender_name} {reply}"
            # 过滤用户标签再发送
            filtered_reply = self._filter_user_tags(reply)
            self.wx.SendMsg(msg=filtered_reply, who=chat_id)

            # 判断是否是系统消息
            is_system_message = sender_name == "System" or username == "System"

            # 异步保存消息记录
            # 保存实际用户发送的内容，群聊中保留发送者信息
            save_content = api_content if is_group else content
            threading.Thread(target=self.save_message,
                           args=(chat_id, sender_name, save_content, reply, is_system_message)).start()
            return reply
        return None

    def _handle_image_generation_request(self, content, chat_id, sender_name, username, is_group):
        """处理图像生成请求"""
        logger.info("处理画图请求")
        # 对于群聊消息，使用更清晰的对话格式
        if is_group:
            api_content = f"<用户 {sender_name}>\n{content}\n</用户>"
        else:
            api_content = content

        image_path = self.image_handler.generate_image(content)
        if image_path:
            try:
                self.wx.SendFiles(filepath=image_path, who=chat_id)
                reply = "这是按照主人您的要求生成的图片\\(^o^)/~"
            except Exception as e:
                logger.error(f"发送生成图片失败: {str(e)}")
                reply = "抱歉主人，图片生成失败了..."
            finally:
                try:
                    if os.path.exists(image_path):
                        os.remove(image_path)
                except Exception as e:
                    logger.error(f"删除临时图片失败: {str(e)}")

            # 群聊环境下添加@标签
            if is_group:
                # 检查回复是否已经包含@用户名，避免重复添加
                if not reply.startswith(f"@{sender_name} "):
                    reply = f"@{sender_name} {reply}"
            # 过滤用户标签再发送
            filtered_reply = self._filter_user_tags(reply)
            self.wx.SendMsg(msg=filtered_reply, who=chat_id)

            # 判断是否是系统消息
            is_system_message = sender_name == "System" or username == "System"

            # 异步保存消息记录
            # 保存实际用户发送的内容，群聊中保留发送者信息
            save_content = api_content if is_group else content
            threading.Thread(target=self.save_message,
                           args=(chat_id, sender_name, save_content, reply, is_system_message)).start()
            return reply
        return None

    def _send_command_response(self, command: str, reply: str, chat_id: str):
        """发送命令响应，根据命令类型决定是否保留原始格式

        Args:
            command: 命令名称，如 '/state'
            reply: 要发送的回复内容
            chat_id: 聊天ID
        """
        if not reply:
            return

        # 检查是否是需要保留原始格式的命令
        if command in self.preserve_format_commands:
            # 使用原始格式发送消息
            logger.info(f"使用原始格式发送命令响应: {command}")
            self._send_raw_message(reply, chat_id)
        else:
            # 使用正常的消息发送方式
            self._send_message_with_dollar(reply, chat_id)

    def _handle_text_message(self, content, chat_id, sender_name, username, is_group):
        """处理普通文本消息"""
        # 新增：检测是否需要定时拨打微信电话
        try:
            self.call_scheduler.handle_message(content, sender_name)
        except Exception as e:
            logger.warning(f"CallScheduler 解析失败: {str(e)}")

        # 新增：智能联网搜索检测 - 已注释
        # if INTELLIGENT_SEARCH_ENABLED:
        #     try:
        #         search_result = self._handle_intelligent_search(content)
        #         if search_result:
        #             # 有搜索结果，添加到消息上下文
        #             api_content = f"用户问题：{content}\n\n最新信息：{search_result}\n\n请根据上述最新信息自然地回答用户的问题。"
        #             logger.debug(f"智能搜索组合内容: {api_content[:300]}...")
        #             reply_from_ai = self.get_api_response(api_content, chat_id, is_group)

        #             if is_group:
        #                 reply_from_ai = f"@{sender_name} {reply_from_ai}"

        #             # 记录智能搜索回复内容
        #             logger.info(f"智能搜索Bot回复内容: {reply_from_ai}")

        #             try:
        #                 self._send_message_with_dollar(reply_from_ai, chat_id)
        #             except Exception as e:
        #                 logger.error(f"搜索回复发送失败，改用原始发送: {str(e)}")
        #                 self._send_raw_message(reply_from_ai, chat_id)

        #             threading.Thread(target=self.save_message,
        #                              args=(chat_id, sender_name, content, reply_from_ai, False)).start()

        #             return reply_from_ai
        #     except Exception as e:
        #         logger.error(f"智能搜索处理失败: {str(e)}")
        #         # 出错时继续正常流程

        # 新增：检测是否需要外出天气提醒 - 已注释
        # try:
        #     detected = self.outdoor_reminder.check_message(content)
        #     if detected:
        #         motivation, weather_summary = detected

        #         location = self.outdoor_reminder.location
        #         prompt = (
        #             f"用户现在要{motivation}。以下是 {location} 当前天气信息：\n{weather_summary}\n\n"
        #             "请充当用户的贴心助手，参考上下文自然地提醒用户注意天气情况（例如带伞、防晒、穿戴等），"
        #             "注意用中文回答，不要提及你是 AI，也不要暴露天气数据来源。"
        #         )

        #         reply_ai = self.get_api_response(prompt, chat_id, is_group)
        #         if is_group:
        #             reply_ai = f"@{sender_name} {reply_ai}"

        #         self._send_message_with_dollar(reply_ai, chat_id)
        #         threading.Thread(target=self.save_message,
        #                          args=(chat_id, sender_name, content, reply_ai, False)).start()
        #         return reply_ai
        # except Exception as e:
        #     logger.error(f"外出天气提醒处理失败: {str(e)}")

        # 检查是否是命令
        command = None
        if content.startswith('/'):
            command = content.split(' ')[0].lower()
            logger.debug(f"检测到命令: {command}")

        # 对于群聊消息，使用更清晰的对话格式
        if is_group:
            api_content = f"<用户 {sender_name}>\n{content}\n</用户>"
        else:
            api_content = content

        reply = self.get_api_response(api_content, chat_id, is_group)
        # logger.info(f"AI回复: {reply}")  # 已在多模态LLM或普通LLM中记录，避免重复

        # 处理回复中的思考过程
        if "</think>" in reply:
            think_content, reply = reply.split("</think>", 1)
            logger.debug(f"思考过程: {think_content.strip()}")

        # 处理群聊中的回复
        if is_group:
            # 检查回复是否已经包含@用户名，避免重复添加
            if not reply.startswith(f"@{sender_name} "):
                reply = f"@{sender_name} {reply}"
                logger.debug("已添加群聊@")
            else:
                logger.debug("回复已包含@标签，跳过添加")

        # 判断是否是系统消息
        is_system_message = sender_name == "System" or username == "System"

        # 记录bot回复内容
        logger.info(f"Bot回复内容: {reply}")

        # 发送文本消息和表情
        if command and command in self.preserve_format_commands:
            # 如果是需要保留原始格式的命令，使用原始格式发送
            self._send_command_response(command, reply, chat_id)
        else:
            # 否则使用正常的消息发送方式
            self._send_message_with_dollar(reply, chat_id)

        # 异步保存消息记录
        # 保存实际用户发送的内容，群聊中保留发送者信息
        save_content = api_content if is_group else content
        threading.Thread(target=self.save_message,
                        args=(chat_id, sender_name, save_content, reply, is_system_message)).start()
        return reply

    
    def _add_to_system_prompt(self, chat_id: str, content: str) -> None:
        """
        将内容添加到系统提示词中

        Args:
            chat_id: 聊天ID
            content: 要添加的内容
        """
        try:
            # 初始化聊天的系统提示词字典（如果不存在）
            if not hasattr(self, 'system_prompts'):
                self.system_prompts = {}

            # 初始化当前聊天的系统提示词（如果不存在）
            if chat_id not in self.system_prompts:
                self.system_prompts[chat_id] = []

            # 添加内容到系统提示词列表
            self.system_prompts[chat_id].append(content)

            # 限制系统提示词列表的长度（保留最新的 5 条）
            if len(self.system_prompts[chat_id]) > 5:
                self.system_prompts[chat_id] = self.system_prompts[chat_id][-5:]

            logger.info(f"已将内容添加到聊天 {chat_id} 的系统提示词中")
        except Exception as e:
            logger.error(f"添加内容到系统提示词失败: {str(e)}")

    # 已在类的开头初始化对话计数器

    def _remove_search_content_from_context(self, chat_id: str, content: str) -> None:
        """
        从上下文中删除搜索内容，并添加到系统提示词中

        Args:
            chat_id: 聊天ID
            content: 要删除的搜索内容
        """
        try:
            # 从内存中的对话历史中删除搜索内容
            if hasattr(self, 'memory_service') and self.memory_service:
                # 尝试从内存中删除搜索内容
                # 注意：这里只是一个示例，实际实现可能需要根据 memory_service 的实际接口调整
                try:
                    # 如果 memory_service 有删除内容的方法，可以调用它
                    # 这里只是记录日志，实际实现可能需要根据具体情况调整
                    logger.info(f"尝试从内存中删除搜索内容: {content[:50]}...")
                except Exception as e:
                    logger.error(f"从内存中删除搜索内容失败: {str(e)}")

            # 如果有其他上下文存储机制，也可以在这里处理

            logger.info(f"已从上下文中删除搜索内容: {content[:50]}...")
        except Exception as e:
            logger.error(f"从上下文中删除搜索内容失败: {str(e)}")

    def _async_generate_summary(self, chat_id: str, url: str, content: str, model: str = None) -> None:
        """
        异步生成总结并添加到系统提示词中
        按照时间而不是对话计数来执行总结

        Args:
            chat_id: 聊天ID
            url: 链接或搜索查询
            content: 要总结的内容
            model: 使用的模型（可选，如果不提供则使用用户配置的模型）
        """
        try:
            # 等待一段时间后再执行总结，确保不占用当前对话的时间
            # 这里设置为30秒，足够让用户进行下一次对话
            logger.info(f"开始等待总结生成时间: {url}")
            time.sleep(30)  # 等待 30 秒

            logger.info(f"开始异步生成总结: {url}")

            # 使用用户配置的模型，如果没有指定模型
            from src.config import MODEL
            summary_model = model if model else MODEL

            # 使用 network_search_service 中的 llm_service
            # 生成总结版本，用于系统提示词
            summary_messages = [
                {
                    "role": "user",
                    "content": f"请将以下内容总结为简洁的要点，以便在系统提示词中使用：\n\n{content}\n\n原始链接或查询: {url}"
                }
            ]

            # 调用 network_search_service 中的 llm_service 获取总结版本
            # 使用用户配置的模型
            logger.info(f"异步总结使用模型: {summary_model}")
            summary_result = self.network_search_service.llm_service.chat(
                messages=summary_messages,
                model=summary_model
            )

            if summary_result:
                # 生成最终的总结内容
                if "http" in url:
                    final_summary = f"关于链接 {url} 的信息：{summary_result}"
                else:
                    final_summary = f"关于\"{url}\"的信息：{summary_result}"

                # 从上下文中删除搜索内容
                self._remove_search_content_from_context(chat_id, content)

                # 添加到系统提示词中，但不发送给用户
                self._add_to_system_prompt(chat_id, final_summary)
                logger.info(f"已将异步生成的总结添加到系统提示词中，并从上下文中删除搜索内容: {url}")
            else:
                logger.warning(f"异步生成总结失败: {url}")
        except Exception as e:
            logger.error(f"异步生成总结失败: {str(e)}")

    #def _check_time_reminder_and_search(self, content: str, user_id: str, chat_id: str, sender_name: str, is_group: bool) -> bool:
    def _check_time_reminder_and_search(self, content: str, sender_name: str) -> bool:
        """
        检查和处理时间提醒和联网搜索需求 - 已完全禁用

        Args:
            content: 消息内容
            chat_id: 聊天ID
            sender_name: 发送者名称

        Returns:
            bool: 是否已处理搜索需求（如果已处理，则不需要继续处理消息）
        """
        # 功能已完全禁用，直接返回False
        return False

        # 以下代码已注释 - 避免处理系统消息
        # if sender_name == "System" or sender_name.lower() == "system" :
        #     logger.debug(f"跳过时间提醒和搜索识别：{sender_name}发送的消息不处理")
        #     return False

        # try:
        #     if "可作为你的回复参考" in content:
        #         logger.info(f"已联网获取过信息，直接获取回复")
        #         return True
            # 以下所有功能代码已注释
            # '''
            # # 使用 time_recognition 服务同时识别时间和搜索需求
            # # 传递用户名和角色名，用于替换提示词中的变量
            # # 传递网络搜索启用状态
            # from src.config import NETWORK_SEARCH_ENABLED

            # result = self.time_recognition.recognize_time_and_search(
            #     message=content,
            #     user_name=sender_name,
            #     avatar_name=self.current_avatar,
            #     network_search_enabled=NETWORK_SEARCH_ENABLED
            # )

            # # 处理提醒 - 已注释
            # # if result['reminders']:
            # #     for target_time, reminder_content in result['reminders']:
            # #         logger.info(f"检测到提醒请求 - 用户: {sender_name}")
            # #         logger.info(f"提醒时间: {target_time}, 内容: {reminder_content}")

            # #         # 使用 reminder_service 创建提醒
            # #         success = self.reminder_service.add_reminder(
            # #             chat_id=chat_id,
            # #             target_time=target_time,
            # #             content=reminder_content,
            # #             sender_name=sender_name,
            # #             silent=True
            # #         )

            # #         if success:
            # #             logger.info("提醒任务创建成功")
            # #         else:
            # #             logger.error("提醒任务创建失败")

            # # 处理搜索需求 - 只在没有在timeout期间处理过的情况下处理 - 已注释
            # # 检查消息中是否已经包含搜索结果
            # # if NETWORK_SEARCH_ENABLED and result['search_required'] and result['search_query']:
            # #     # 检查消息中是否已经包含搜索结果的标记
            # #     if "以下是关于" in content and "的搜索结果" in content:
            # #         logger.info(f"消息中已包含搜索结果，跳过搜索处理")
            #             return False

            #         logger.info(f"检测到搜索需求 - 用户: {sender_name}")
            #         logger.info(f"搜索查询: {result['search_query']}")

            #         # 先发送一个正在搜索的提示
            #         processing_message = f"正在搜索：{result['search_query']}，请稍候..."
            #         self._send_raw_message(processing_message, chat_id)

            #         # 获取对话上下文
            #         conversation_context = self.memory_manager.get_recent_context(self.current_avatar, user_id)

            #         # 调用网络搜索服务，传递对话上下文
            #         search_results = self.network_search_service.search_internet(
            #             query=result['search_query'],
            #             conversation_context=conversation_context
            #         )

            #         if search_results['original']:
            #             # 不发送搜索结果给用户，而是将其添加到上下文中
            #             logger.info("将搜索结果添加到上下文中，不发送给用户")

            #             # 异步生成总结并添加到系统提示词中
            #             # 使用用户配置的模型
            #             threading.Thread(
            #                 target=self._async_generate_summary,
            #                 args=(chat_id, result['search_query'], search_results['original'], None),  # 传入None，使用用户配置的模型
            #                 daemon=True
            #             ).start()
            #             logger.info(f"已启动异步线程生成搜索结果总结，使用用户配置的模型")

            #             # 异步保存消息记录
            #             is_system_message = sender_name == "System" or sender_name.lower() == "system"
            #             threading.Thread(target=self.save_message,
            #                             args=(chat_id, sender_name, content, content, is_system_message)).start()

            #             # 将搜索结果作为上下文添加到当前消息中
            #             content_with_context = f"{content}\n\n{search_results['original']}"

            #             # 处理添加了上下文的消息
            #             api_response = self.get_api_response(content_with_context, chat_id, is_group)

            #             # 发送API响应给用户
            #             self._send_raw_message(api_response, chat_id)

            #             # 返回 True 表示已处理搜索需求
            #             return True
            #         else:
            #             # 搜索失败，不发送错误消息，直接返回处理失败
            #             logger.info("搜索失败，不发送错误消息，继续正常处理请求")


            # # 返回 False 表示未处理搜索需求或处理失败
            # return False
            # '''

        # except Exception as e:
        #     logger.error(f"处理时间提醒和搜索失败: {str(e)}")
        #     return False

    def _check_time_reminder(self, content: str, chat_id: str, sender_name: str):
        """检查和处理时间提醒（兼容旧接口）- 已注释"""
        # 避免处理系统消息
        # if sender_name == "System" or sender_name.lower() == "system" :
        #     logger.debug(f"跳过时间提醒识别：{sender_name}发送的消息不处理")
        #     return

        # try:
        #     # 使用 time_recognition 服务识别时间
        #     time_infos = self.time_recognition.recognize_time(content)
        #     if time_infos:
        #         for target_time, reminder_content in time_infos:
        #             logger.info(f"检测到提醒请求 - 用户: {sender_name}")
        #             logger.info(f"提醒时间: {target_time}, 内容: {reminder_content}")

        #             # 使用 reminder_service 创建提醒
        #             success = self.reminder_service.add_reminder(
        #                 chat_id=chat_id,
        #                 target_time=target_time,
        #                 content=reminder_content,
        #                 sender_name=sender_name,
        #                 silent=True
        #             )

        #             if success:
        #                 logger.info("提醒任务创建成功")
        #             else:
        #                 logger.error("提醒任务创建失败")

        # except Exception as e:
        #     logger.error(f"处理时间提醒失败: {str(e)}")
        pass  # 功能已禁用

    def _judge_weather_intent(self, message: str) -> str:
        """
        智能判断天气相关消息的意图
        返回: "REAL_TIME" 或 "MEMORY"
        """
        # 实时查询关键词（优先级更高，先检查）
        REALTIME_KEYWORDS = [
            "今天", "现在", "当前", "目前", "实时", "最新",
            "外面", "出门", "准备出去", "要出门", "马上出门",
            "怎么样", "如何", "好不好"
        ]

        # 历史/回忆类关键词
        MEMORY_KEYWORDS = [
            "那天", "以前", "昨天", "前天", "上次", "之前",
            "记得", "还记得", "想起", "回忆", "当时", "那时候",
            "曾经", "过去", "原来", "以往"
        ]

        # 第一层：规则判断（先检查实时查询，避免"当前"被"当时"误匹配）
        message_lower = message.lower()

        # 先检查实时查询关键词
        if any(keyword in message for keyword in REALTIME_KEYWORDS):
            logger.info(f"天气意图判断：检测到实时查询关键词，判断为REAL_TIME")
            return "REAL_TIME"

        # 再检查回忆类关键词
        if any(keyword in message for keyword in MEMORY_KEYWORDS):
            logger.info(f"天气意图判断：检测到回忆关键词，判断为MEMORY")
            return "MEMORY"

        # 第二层：LLM判断（仅模糊情况）
        logger.info(f"天气意图判断：关键词规则无法判断，调用LLM判断")
        return self._llm_judge_weather_intent(message)

    def _llm_judge_weather_intent(self, message: str) -> str:
        """
        使用LLM判断模糊的天气意图
        """
        try:
            system_prompt = """分类任务：判断用户询问天气的意图

规则：
- 询问当前天气 → 输出：REAL_TIME
- 回忆过去天气 → 输出：MEMORY

只返回：REAL_TIME 或 MEMORY"""

            user_prompt = f"用户消息：{message}"

            response = self.deepseek.get_response(
                message=user_prompt,
                system_prompt=system_prompt,
                user_id="weather_intent_judge"
            )

            # 简单解析，容错处理
            if "REAL_TIME" in response:
                logger.info(f"LLM判断天气意图为：REAL_TIME")
                return "REAL_TIME"
            elif "MEMORY" in response:
                logger.info(f"LLM判断天气意图为：MEMORY")
                return "MEMORY"
            else:
                logger.warning(f"LLM返回格式异常：{response}，默认为REAL_TIME")
                return "REAL_TIME"  # 默认值

        except Exception as e:
            logger.error(f"LLM天气意图判断失败: {str(e)}，默认为REAL_TIME")
            return "REAL_TIME"  # 出错时默认为实时查询

    def _handle_intelligent_search(self, message: str) -> Optional[str]:
        """
        智能联网搜索处理 - 已禁用
        返回搜索结果上下文，如果不需要搜索则返回None
        """
        # 功能已禁用，直接返回None
        return None

        # 以下代码已注释
        # try:
        #     # 检查消息是否已经包含搜索结果（避免重复搜索）
        #     if "稍等，我看看" in message or "搜索结果：" in message:
        #         logger.info("消息已包含搜索结果，跳过智能搜索")
        #         return None

        #     # 检测搜索意图并规划查询
        #     intent_result = self._detect_and_plan_search(message)

        #     if not intent_result.get("need_search", False):
        #         logger.info("无需联网搜索，继续正常对话流程")
        #         return None

        #     search_query = intent_result.get("search_query", "")
        #     if not search_query:
        #         logger.warning("搜索意图检测到但查询内容为空")
        #         return None

        #     logger.info(f"检测到搜索需求，查询内容：{search_query}")

        #     # 添加默认地区信息到搜索查询
        #     from src.config import PRESET_WEATHER_LOCATION
        #     default_location = PRESET_WEATHER_LOCATION or "深圳龙华"

        #     # 如果是天气查询且没有指定地点，添加默认地区
        #     if "天气" in search_query and not any(loc in search_query for loc in ["深圳", "北京", "上海", "广州", "杭州", "成都", "重庆", "武汉", "南京", "西安", "惠州", "东莞", "佛山"]):
        #         enhanced_query = f"{default_location} {search_query}"
        #         logger.info(f"天气查询添加默认地区：{enhanced_query}")
        #     else:
        #         enhanced_query = search_query

        #     # 使用主LLM联网搜索
        #     natural_prompt = f"""你可以联网搜索实时信息。在回复时请注意：

        # 1. 自然对话风格：
        #    - 不要说"根据查询结果"、"根据搜索信息"等机器化表达
        #    - 可以说"我看了一下天气预报"、"刚查了下"、"天气情况是这样的"
        #    - 像朋友聊天一样自然地分享信息

        # 2. 天气回复示例：
        #    - ❌ 机器化："根据查询结果，{default_location}今天天气晴朗，温度25度"
        #    - ✅ 自然："今天天气不错呢，晴天，大概25度左右，挺舒服的"
        #    - ✅ 自然："我看了下天气预报，今天是晴天，温度在25度上下"

        # 3. 其他信息也要自然表达，不要暴露搜索过程，就像你本来就知道这些信息一样。

        # 如果用户询问天气但没有指定地点，默认查询{default_location}的天气。"""

        #     search_result = self.deepseek.get_response(
        #         message=enhanced_query,
        #         system_prompt=natural_prompt,
        #         user_id="web_search"
        #     )

        #     if search_result:
        #         logger.info("联网搜索成功")
        #         logger.debug(f"搜索结果内容: {search_result[:200]}...")
        #         return search_result  # 直接返回搜索结果，不添加前缀
        #     else:
        #         logger.warning("联网搜索结果为空")
        #         return None

        # except Exception as e:
        #     logger.error(f"智能搜索处理失败: {str(e)}")
        #     return None

    def _detect_and_plan_search(self, message: str) -> Dict:
        """
        检测搜索意图并规划查询内容 - 已禁用
        """
        # 功能已禁用，直接返回不需要搜索
        return {"need_search": False, "search_query": ""}

        # 以下代码已注释
        # try:
        #     # 第一层：字典预过滤（快速判断）
        #     message_lower = message.lower()

        #     # 搜索需求关键词
        #     search_keywords = [
        #         # 天气相关
        #         "天气", "温度", "下雨", "晴天", "阴天", "雾霾", "空气质量", "气温",
        #         # 信息查询
        #         "新闻", "最新消息", "价格", "股价", "汇率", "疫情数据", "政策",
        #         # 明确的查询意图
        #         "帮我查", "搜索一下", "查询", "查找",
        #         # 地点和路线
        #         "在哪里", "怎么去", "路线", "地址", "营业时间", "电话号码",
        #         # 比较和评价
        #         "官网", "评价", "排行榜", "比较", "哪个好", "推荐"
        #     ]

        #     # 检查搜索关键词
        #     for keyword in search_keywords:
        #         if keyword in message:
        #             logger.info(f"检测到搜索关键词 '{keyword}'，需要搜索")
        #             return {"need_search": True, "search_query": message}

            # 以下代码已注释
            # # 如果没有匹配到搜索关键词，跳过搜索
            # logger.info("搜索关键词未匹配到，采用普通回复")
            # return {"need_search": False, "search_query": ""}

            # # 从配置获取默认地区
            # from src.config import PRESET_WEATHER_LOCATION
            # default_location = PRESET_WEATHER_LOCATION or "深圳龙华"

            # system_prompt = f"""以下是用户的消息，请你根据他的消息来判断他想干什么以及需要搜索什么信息。

            # 如果用户需要联网查询信息（如天气、新闻、价格、最新信息等），返回JSON格式：
            # {{
            #     "need_search": true,
            #     "search_query": "具体的搜索查询内容"
            # }}

            # 如果用户不需要联网查询（比如聊天、回忆、角色扮演等），返回：
            # {{
            #     "need_search": false,
            #     "search_query": ""
            # }}

            # 特别注意：
            # - 如果用户询问天气但没有指定地点，请使用默认地区：{default_location}
            # - 天气查询格式应为："{default_location} 当前天气"

            # 用户消息："""

            # response = self.deepseek.get_response(
            #     message=f"{system_prompt}{message}",
            #     system_prompt="你是一个意图识别助手，只返回JSON格式，不要其他内容。",
            #     user_id="search_intent"
            # )

            # # 解析JSON响应
            # import json
            # try:
            #     result = json.loads(response)
            #     logger.info(f"搜索意图识别结果：{result}")
            #     return result
            # except json.JSONDecodeError:
            #     logger.warning(f"搜索意图识别返回格式异常：{response}")
            #     return {"need_search": False, "search_query": ""}

        # except Exception as e:
        #     logger.error(f"搜索意图识别失败: {str(e)}")
        #     return {"need_search": False, "search_query": ""}

    def add_to_queue(self, chat_id: str, content: str, sender_name: str,
                    username: str, is_group: bool = False):
        """添加消息到队列（兼容旧接口）"""
        return self._add_to_message_queue(content, chat_id, sender_name, username, is_group, False)

    def process_messages(self, chat_id: str):
        """处理消息队列中的消息（已废弃，保留兼容）"""
        # 该方法不再使用，保留以兼容旧代码
        logger.warning("process_messages方法已废弃，使用handle_message代替")
        pass
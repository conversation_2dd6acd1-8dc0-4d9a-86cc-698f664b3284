#!/usr/bin/env python3
"""
详细测试核心记忆增量更新功能
"""

import sys
import os
sys.path.append('F:/test/fork夜间 - q/Cherry Memory')

from modules.newmemory.database_memory_service import DatabaseMemoryService
import json
import logging

# 设置日志级别为DEBUG
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def load_config():
    """加载配置文件"""
    config_path = 'F:/test/fork夜间 - q/Cherry Memory/src/config/config.json'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config

def main():
    """主测试函数"""
    print("=== 核心记忆增量更新详细测试 ===\n")
    
    # 加载配置
    config = load_config()
    llm_config = config['categories']['llm_settings']['settings']
    
    print("配置信息:")
    print(f"API Key: {llm_config['api_key']['value'][:10]}...")
    print(f"Base URL: {llm_config['base_url']['value']}")
    print(f"Model: {llm_config['model']['value']}")
    print(f"Max Tokens: {llm_config['max_tokens']['value']}")
    print(f"Temperature: {llm_config['temperature']['value']}")
    print()
    
    # 创建记忆服务实例
    service = DatabaseMemoryService(
        root_dir='F:/test/fork夜间 - q/Cherry Memory',
        api_key=llm_config['api_key']['value'],
        base_url=llm_config['base_url']['value'],
        model=llm_config['model']['value'],
        max_token=llm_config['max_tokens']['value'],
        temperature=llm_config['temperature']['value']
    )
    
    # 模拟测试对话数据 - 包含更多个人化和重要信息
    test_conversations = [
        {
            "role": "user",
            "content": "今天是我们恋爱三周年纪念日，我给你准备了一个特别的礼物"
        },
        {
            "role": "assistant", 
            "content": "真的吗？三周年了，时间过得真快！是什么特别的礼物呢？我很期待！"
        },
        {
            "role": "user",
            "content": "是一条你一直想要的项链，上面有我们名字的缩写"
        },
        {
            "role": "assistant",
            "content": "太感动了！你记得我说过想要那条项链，我真的很喜欢。谢谢你这么用心，这是我收到过最珍贵的礼物。"
        },
        {
            "role": "user",
            "content": "我们晚上去那家我们第一次约会的餐厅庆祝吧"
        },
        {
            "role": "assistant",
            "content": "好主意！那家意大利餐厅对我们来说有特殊的意义，我还记得第一次约会时你紧张的样子，很可爱。"
        },
        {
            "role": "user",
            "content": "我也记得，那时候我手心都在出汗。对了，我下个月要出差去上海一周"
        },
        {
            "role": "assistant",
            "content": "去上海一周啊，有点久呢。不过没关系，工作重要，我会想你的。记得每天和我联系，我会给你准备一些带在路上吃的东西。"
        },
        {
            "role": "user",
            "content": "你对我太好了，我一定会每天给你发消息的。我也给你带了小礼物"
        },
        {
            "role": "assistant",
            "content": "你对我真好！其实只要你能平安回来，就是最好的礼物了。不过既然有礼物，我当然也很期待啦！"
        }
    ]
    
    print("模拟对话数据:")
    for i, msg in enumerate(test_conversations, 1):
        role = "用户" if msg['role'] == 'user' else "助手"
        print(f"{i}. {role}: {msg['content']}")
    print()
    
    # 检查当前记忆状态
    print("=== 当前记忆状态 ===")
    current_memory = service.get_core_memory('林绫-优化版', '1')
    print(f"当前记忆长度: {len(current_memory)} 字符")
    print(f"记忆内容预览: {current_memory[:200]}...")
    print()
    
    # 测试是否为初始状态
    is_initial = service._is_initial_memory_state(current_memory)
    print(f"是否为初始记忆状态: {is_initial}")
    print()
    
    # 执行增量更新
    print("=== 开始增量更新测试 ===")
    print("正在调用LLM分析新信息...")
    result = service.update_core_memory('林绫-优化版', '1', test_conversations)
    print(f"更新结果: {result}")
    print()
    
    # 检查更新后的记忆
    print("=== 更新后的记忆状态 ===")
    updated_memory = service.get_core_memory('林绫-优化版', '1')
    print(f"更新后记忆长度: {len(updated_memory)} 字符")
    print(f"记忆内容预览: {updated_memory[:500]}...")
    print()
    
    # 显示变化
    if len(updated_memory) > len(current_memory):
        print("[成功] 记忆已成功增量更新！")
        print(f"增加了 {len(updated_memory) - len(current_memory)} 字符")
        
        # 找出新增的内容
        if current_memory in updated_memory:
            new_content = updated_memory[len(current_memory):].strip()
            if new_content:
                print("新增内容:")
                print(new_content)
            else:
                print("没有明显的新增内容")
        else:
            print("记忆内容被重新组织或优化")
    elif len(updated_memory) == len(current_memory):
        print("[信息] 记忆没有变化，可能是因为:")
        print("1. 没有新的重要信息")
        print("2. LLM认为这些信息不够重要")
        print("3. 系统检测到重复信息")
    else:
        print("[警告] 记忆长度减少了，可能进行了优化压缩")
    
    # 检查数据库状态
    print("\n=== 数据库状态检查 ===")
    import sqlite3
    db_path = 'F:/test/fork夜间 - q/Cherry Memory/data/avatars/林绫-优化版/memory/db/core_memory.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM core_memory WHERE user_id = "1";')
    count = cursor.fetchone()[0]
    print(f"数据库记录数: {count}")
    
    cursor.execute('SELECT content FROM core_memory WHERE user_id = "1";')
    db_content = cursor.fetchone()[0]
    print(f"数据库内容长度: {len(db_content)}")
    conn.close()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
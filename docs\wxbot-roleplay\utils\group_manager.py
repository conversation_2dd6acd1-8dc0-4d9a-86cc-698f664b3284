#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
群聊管理模块
基于 wxautox 的群聊管理功能，提供群聊成员管理、群聊设置等功能
"""

import time
import re
from typing import List, Dict, Optional, Union, Tuple
from utils.logger import log
import config


class GroupManager:
    """群聊管理器 - 基于 wxautox Plus 版本的群聊管理功能"""
    
    def __init__(self, wx_instance=None):
        """
        初始化群聊管理器

        Args:
            wx_instance: WeChat 实例
        """
        self.wx = wx_instance
        self.group_cache = {}  # 群聊信息缓存

    def check_group_management_permission(self, user_id: str) -> bool:
        """
        检查用户是否有群聊管理权限

        Args:
            user_id: 用户ID

        Returns:
            bool: 是否有权限
        """
        try:
            import config

            # 检查是否启用群聊管理功能
            if not getattr(config, 'ENABLE_GROUP_MANAGEMENT', True):
                return False

            # 检查是否为管理员
            if user_id not in config.ADMIN_LIST:
                return False

            # 检查群聊管理权限列表
            group_admins = getattr(config, 'GROUP_MANAGEMENT_ADMINS', [])
            if group_admins and user_id not in group_admins:
                return False

            return True

        except Exception as e:
            log(f"检查群聊管理权限异常: {str(e)}", "ERROR")
            return False
        
    def set_wx_instance(self, wx_instance):
        """设置微信实例"""
        self.wx = wx_instance
        
    def get_group_members(self, group_name: str) -> List[str]:
        """
        获取群聊成员列表

        Args:
            group_name: 群聊名称

        Returns:
            List[str]: 群聊成员列表，失败返回空列表
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return []

            # 打开群聊窗口
            self.wx.ChatWith(group_name)

            # 获取群聊子窗口实例
            chat = self.wx.GetSubWindow(group_name)
            if not chat:
                log(f"无法获取群聊子窗口: {group_name}", "ERROR")
                return []

            # 获取群聊成员
            members = chat.GetGroupMembers()
            if members:
                log(f"获取群聊 {group_name} 成员成功，共 {len(members)} 人")
                # 缓存群聊成员信息
                self.group_cache[group_name] = {
                    'members': members,
                    'update_time': time.time()
                }
                return members
            else:
                log(f"获取群聊 {group_name} 成员失败", "ERROR")
                return []

        except Exception as e:
            log(f"获取群聊成员异常: {str(e)}", "ERROR")
            return []
    
    def add_group_members(self, group_name: str, members: Union[str, List[str]], 
                         reason: str = None) -> bool:
        """
        添加群聊成员
        
        Args:
            group_name: 群聊名称
            members: 要添加的成员名称或成员列表
            reason: 申请理由（当群主开启验证时需要）
            
        Returns:
            bool: 是否添加成功
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return False
                
            # 确保 members 是列表格式
            if isinstance(members, str):
                members = [members]

            # 打开群聊窗口
            self.wx.ChatWith(group_name)

            # 获取群聊子窗口实例
            chat = self.wx.GetSubWindow(group_name)
            if not chat:
                log(f"无法获取群聊子窗口: {group_name}", "ERROR")
                return False

            # 添加群聊成员
            result = chat.AddGroupMembers(group=group_name, members=members, reason=reason)
            
            if result and result.success:
                log(f"成功添加成员到群聊 {group_name}: {', '.join(members)}")
                # 清除缓存，下次获取时重新加载
                if group_name in self.group_cache:
                    del self.group_cache[group_name]
                return True
            else:
                error_msg = result.message if result else "未知错误"
                log(f"添加群聊成员失败: {error_msg}", "ERROR")
                return False
                
        except Exception as e:
            log(f"添加群聊成员异常: {str(e)}", "ERROR")
            return False
    
    def remove_group_members(self, group_name: str, members: Union[str, List[str]]) -> bool:
        """
        移除群聊成员
        
        Args:
            group_name: 群聊名称
            members: 要移除的成员名称或成员列表
            
        Returns:
            bool: 是否移除成功
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return False
                
            # 确保 members 是列表格式
            if isinstance(members, str):
                members = [members]

            # 打开群聊窗口
            self.wx.ChatWith(group_name)

            # 获取群聊子窗口实例
            chat = self.wx.GetSubWindow(group_name)
            if not chat:
                log(f"无法获取群聊子窗口: {group_name}", "ERROR")
                return False

            # 移除群聊成员
            result = chat.RemoveGroupMembers(group=group_name, members=members)
            
            if result and result.success:
                log(f"成功从群聊 {group_name} 移除成员: {', '.join(members)}")
                # 清除缓存，下次获取时重新加载
                if group_name in self.group_cache:
                    del self.group_cache[group_name]
                return True
            else:
                error_msg = result.message if result else "未知错误"
                log(f"移除群聊成员失败: {error_msg}", "ERROR")
                return False
                
        except Exception as e:
            log(f"移除群聊成员异常: {str(e)}", "ERROR")
            return False
    
    def manage_group_settings(self, group_name: str, **kwargs) -> bool:
        """
        管理群聊设置
        
        Args:
            group_name: 群聊名称
            **kwargs: 群聊设置参数
                - name: 新群名称
                - remark: 新备注名
                - myname: 我的群昵称
                - notice: 群公告
                - quit: 是否退出群（谨慎使用）
                
        Returns:
            bool: 是否设置成功
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return False
                
            # 打开群聊窗口
            self.wx.ChatWith(group_name)

            # 获取群聊子窗口实例
            chat = self.wx.GetSubWindow(group_name)
            if not chat:
                log(f"无法获取群聊子窗口: {group_name}", "ERROR")
                return False

            # 管理群聊设置
            result = chat.ManageGroup(**kwargs)
            
            if result and result.success:
                settings_desc = []
                for key, value in kwargs.items():
                    if value is not None:
                        settings_desc.append(f"{key}: {value}")
                log(f"成功修改群聊 {group_name} 设置: {', '.join(settings_desc)}")
                return True
            else:
                error_msg = result.message if result else "未知错误"
                log(f"修改群聊设置失败: {error_msg}", "ERROR")
                return False
                
        except Exception as e:
            log(f"管理群聊设置异常: {str(e)}", "ERROR")
            return False
    
    def at_all_members(self, group_name: str, message: str) -> bool:
        """
        @所有人发送消息
        
        Args:
            group_name: 群聊名称
            message: 要发送的消息
            
        Returns:
            bool: 是否发送成功
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return False
                
            # 使用 AtAll 方法发送@所有人消息
            result = self.wx.AtAll(msg=message, who=group_name)
            
            if result and result.success:
                log(f"成功在群聊 {group_name} @所有人发送消息")
                return True
            else:
                error_msg = result.message if result else "未知错误"
                log(f"@所有人发送消息失败: {error_msg}", "ERROR")
                return False
                
        except Exception as e:
            log(f"@所有人发送消息异常: {str(e)}", "ERROR")
            return False
    
    def get_group_info(self, group_name: str) -> Dict:
        """
        获取群聊信息
        
        Args:
            group_name: 群聊名称
            
        Returns:
            Dict: 群聊信息字典
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return {}
                
            # 打开群聊窗口
            self.wx.ChatWith(group_name)

            # 获取群聊子窗口实例
            chat = self.wx.GetSubWindow(group_name)
            if not chat:
                log(f"无法获取群聊子窗口: {group_name}", "ERROR")
                return {}

            # 获取群聊信息
            info = chat.ChatInfo()
            if info:
                log(f"获取群聊 {group_name} 信息成功")
                return info
            else:
                log(f"获取群聊 {group_name} 信息失败", "ERROR")
                return {}
                
        except Exception as e:
            log(f"获取群聊信息异常: {str(e)}", "ERROR")
            return {}
    
    def get_all_recent_groups(self) -> List[str]:
        """
        获取所有最近群聊列表
        
        Returns:
            List[str]: 最近群聊名称列表
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return []
                
            groups = self.wx.GetAllRecentGroups()
            if isinstance(groups, list):
                log(f"获取最近群聊列表成功，共 {len(groups)} 个群聊")
                return groups
            else:
                log("获取最近群聊列表失败", "ERROR")
                return []
                
        except Exception as e:
            log(f"获取最近群聊列表异常: {str(e)}", "ERROR")
            return []
    
    def get_contact_groups(self, speed: int = 1, interval: float = 0.1) -> List[str]:
        """
        获取通讯录中的群聊列表
        
        Args:
            speed: 滚动速度
            interval: 滚动时间间隔
            
        Returns:
            List[str]: 通讯录群聊列表
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return []
                
            groups = self.wx.GetContactGroups(speed=speed, interval=interval)
            if isinstance(groups, list):
                log(f"获取通讯录群聊列表成功，共 {len(groups)} 个群聊")
                return groups
            else:
                log("获取通讯录群聊列表失败", "ERROR")
                return []
                
        except Exception as e:
            log(f"获取通讯录群聊列表异常: {str(e)}", "ERROR")
            return []
    
    def add_friend_from_group(self, group_name: str, member_index: int, 
                             addmsg: str = None, remark: str = None, 
                             tags: List[str] = None, permission: str = "仅聊天") -> bool:
        """
        从群聊中添加好友
        
        Args:
            group_name: 群聊名称
            member_index: 群聊成员索引
            addmsg: 申请理由
            remark: 添加好友后的备注名
            tags: 添加好友后的标签
            permission: 添加好友后的权限 ('朋友圈' 或 '仅聊天')
            
        Returns:
            bool: 是否发起申请成功
        """
        try:
            if not self.wx:
                log("微信实例未初始化", "ERROR")
                return False
                
            # 打开群聊窗口
            self.wx.ChatWith(group_name)

            # 获取群聊子窗口实例
            chat = self.wx.GetSubWindow(group_name)
            if not chat:
                log(f"无法获取群聊子窗口: {group_name}", "ERROR")
                return False

            # 从群聊中添加好友
            result = chat.AddFriendFromGroup(
                index=member_index,
                who=group_name,
                addmsg=addmsg,
                remark=remark,
                tags=tags,
                permission=permission
            )
            
            if result and result.success:
                log(f"成功从群聊 {group_name} 发起好友申请（索引: {member_index}）")
                return True
            else:
                error_msg = result.message if result else "未知错误"
                log(f"从群聊添加好友失败: {error_msg}", "ERROR")
                return False
                
        except Exception as e:
            log(f"从群聊添加好友异常: {str(e)}", "ERROR")
            return False
    
    def get_cached_group_members(self, group_name: str, max_age: int = 3600) -> List[str]:
        """
        获取缓存的群聊成员列表（如果缓存过期则重新获取）
        
        Args:
            group_name: 群聊名称
            max_age: 缓存最大有效期（秒），默认1小时
            
        Returns:
            List[str]: 群聊成员列表
        """
        try:
            current_time = time.time()
            
            # 检查缓存是否存在且未过期
            if (group_name in self.group_cache and 
                current_time - self.group_cache[group_name]['update_time'] < max_age):
                log(f"使用缓存的群聊 {group_name} 成员列表")
                return self.group_cache[group_name]['members']
            
            # 缓存不存在或已过期，重新获取
            log(f"缓存过期或不存在，重新获取群聊 {group_name} 成员列表")
            return self.get_group_members(group_name)
            
        except Exception as e:
            log(f"获取缓存群聊成员异常: {str(e)}", "ERROR")
            return []
    
    def clear_group_cache(self, group_name: str = None):
        """
        清除群聊缓存
        
        Args:
            group_name: 群聊名称，如果为 None 则清除所有缓存
        """
        try:
            if group_name:
                if group_name in self.group_cache:
                    del self.group_cache[group_name]
                    log(f"已清除群聊 {group_name} 的缓存")
                else:
                    log(f"群聊 {group_name} 无缓存数据")
            else:
                self.group_cache.clear()
                log("已清除所有群聊缓存")
                
        except Exception as e:
            log(f"清除群聊缓存异常: {str(e)}", "ERROR")

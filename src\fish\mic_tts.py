"""通过 VB 虚拟麦克风实时播放 TTS（基于 fish_audio_sdk）。

从 root 目录迁移而来，逻辑保持一致，仅路径调整。
"""

from __future__ import annotations

import os
import sys

import sounddevice as sd
from dotenv import load_dotenv
from fish_audio_sdk import WebSocketSession, TTSRequest

if not load_dotenv():
    load_dotenv("env_example.txt")

API_KEY = os.getenv("FISH_API_KEY")
MODEL_ID = os.getenv("MODEL_ID")
if not API_KEY:
    raise RuntimeError("请在 .env 或 env_example.txt 中设置 FISH_API_KEY")

VB_DEVICE_NAME = os.getenv("VB_DEVICE", "VB-Audio")

SAMPLE_RATE = 44100
CHANNELS = 1
DTYPE = "int16"


def find_output_device(keyword: str) -> int | None:
    for idx, dev in enumerate(sd.query_devices()):
        if dev["max_output_channels"] > 0 and keyword.lower() in dev["name"].lower():
            return idx
    return None


def main(text: str | None = None):
    if text is None:
        text = " ".join(sys.argv[1:]) if len(sys.argv) > 1 else input("文本> ")

    device_idx = find_output_device(VB_DEVICE_NAME)
    if device_idx is None:
        raise RuntimeError(f"未找到包含关键词 '{VB_DEVICE_NAME}' 的 VB 虚拟麦克风设备")

    print("[mic_tts] 输出设备:", sd.query_devices(device_idx)["name"])

    session = WebSocketSession(API_KEY)
    request = TTSRequest(text="", reference_id=MODEL_ID, format="pcm", sample_rate=SAMPLE_RATE)

    def one_shot():
        yield text + " "

    with sd.RawOutputStream(
        samplerate=SAMPLE_RATE,
        channels=CHANNELS,
        dtype=DTYPE,
        device=device_idx,
        blocksize=0,
    ) as stream:
        for chunk in session.tts(request, one_shot(), backend="speech-1.6"):
            if chunk:
                stream.write(chunk)


if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
情感状态管理模块
为AI机器人添加情感状态系统，让AI根据对话内容和时间产生情感变化
"""

import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from utils.logger import log


class EmotionType(Enum):
    """情感类型枚举"""
    HAPPY = "happy"          # 开心
    EXCITED = "excited"      # 兴奋
    CALM = "calm"           # 平静
    SAD = "sad"             # 悲伤
    ANGRY = "angry"         # 愤怒
    ANXIOUS = "anxious"     # 焦虑
    SURPRISED = "surprised"  # 惊讶
    CONFUSED = "confused"    # 困惑
    TIRED = "tired"         # 疲惫
    ENERGETIC = "energetic"  # 精力充沛
    LONELY = "lonely"       # 孤独
    GRATEFUL = "grateful"   # 感激
    CURIOUS = "curious"     # 好奇
    BORED = "bored"         # 无聊
    NEUTRAL = "neutral"     # 中性


class EmotionIntensity(Enum):
    """情感强度枚举"""
    VERY_LOW = 1    # 非常低
    LOW = 2         # 低
    MEDIUM = 3      # 中等
    HIGH = 4        # 高
    VERY_HIGH = 5   # 非常高


@dataclass
class EmotionState:
    """情感状态数据类"""
    emotion_type: EmotionType
    intensity: EmotionIntensity
    start_time: datetime
    duration: int  # 持续时间（秒）
    decay_rate: float = 0.1  # 衰减率
    trigger_reason: str = ""  # 触发原因
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'emotion_type': self.emotion_type.value,
            'intensity': self.intensity.value,
            'start_time': self.start_time.isoformat(),
            'duration': self.duration,
            'decay_rate': self.decay_rate,
            'trigger_reason': self.trigger_reason
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'EmotionState':
        """从字典创建"""
        return cls(
            emotion_type=EmotionType(data['emotion_type']),
            intensity=EmotionIntensity(data['intensity']),
            start_time=datetime.fromisoformat(data['start_time']),
            duration=data['duration'],
            decay_rate=data.get('decay_rate', 0.1),
            trigger_reason=data.get('trigger_reason', '')
        )
    
    def get_current_intensity(self) -> float:
        """获取当前强度（考虑时间衰减）"""
        elapsed = (datetime.now() - self.start_time).total_seconds()
        if elapsed >= self.duration:
            return 0.0
        
        # 计算衰减后的强度
        decay_factor = 1.0 - (elapsed / self.duration) * self.decay_rate
        return max(0.0, self.intensity.value * decay_factor)
    
    def is_expired(self) -> bool:
        """检查情感状态是否已过期"""
        elapsed = (datetime.now() - self.start_time).total_seconds()
        return elapsed >= self.duration or self.get_current_intensity() <= 0.1


class EmotionStateManager:
    """情感状态管理器"""
    
    def __init__(self, user_id: str = "default"):
        self.user_id = user_id
        self.current_emotions: List[EmotionState] = []
        self.emotion_history: List[EmotionState] = []
        self.base_emotion = EmotionType.NEUTRAL
        self.emotion_file = f"emotion_state_{user_id}.json"
        
        # 情感转换规则
        self.emotion_transitions = self._init_emotion_transitions()
        
        # 加载保存的情感状态
        self.load_emotion_state()
    
    def _init_emotion_transitions(self) -> Dict[str, Dict[EmotionType, float]]:
        """初始化情感转换规则"""
        return {
            'positive_high': {
                EmotionType.HAPPY: 0.4,
                EmotionType.EXCITED: 0.3,
                EmotionType.ENERGETIC: 0.2,
                EmotionType.GRATEFUL: 0.1
            },
            'positive_medium': {
                EmotionType.HAPPY: 0.5,
                EmotionType.CALM: 0.3,
                EmotionType.GRATEFUL: 0.2
            },
            'positive_low': {
                EmotionType.CALM: 0.6,
                EmotionType.HAPPY: 0.4
            },
            'negative_high': {
                EmotionType.SAD: 0.3,
                EmotionType.ANGRY: 0.3,
                EmotionType.ANXIOUS: 0.2,
                EmotionType.CONFUSED: 0.2
            },
            'negative_medium': {
                EmotionType.SAD: 0.4,
                EmotionType.ANXIOUS: 0.3,
                EmotionType.CONFUSED: 0.3
            },
            'negative_low': {
                EmotionType.CALM: 0.5,
                EmotionType.SAD: 0.3,
                EmotionType.CONFUSED: 0.2
            },
            'neutral': {
                EmotionType.CALM: 0.5,
                EmotionType.CURIOUS: 0.3,
                EmotionType.NEUTRAL: 0.2
            }
        }
    
    def load_emotion_state(self):
        """加载保存的情感状态"""
        try:
            with open(self.emotion_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 加载当前情感状态
            if 'current_emotions' in data:
                self.current_emotions = [
                    EmotionState.from_dict(emotion_data) 
                    for emotion_data in data['current_emotions']
                ]
                # 清理过期的情感状态
                self.cleanup_expired_emotions()
                
            # 加载基础情感
            if 'base_emotion' in data:
                self.base_emotion = EmotionType(data['base_emotion'])
                
            log(f"情感状态加载成功: {self.user_id}")
            
        except FileNotFoundError:
            log(f"情感状态文件不存在，使用默认状态: {self.user_id}")
        except Exception as e:
            log(f"加载情感状态失败: {str(e)}", "ERROR")
    
    def save_emotion_state(self):
        """保存情感状态"""
        try:
            data = {
                'user_id': self.user_id,
                'base_emotion': self.base_emotion.value,
                'current_emotions': [emotion.to_dict() for emotion in self.current_emotions],
                'last_update': datetime.now().isoformat()
            }
            
            with open(self.emotion_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            log(f"情感状态保存成功: {self.user_id}")
            
        except Exception as e:
            log(f"保存情感状态失败: {str(e)}", "ERROR")
    
    def cleanup_expired_emotions(self):
        """清理过期的情感状态"""
        before_count = len(self.current_emotions)
        self.current_emotions = [
            emotion for emotion in self.current_emotions 
            if not emotion.is_expired()
        ]
        after_count = len(self.current_emotions)
        
        if before_count != after_count:
            log(f"清理过期情感状态: {before_count} -> {after_count}")
    
    def get_dominant_emotion(self) -> Tuple[EmotionType, float]:
        """获取当前主导情感"""
        self.cleanup_expired_emotions()
        
        if not self.current_emotions:
            return self.base_emotion, 1.0
        
        # 计算各种情感的总强度
        emotion_weights = {}
        for emotion in self.current_emotions:
            current_intensity = emotion.get_current_intensity()
            if current_intensity > 0:
                if emotion.emotion_type not in emotion_weights:
                    emotion_weights[emotion.emotion_type] = 0
                emotion_weights[emotion.emotion_type] += current_intensity
        
        if not emotion_weights:
            return self.base_emotion, 1.0
        
        # 找到强度最高的情感
        dominant_emotion = max(emotion_weights.items(), key=lambda x: x[1])
        return dominant_emotion[0], min(dominant_emotion[1] / 5.0, 1.0)  # 归一化到0-1
    
    def add_emotion(self, emotion_type: EmotionType, intensity: EmotionIntensity, 
                   duration: int = 3600, trigger_reason: str = ""):
        """添加新的情感状态"""
        new_emotion = EmotionState(
            emotion_type=emotion_type,
            intensity=intensity,
            start_time=datetime.now(),
            duration=duration,
            trigger_reason=trigger_reason
        )
        
        self.current_emotions.append(new_emotion)
        self.emotion_history.append(new_emotion)
        
        # 限制当前情感数量
        if len(self.current_emotions) > 5:
            self.current_emotions = self.current_emotions[-5:]
        
        # 限制历史记录数量
        if len(self.emotion_history) > 50:
            self.emotion_history = self.emotion_history[-50:]
        
        self.save_emotion_state()
        
        log(f"添加情感状态: {emotion_type.value} ({intensity.value}) - {trigger_reason}")

    def process_sentiment_analysis(self, sentiment_result: dict, user_message: str = ""):
        """根据情感分析结果更新情感状态"""
        try:
            label = sentiment_result.get('label', 'neutral')
            intensity = sentiment_result.get('intensity', 'low')
            score = sentiment_result.get('score', 0.5)

            # 确定情感强度等级
            if intensity == 'high':
                emotion_intensity = EmotionIntensity.HIGH if score >= 0.8 else EmotionIntensity.MEDIUM
            elif intensity == 'medium':
                emotion_intensity = EmotionIntensity.MEDIUM
            else:
                emotion_intensity = EmotionIntensity.LOW

            # 根据情感标签和强度选择情感类型
            emotion_key = f"{label}_{intensity}"
            if emotion_key in self.emotion_transitions:
                possible_emotions = self.emotion_transitions[emotion_key]
                # 根据权重随机选择情感类型
                emotion_type = self._weighted_random_choice(possible_emotions)
            else:
                # 默认情感映射
                emotion_type = self._map_sentiment_to_emotion(label, intensity)

            # 计算持续时间（基于强度和分数）
            base_duration = 1800  # 30分钟基础时间
            duration_multiplier = 1.0 + score
            duration = int(base_duration * duration_multiplier)

            # 添加情感状态
            trigger_reason = f"用户消息情感分析: {label}({intensity}) - {user_message[:30]}..."
            self.add_emotion(emotion_type, emotion_intensity, duration, trigger_reason)

            log(f"情感状态更新: {emotion_type.value} 强度:{emotion_intensity.value} 持续:{duration}秒")

        except Exception as e:
            log(f"处理情感分析结果失败: {str(e)}", "ERROR")

    def _weighted_random_choice(self, choices: Dict[EmotionType, float]) -> EmotionType:
        """根据权重随机选择情感类型"""
        emotions = list(choices.keys())
        weights = list(choices.values())
        return random.choices(emotions, weights=weights)[0]

    def _map_sentiment_to_emotion(self, label: str, intensity: str) -> EmotionType:
        """将情感标签映射到情感类型"""
        mapping = {
            'positive': {
                'high': EmotionType.EXCITED,
                'medium': EmotionType.HAPPY,
                'low': EmotionType.CALM
            },
            'negative': {
                'high': EmotionType.SAD,
                'medium': EmotionType.ANXIOUS,
                'low': EmotionType.CONFUSED
            },
            'neutral': {
                'high': EmotionType.CURIOUS,
                'medium': EmotionType.CALM,
                'low': EmotionType.NEUTRAL
            }
        }

        return mapping.get(label, {}).get(intensity, EmotionType.NEUTRAL)

    def get_emotion_prompt(self) -> str:
        """获取当前情感状态的提示词"""
        dominant_emotion, intensity = self.get_dominant_emotion()

        # 情感描述映射
        emotion_descriptions = {
            EmotionType.HAPPY: {
                'low': "心情不错，比较开心",
                'medium': "很开心，充满喜悦",
                'high': "非常开心，兴高采烈"
            },
            EmotionType.EXCITED: {
                'low': "有些兴奋",
                'medium': "很兴奋，充满活力",
                'high': "极度兴奋，激动不已"
            },
            EmotionType.CALM: {
                'low': "比较平静",
                'medium': "很平静，内心安宁",
                'high': "非常平静，心如止水"
            },
            EmotionType.SAD: {
                'low': "有些难过",
                'medium': "比较难过，心情低落",
                'high': "非常难过，情绪低沉"
            },
            EmotionType.ANGRY: {
                'low': "有些不满",
                'medium': "比较生气，情绪激动",
                'high': "非常愤怒，怒火中烧"
            },
            EmotionType.ANXIOUS: {
                'low': "有些担心",
                'medium': "比较焦虑，心神不宁",
                'high': "非常焦虑，忧心忡忡"
            },
            EmotionType.SURPRISED: {
                'low': "有些惊讶",
                'medium': "很惊讶，出乎意料",
                'high': "非常惊讶，震惊不已"
            },
            EmotionType.CONFUSED: {
                'low': "有些困惑",
                'medium': "比较困惑，不太理解",
                'high': "非常困惑，完全不明白"
            },
            EmotionType.TIRED: {
                'low': "有些疲惫",
                'medium': "比较累，精神不振",
                'high': "非常疲惫，筋疲力尽"
            },
            EmotionType.ENERGETIC: {
                'low': "精神还不错",
                'medium': "精力充沛，活力满满",
                'high': "精力旺盛，充满干劲"
            },
            EmotionType.LONELY: {
                'low': "有些孤单",
                'medium': "比较孤独，渴望陪伴",
                'high': "非常孤独，内心空虚"
            },
            EmotionType.GRATEFUL: {
                'low': "心存感激",
                'medium': "很感激，心怀感恩",
                'high': "非常感激，感恩不尽"
            },
            EmotionType.CURIOUS: {
                'low': "有些好奇",
                'medium': "很好奇，想要了解",
                'high': "非常好奇，求知欲强"
            },
            EmotionType.BORED: {
                'low': "有些无聊",
                'medium': "比较无聊，缺乏兴趣",
                'high': "非常无聊，百无聊赖"
            },
            EmotionType.NEUTRAL: {
                'low': "情绪平和",
                'medium': "心情平静",
                'high': "情绪稳定"
            }
        }

        # 确定强度级别
        if intensity >= 0.8:
            intensity_level = 'high'
        elif intensity >= 0.5:
            intensity_level = 'medium'
        else:
            intensity_level = 'low'

        description = emotion_descriptions.get(dominant_emotion, {}).get(
            intensity_level, "情绪平和"
        )

        # 构建情感提示词
        emotion_prompt = f"""[当前情感状态]
情感类型: {dominant_emotion.value}
情感强度: {intensity:.2f}
状态描述: {description}

请根据当前的情感状态调整回复的语气和内容，让回复更加自然和真实。"""

        return emotion_prompt

    def get_emotion_stats(self) -> dict:
        """获取情感状态统计信息"""
        self.cleanup_expired_emotions()

        dominant_emotion, intensity = self.get_dominant_emotion()

        # 统计当前活跃情感
        active_emotions = {}
        for emotion in self.current_emotions:
            current_intensity = emotion.get_current_intensity()
            if current_intensity > 0:
                emotion_name = emotion.emotion_type.value
                if emotion_name not in active_emotions:
                    active_emotions[emotion_name] = []
                active_emotions[emotion_name].append({
                    'intensity': current_intensity,
                    'start_time': emotion.start_time.strftime('%H:%M:%S'),
                    'trigger': emotion.trigger_reason[:50] + '...' if len(emotion.trigger_reason) > 50 else emotion.trigger_reason
                })

        return {
            'dominant_emotion': dominant_emotion.value,
            'dominant_intensity': intensity,
            'base_emotion': self.base_emotion.value,
            'active_emotions_count': len(self.current_emotions),
            'active_emotions': active_emotions,
            'total_history_count': len(self.emotion_history)
        }

    def reset_emotions(self):
        """重置所有情感状态"""
        self.current_emotions.clear()
        self.base_emotion = EmotionType.NEUTRAL
        self.save_emotion_state()
        log(f"情感状态已重置: {self.user_id}")

    def set_base_emotion(self, emotion_type: EmotionType):
        """设置基础情感"""
        self.base_emotion = emotion_type
        self.save_emotion_state()
        log(f"基础情感已设置为: {emotion_type.value}")

    def apply_time_decay(self):
        """应用时间衰减（定期调用）"""
        self.cleanup_expired_emotions()

        # 如果没有活跃情感，逐渐回归基础情感
        if not self.current_emotions:
            # 可以在这里添加自然的情感变化逻辑
            # 比如根据时间段添加不同的情感倾向
            current_hour = datetime.now().hour

            if 6 <= current_hour < 9:  # 早晨
                if random.random() < 0.1:  # 10%概率
                    self.add_emotion(EmotionType.ENERGETIC, EmotionIntensity.LOW,
                                   1800, "早晨时光，精神饱满")
            elif 12 <= current_hour < 14:  # 午餐时间
                if random.random() < 0.05:  # 5%概率
                    self.add_emotion(EmotionType.HAPPY, EmotionIntensity.LOW,
                                   900, "午餐时间，心情愉悦")
            elif 18 <= current_hour < 20:  # 晚餐时间
                if random.random() < 0.05:  # 5%概率
                    self.add_emotion(EmotionType.CALM, EmotionIntensity.LOW,
                                   1200, "晚餐时光，心情平静")
            elif 22 <= current_hour or current_hour < 6:  # 深夜/凌晨
                if random.random() < 0.08:  # 8%概率
                    self.add_emotion(EmotionType.TIRED, EmotionIntensity.LOW,
                                   1800, "深夜时分，有些疲惫")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
括号过滤修复脚本
修复中文括号和中文方括号的过滤问题
"""

import re

def filter_brackets_fixed(text: str, filter_mode: str = "remove") -> str:
    """
    修复后的括号过滤函数
    支持中文括号、方括号、花括号和中文方括号
    
    Args:
        text: 要过滤的文本
        filter_mode: 过滤模式 ("remove" 或 "replace")
        
    Returns:
        str: 过滤后的文本
    """
    if not text:
        return text
    
    try:
        # 定义所有需要过滤的括号类型
        patterns = [
            r'\([^)]*\)',    # 圆括号（中文和英文）
            r'\[[^\]]*\]',   # 方括号（英文）
            r'\{[^}]*\}',    # 花括号（英文）
            r'【[^】]*】',    # 中文方括号
        ]
        
        filtered_text = text
        removed_count = 0
        
        for pattern in patterns:
            matches = re.findall(pattern, filtered_text)
            removed_count += len(matches)
            
            if filter_mode == "remove":
                # 直接删除
                filtered_text = re.sub(pattern, '', filtered_text)
            elif filter_mode == "replace":
                # 替换为[已过滤]
                filtered_text = re.sub(pattern, '[已过滤]', filtered_text)
        
        # 清理多余的空格
        filtered_text = re.sub(r'\s+', ' ', filtered_text).strip()
        
        return filtered_text
        
    except Exception as e:
        print(f"括号过滤失败: {e}")
        return text

def test_fixed_filter():
    """测试修复后的过滤功能"""
    print("=== 修复后的括号过滤测试 ===\n")
    
    test_cases = [
        {
            'name': '中文括号',
            'input': '你好（这是中文括号内容）世界',
            'expected': '你好世界'
        },
        {
            'name': '英文方括号',
            'input': '你好[这是英文方括号内容]世界',
            'expected': '你好世界'
        },
        {
            'name': '英文花括号',
            'input': '你好{这是英文花括号内容}世界',
            'expected': '你好世界'
        },
        {
            'name': '中文方括号',
            'input': '你好【这是中文方括号内容】世界',
            'expected': '你好世界'
        },
        {
            'name': '混合括号',
            'input': '你好（中文）[英文]{花括号}【中文方括号】世界',
            'expected': '你好世界'
        },
        {
            'name': '嵌套括号',
            'input': '你好（外面（里面）外面）世界',
            'expected': '你好世界'
        },
        {
            'name': '空括号',
            'input': '你好（）[]{}【】世界',
            'expected': '你好世界'
        }
    ]
    
    print("测试移除模式:")
    print("-" * 50)
    
    for case in test_cases:
        result = filter_brackets_fixed(case['input'], "remove")
        status = "PASS" if result == case['expected'] else "FAIL"
        print(f"{status}: {case['name']}")
        print(f"   输入: {case['input']}")
        print(f"   期望: {case['expected']}")
        print(f"   实际: {result}")
        if status == "FAIL":
            print(f"   [ERROR] 不匹配!")
        print()

def show_regex_demonstration():
    """演示正则表达式的工作原理"""
    print("=== 正则表达式演示 ===\n")
    
    text = '你好（中文）[英文]{花括号}【中文方括号】世界'
    print(f"原始文本: {text}")
    print()
    
    patterns = {
        '中文括号': r'\([^)]*\)',
        '英文方括号': r'\[[^\]]*\]',
        '英文花括号': r'\{[^}]*\}',
        '中文方括号': r'【[^】]*】'
    }
    
    for name, pattern in patterns.items():
        matches = re.findall(pattern, text)
        filtered = re.sub(pattern, '', text)
        print(f"{name}:")
        print(f"  模式: {pattern}")
        print(f"  匹配: {matches}")
        print(f"  过滤后: {filtered}")
        print()

if __name__ == "__main__":
    test_fixed_filter()
    show_regex_demonstration()
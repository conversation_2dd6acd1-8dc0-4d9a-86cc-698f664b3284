﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="KouriChat - 配置中心">
    <meta name="keywords" content="AI,KouriChat">
    <meta name="theme-color" content="#6366f1">
    <title>KouriChat - 配置中心</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdmirror.com/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/static/mom.ico">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #4f46e5;
            --background-color: #f8fafc;
            --text-color: #1e293b;
            --card-bg: rgba(255, 255, 255, 0.8);
            --card-border: rgba(255, 255, 255, 0.5);
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        [data-bs-theme="dark"] {
            --primary-color: #818cf8;
            --secondary-color: #6366f1;
            --background-color: #0f172a;
            --text-color: #e2e8f0;
            --card-bg: rgba(30, 41, 59, 0.8);
            --card-border: rgba(255, 255, 255, 0.1);
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }
        html,body{
            height:100%;
            margin: 0;
        }
        body {
            background: var(--background-color);
            color: var(--text-color);
            transition: all 0.3s ease;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
        }

        .config-section {
            background: var(--card-bg);
            -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
            border-radius: 1rem;
            border: 1px solid var(--card-border);
            box-shadow: var(--card-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            opacity: 0;
            transform: translateY(20px);
            animation: slideUp 0.5s ease forwards;
            animation-delay: 0.1s;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 添加卡片微妙的装饰效果 */
        .config-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            opacity: 0.5;
        }

        .form-control, .form-select {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid var(--card-border) !important;
            background: var(--card-bg);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
            outline: none;
        }

        .form-label {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .badge-info {
            background: var(--primary-color);
            cursor: pointer;
        }

        .theme-switcher {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            z-index: 1000;
        }

        .accordion-button {
            background: transparent;
            border: none;
        }

        .accordion-button:not(.collapsed) {
            background: rgba(var(--bs-primary-rgb), 0.1);
            color: var(--primary-color);
        }

        .accordion-item {
            background: transparent;
            border-color: var(--card-border);
        }

        .toast {
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .form-check-label {
            margin-top: 0;
        }

        /* 人设选择下拉栏样式 */
        select[name="AVATAR_DIR"] {
            max-height: 300px;
            overflow-y: auto;
        }

        /* 添加导航栏样式，与dashboard.html保持一致 */
        .navbar {
            background: var(--card-bg) !important;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--card-border);
        }

        /* 输入框组样式 */
        .input-group {
            border: 1px solid var(--card-border);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .input-group .form-control {
            border: none !important;
        }

        /* 配置项容器样式 */
        .config-item {
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 响应式布局 */
        @media (max-width: 700px) {
            .config-section {
                padding: 1rem;
            }
        }

        /* 密码输入框样式 */
        .password-input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
        }

        .password-toggle:hover {
            opacity: 1;
        }

        /* 修改左右两栏的间距 */
        @media (min-width: 768px) {
            .col-md-6.pe-md-2 {
                padding-right: 1.5rem !important;  /* 增加右边距 */
            }

            .col-md-6.ps-md-2 {
                padding-left: 1.5rem !important;   /* 增加左边距 */
            }
        }

        /* 修改响应式布局下的间距 */
        @media (max-width: 767px) {
            .col-md-6 {
                margin-bottom: 2rem;  /* 增加上下间距 */
            }

            .config-section {
                margin-bottom: 0;  /* 移除原有的底部间距 */
            }

            /* 为了让背景更好地显示，增加内容区域的内边距 */
            main.container-fluid {
                padding: 2rem 1rem;  /* 调整移动端的内边距 */
            }

            /* 为底部保存按钮留出空间 */
            main.container-fluid {
                padding-bottom: 5rem;  /* 底部留出更多空间 */
            }
        }

        /* 优化桌面端布局 */
        @media (min-width: 768px) {
            .col-md-6.pe-md-2 {
                padding-right: 1.5rem !important;
            }

            .col-md-6.ps-md-2 {
                padding-left: 1.5rem !important;
            }

            main.container-fluid {
                padding: 2rem;
                padding-bottom: 5rem;  /* 为底部保存按钮留出空间 */
            }
        }

        /* 修改主容器的内边距 */
        main.container-fluid {
            padding: 2rem;  /* 增加整体内边距 */
        }

        /* 添加相同的基础样式 */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        main {
            flex: 1 0 auto;
            width: 100%;
            padding: 2rem 0;
        }

        /* 添加通知动画 */
        .toast {
            transition: all 0.3s ease;
            transform: translateY(-20px);
            opacity: 0;
        }

        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }

        /* 优化按钮悬停效果 */
        .btn-outline-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* 添加输入框过渡效果 */
        #customApiInput {
            transition: all 0.3s ease;
        }

        #customApiInput.show {
            transform: translateY(0);
            opacity: 1;
        }

        #customApiInput.hide {
            transform: translateY(-10px);
            opacity: 0;
        }

        /* 添加通知容器样式 */
        .notification-container {
            z-index: 1050;
        }

        .accordion-button {
            background: transparent !important;
        }

        .accordion-button:not(.collapsed) {
            color: rgb(147, 151, 255) !important;
        }

        .list-group-item {
            border: none;
            margin-bottom: 8px;
            border-radius: 8px !important;
            transition: all 0.3s ease;
        }

        /* 定时任务的列表项使用主题样式 */
        #schedule-settings .list-group-item {
            background: var(--card-bg);
            color: var(--text-color);
            border: 1px solid var(--card-border);
        }

        #schedule-settings .list-group-item:hover {
            background: var(--card-bg);
            transform: translateX(5px);
            opacity: 0.9;
        }

        /* 监听用户列表使用默认颜色 */
        #selected_users_LISTEN_LIST .list-group-item {
            background: var(--bs-list-group-bg);
            color: var(--bs-body-color);
        }

        /* 移除深色模式特定样式，使用主题变量 */

        /* 添加日期选择按钮组的响应式样式 */
        .btn-group.flex-wrap {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .btn-group.flex-wrap .btn {
            flex: 1 1 calc(14.28% - 0.25rem);
            min-width: 40px;
            padding: 0.375rem 0.5rem;
            font-size: 0.875rem;
            text-align: center;
        }

        /* 在小屏幕上调整按钮大小 */
        @media (max-width: 768px) {
            .btn-group.flex-wrap .btn {
                flex: 1 1 calc(33.33% - 0.25rem);
                min-width: 30px;
                padding: 0.25rem 0.375rem;
                font-size: 0.75rem;
            }
        }

        /* 在更小的屏幕上进一步调整 */
        @media (max-width: 480px) {
            .btn-group.flex-wrap .btn {
                flex: 1 1 calc(50% - 0.25rem);
                min-width: 25px;
                padding: 0.25rem;
                font-size: 0.75rem;
            }
        }

        /* OpenAI TTS 专用滑块样式 - 统一粉色渐变主题 */
        input[type="range"].openai-tts-temp-slider {
            -webkit-appearance: none !important;
            width: 100% !important;
            height: 8px !important;
            border-radius: 4px !important;
            background: linear-gradient(to right,
                rgb(236, 72, 153) 0%,
                rgb(244, 114, 182) 50%,
                rgb(251, 146, 207) 100%) !important;
            outline: none !important;
            transition: opacity 0.2s !important;
        }

        input[type="range"].openai-tts-top-p-slider {
            -webkit-appearance: none !important;
            width: 100% !important;
            height: 8px !important;
            border-radius: 4px !important;
            background: linear-gradient(to right,
                rgb(236, 72, 153) 0%,
                rgb(244, 114, 182) 50%,
                rgb(251, 146, 207) 100%) !important;
            outline: none !important;
            transition: opacity 0.2s !important;
        }

        input[type="range"].openai-tts-speed-slider {
            -webkit-appearance: none !important;
            width: 100% !important;
            height: 8px !important;
            border-radius: 4px !important;
            background: linear-gradient(to right,
                rgb(236, 72, 153) 0%,
                rgb(244, 114, 182) 50%,
                rgb(251, 146, 207) 100%) !important;
            outline: none !important;
            transition: opacity 0.2s !important;
        }

        /* 全局温度滑块样式 */
        .temperature-slider {
            -webkit-appearance: none;
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right,
                rgb(13, 110, 253) 0%,
                rgb(13, 202, 240) 50%,
                rgb(253, 126, 20) 100%);
            outline: none;
            transition: opacity 0.2s;
        }

        /* TTS 拖拽按钮样式 - 统一粉色主题 */
        input[type="range"]#OPENAI_TTS_TEMPERATURE_slider::-webkit-slider-thumb {
            -webkit-appearance: none !important;
            width: 20px !important;
            height: 20px !important;
            border-radius: 50% !important;
            background: var(--card-bg) !important;
            border: 2px solid rgb(236, 72, 153) !important;
            cursor: pointer !important;
            box-shadow: 0 0 8px rgba(236, 72, 153, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        input[type="range"]#OPENAI_TTS_TEMPERATURE_slider::-webkit-slider-thumb:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 0 12px rgba(236, 72, 153, 0.6) !important;
        }

        input[type="range"]#OPENAI_TTS_TOP_P_slider::-webkit-slider-thumb {
            -webkit-appearance: none !important;
            width: 20px !important;
            height: 20px !important;
            border-radius: 50% !important;
            background: var(--card-bg) !important;
            border: 2px solid rgb(236, 72, 153) !important;
            cursor: pointer !important;
            box-shadow: 0 0 8px rgba(236, 72, 153, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        input[type="range"]#OPENAI_TTS_TOP_P_slider::-webkit-slider-thumb:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 0 12px rgba(236, 72, 153, 0.6) !important;
        }

        input[type="range"]#OPENAI_TTS_SPEED_slider::-webkit-slider-thumb {
            -webkit-appearance: none !important;
            width: 20px !important;
            height: 20px !important;
            border-radius: 50% !important;
            background: var(--card-bg) !important;
            border: 2px solid rgb(236, 72, 153) !important;
            cursor: pointer !important;
            box-shadow: 0 0 8px rgba(236, 72, 153, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        input[type="range"]#OPENAI_TTS_SPEED_slider::-webkit-slider-thumb:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 0 12px rgba(236, 72, 153, 0.6) !important;
        }

        /* TTS滑块样式 - 统一粉色渐变主题 - 使用更高优先级的选择器 */
        input[type="range"].openai-tts-temp-slider,
        input[type="range"].openai-tts-top-p-slider,
        input[type="range"].openai-tts-speed-slider,
        input[type="range"]#OPENAI_TTS_TEMPERATURE_slider,
        input[type="range"]#OPENAI_TTS_TOP_P_slider,
        input[type="range"]#OPENAI_TTS_SPEED_slider {
            -webkit-appearance: none !important;
            width: 100% !important;
            height: 8px !important;
            border-radius: 4px !important;
            background: linear-gradient(to right,
                rgb(236, 72, 153) 0%,
                rgb(244, 114, 182) 50%,
                rgb(251, 146, 207) 100%) !important;
            outline: none !important;
            transition: opacity 0.2s !important;
            border: none !important;
        }



        /* Fish API滑块拖拽按钮通用样式 */
        .fish-api-temp-slider::-webkit-slider-thumb,
        .fish-api-top-p-slider::-webkit-slider-thumb,
        .fish-api-speed-slider::-webkit-slider-thumb {
            -webkit-appearance: none !important;
            width: 20px !important;
            height: 20px !important;
            border-radius: 50% !important;
            background: var(--card-bg) !important;
            border: 2px solid rgb(236, 72, 153) !important;
            cursor: pointer !important;
            box-shadow: 0 0 8px rgba(236, 72, 153, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        .fish-api-temp-slider::-webkit-slider-thumb:hover,
        .fish-api-top-p-slider::-webkit-slider-thumb:hover,
        .fish-api-speed-slider::-webkit-slider-thumb:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 0 12px rgba(236, 72, 153, 0.6) !important;
        }

        /* Firefox 浏览器支持 */
        .fish-api-temp-slider::-moz-range-track,
        .fish-api-top-p-slider::-moz-range-track,
        .fish-api-speed-slider::-moz-range-track {
            height: 8px !important;
            border-radius: 4px !important;
            background: linear-gradient(to right,
                rgb(236, 72, 153) 0%,
                rgb(244, 114, 182) 50%,
                rgb(251, 146, 207) 100%) !important;
            border: none !important;
        }

        .fish-api-temp-slider::-moz-range-thumb,
        .fish-api-top-p-slider::-moz-range-thumb,
        .fish-api-speed-slider::-moz-range-thumb {
            width: 20px !important;
            height: 20px !important;
            border-radius: 50% !important;
            background: var(--card-bg) !important;
            border: 2px solid rgb(236, 72, 153) !important;
            cursor: pointer !important;
            box-shadow: 0 0 8px rgba(236, 72, 153, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        .temperature-slider::-webkit-slider-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--card-bg);
            border: 2px solid rgb(13, 110, 253);
            cursor: pointer;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .temperature-slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
        }

        .temperature-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--card-bg);
            border: 2px solid rgb(13, 110, 253);
            cursor: pointer;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .temperature-value {
            transition: all 0.3s ease;
        }

        .temperature-value.updating {
            color: var(--primary-color);
            transform: scale(1.2);
        }

        /* 队列超时滑块样式 */
        .queue-timeout-slider {
            -webkit-appearance: none;
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right,
                rgb(253, 20, 20) 0%,
                rgb(253, 126, 20) 40%,
                rgb(13, 202, 240) 60%,
                rgb(13, 110, 253) 100%);
            outline: none;
            transition: opacity 0.2s;
        }

        .queue-timeout-slider::-webkit-slider-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--card-bg);
            border: 2px solid rgb(13, 110, 253);
            cursor: pointer;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .queue-timeout-slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
        }

        .queue-timeout-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--card-bg);
            border: 2px solid rgb(13, 110, 253);
            cursor: pointer;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        /* 强制覆盖TTS滑块样式 - 最高优先级 */
        input[type="range"]#OPENAI_TTS_TEMPERATURE_slider,
        input[type="range"]#OPENAI_TTS_TOP_P_slider,
        input[type="range"]#OPENAI_TTS_SPEED_slider {
            background: linear-gradient(to right,
                rgb(236, 72, 153) 0%,
                rgb(244, 114, 182) 50%,
                rgb(251, 146, 207) 100%) !important;
        }

        /* 黑夜模式下的特殊样式 */
        [data-bs-theme="dark"] .accordion-button {
            background: transparent !important;
            color: var(--text-color) !important;
        }

        [data-bs-theme="dark"] .accordion-button:not(.collapsed) {
            background: rgba(var(--bs-primary-rgb), 0.1) !important;
            color: var(--primary-color) !important;
        }

        [data-bs-theme="dark"] .accordion-item {
            background: transparent !important;
            border-color: var(--card-border) !important;
        }

        [data-bs-theme="dark"] .list-group-item {
            background: var(--card-bg) !important;
            color: var(--text-color) !important;
            border-color: var(--card-border) !important;
        }

        [data-bs-theme="dark"] .form-control,
        [data-bs-theme="dark"] .form-select {
            background: var(--card-bg) !important;
            color: var(--text-color) !important;
            border-color: var(--card-border) !important;
        }

        [data-bs-theme="dark"] .form-control:focus,
        [data-bs-theme="dark"] .form-select:focus {
            background: var(--card-bg) !important;
            color: var(--text-color) !important;
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 0.25rem rgba(129, 140, 248, 0.25) !important;
        }

        [data-bs-theme="dark"] .btn-outline-primary {
            color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        [data-bs-theme="dark"] .btn-outline-primary:hover {
            background: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: white !important;
        }

        [data-bs-theme="dark"] .toast {
            background: var(--card-bg) !important;
            color: var(--text-color) !important;
            border-color: var(--card-border) !important;
        }

        [data-bs-theme="dark"] .navbar {
            background: var(--card-bg) !important;
        }

    </style>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dark-mode.js"></script>

    <!-- Fish API滑块步长修复和样式强制应用 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('开始修复Fish API滑块步长和样式...');

        // 等待页面完全加载后再修复步长和样式
        setTimeout(() => {
            // 温度滑块 - 更新为新的OpenAI TTS ID
            const tempSlider = document.getElementById('OPENAI_TTS_TEMPERATURE_slider');
            if (tempSlider) {
                fixFishSliderStep(tempSlider);
                // 强制应用粉色渐变样式
                tempSlider.style.background = 'linear-gradient(to right, rgb(236, 72, 153) 0%, rgb(244, 114, 182) 50%, rgb(251, 146, 207) 100%)';
                console.log('TTS温度滑块步长和样式已修复');
            }

            // Top-P滑块 - 更新为新的OpenAI TTS ID
            const topPSlider = document.getElementById('OPENAI_TTS_TOP_P_slider');
            if (topPSlider) {
                fixFishSliderStep(topPSlider);
                // 强制应用粉色渐变样式
                topPSlider.style.background = 'linear-gradient(to right, rgb(236, 72, 153) 0%, rgb(244, 114, 182) 50%, rgb(251, 146, 207) 100%)';
                console.log('TTS Top-P滑块步长和样式已修复');
            }

            // 语速滑块 - 更新为新的OpenAI TTS ID
            const speedSlider = document.getElementById('OPENAI_TTS_SPEED_slider');
            if (speedSlider) {
                fixFishSliderStep(speedSlider);
                // 强制应用粉色渐变样式
                speedSlider.style.background = 'linear-gradient(to right, rgb(236, 72, 153) 0%, rgb(244, 114, 182) 50%, rgb(251, 146, 207) 100%)';
                console.log('TTS语速滑块步长和样式已修复');
            }

            console.log('OpenAI TTS滑块步长和样式修复完成');
        }, 500);
    });
    </script>

    <!-- 全局统一updateTemperature函数 - 处理所有温度滑块 -->
    <script>
    // Fish API滑块步长修复函数
    function fixFishSliderStep(slider) {
        if (!slider) return;

        // 强制设置步长为0.1
        slider.step = '0.1';
        slider.setAttribute('step', '0.1');

        console.log(`已修复滑块步长：${slider.id}，当前步长：${slider.step}`);
    }

    function updateTemperature(key, value) {
        // 将字符串转换为数字并保留一位小数
        const numValue = parseFloat(value).toFixed(1);

        // 更新显示值
        const displayElement = document.getElementById(key + '_display');
        if (displayElement) {
            displayElement.classList.add('updating');
            displayElement.textContent = numValue;
            setTimeout(() => {
                displayElement.classList.remove('updating');
            }, 300);
        }

        // 更新隐藏的实际提交值
        const inputElement = document.getElementById(key);
        if (inputElement) {
            inputElement.value = numValue;
            // 触发 change 事件以确保表单能捕获到值的变化
            const event = new Event('change', { bubbles: true });
            inputElement.dispatchEvent(event);
        }

        // 更新滑块位置（如果不是从滑块触发的事件）
        const sliderElement = document.getElementById(key + '_slider');
        if (sliderElement && sliderElement.value !== numValue) {
            sliderElement.value = numValue;
        }

        // 视觉反馈
        const container = inputElement?.closest('.mb-3') || displayElement?.closest('.mb-3');
        if (container) {
            container.style.transition = 'background-color 0.3s';
            container.style.backgroundColor = 'rgba(var(--bs-primary-rgb), 0.1)';
            setTimeout(() => {
                container.style.backgroundColor = '';
            }, 300);
        }
    }
    </script>

    <!-- 新增宏定义替代 config_item.html -->
    {% macro render_config_item(key, config) %}
<style>
    .form-label {
        transition: all 0.3s ease;
    }

    .form-label:hover {
        color: var(--primary-color);
    }

    .badge {
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: scale(1.1);
    }

    /* 列表项动画 */
    .list-group-item {
        transition: all 0.3s ease;
    }

    .list-group-item:hover {
        transform: translateX(5px);
        background: rgba(var(--bs-primary-rgb), 0.1);
    }

    /* 按钮动画 */
    .btn {
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    /* 输入框动画 */
    .form-control {
        transition: all 0.3s ease;
    }

    .form-control:focus {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
</style>
{% if config.get('type') != 'hidden' %}
<label class="form-label">
    <span class="badge badge-info rounded-pill me-2"
        data-bs-toggle="tooltip"
        title="{{ key }}">
        <i class="bi bi-info-circle"></i>
    </span>
    {{ config.description }}
</label>
{% endif %}
{% if key == 'LISTEN_LIST' %}
    <div class="mb-2">
        <div class="input-group mb-2">
            <input type="text" class="form-control"
                id="input_{{ key }}"
                placeholder="请输入要监听的用户">
            <button class="btn btn-primary" type="button"
                onclick="addNewUser('{{ key }}')"
                title="添加用户">
                添加 <i class="bi bi-plus-lg"></i>
            </button>
        </div>
        <div id="selected_users_{{ key }}" class="list-group">
            {% if config.value %}
                {% for user in config.value %}
                    {% if user %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {{ user }}
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeUser('{{ key }}', '{{ user }}')" title="删除用户">
                                <i class="bi bi-x-lg"></i>
                            </button>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        </div>
    </div>
    <input type="text" class="form-control"
        id="{{ key }}" name="{{ key }}"
        value="{{ config.value|join(',') }}"
        placeholder="多个值用英文逗号分隔"
        readonly
        style="display: none;">
{% elif key == 'GROUP_CHAT_CONFIG' %}
    <div class="mb-3">
        <!-- 添加群聊配置按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <span class="text-muted small">
                <i class="bi bi-info-circle me-1"></i>为不同群聊配置专用人设和触发词
                <br><i class="bi bi-exclamation-triangle text-warning me-1"></i>当前版本仅支持一个群聊配置，多个群聊会导致记忆混乱
            </span>
            <button type="button" id="addGroupChatBtn" class="btn btn-primary btn-sm" onclick="addGroupChatConfig()">
                <i class="bi bi-plus-lg me-1"></i>添加群聊配置
            </button>
        </div>
        
        <!-- 群聊配置列表 -->
        <div id="groupChatConfigList" class="mb-3">
            <!-- 群聊配置项将通过JavaScript动态添加 -->
        </div>
        
        <!-- 隐藏的配置数据存储 -->
        <input type="hidden" id="{{ key }}" name="{{ key }}" value="{{ config.value|default('[]') }}">
    </div>
    
    <script>
        // 群聊配置相关功能
        window.groupChatConfigs = [];
        let groupChatConfigIndex = 0;
        
        // 初始化群聊配置
        window.initGroupChatConfig = function initGroupChatConfig() {
            const configInput = document.getElementById('GROUP_CHAT_CONFIG');
            if (configInput && configInput.value) {
                try {
                    window.groupChatConfigs = JSON.parse(configInput.value);
                } catch (e) {
                    console.error('解析群聊配置失败:', e);
                    window.groupChatConfigs = [];
                }
            }
            renderGroupChatConfigList();
            updateAddGroupChatButton();
        }
        
        // 添加新的群聊配置
        function addGroupChatConfig() {
            // 检查群聊配置数量限制
            if (window.groupChatConfigs.length >= 1) {
                alert('当前版本仅支持一个群聊配置，多个群聊会导致记忆混乱。\n\n支持私聊和群聊同步进行，但群聊配置限制为1个。');
                return;
            }
            
            const newConfig = {
                id: 'group_' + Date.now(),
                groupName: '',
                avatar: '',
                triggers: [],
                enableAtTrigger: true  // 默认启用@触发
            };
            window.groupChatConfigs.push(newConfig);
            updateGroupChatConfigData();
            renderGroupChatConfigList();
            updateAddGroupChatButton();
        }
        
        // 渲染群聊配置列表
        window.renderGroupChatConfigList = function renderGroupChatConfigList() {
            const container = document.getElementById('groupChatConfigList');
            if (!container) return;
            
            if (window.groupChatConfigs.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted p-4 border rounded">
                        <i class="bi bi-chat-dots fs-2"></i>
                        <p class="mt-2 mb-0">暂无群聊配置</p>
                        <small>点击上方"添加群聊配置"按钮开始设置</small>
                        <small class="text-warning d-block mt-2">
                            <i class="bi bi-info-circle me-1"></i>
                            支持私聊和群聊同步进行，当前版本限制群聊配置为1个
                        </small>
                    </div>
                `;
                updateAddGroupChatButton();
                return;
            }
            
            container.innerHTML = window.groupChatConfigs.map((config, index) => `
                <div class="config-item mb-3 p-3 border rounded" data-config-id="${config.id}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="bi bi-chat-square-text me-2"></i>
                            群聊配置 ${index + 1}
                        </h6>
                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                onclick="removeGroupChatConfig('${config.id}')" title="删除此群聊配置">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                    
                    <div class="row">
                        <!-- 群聊名称 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="bi bi-people me-1"></i>群聊名称
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" 
                                    onchange="updateGroupChatConfigField('${config.id}', 'groupName', this.value)">
                                <option value="">请选择群聊名称</option>
                                ${getUserListOptions(config.groupName)}
                            </select>
                        </div>
                        
                        <!-- 使用的人设 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="bi bi-person-badge me-1"></i>使用的人设
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" 
                                    onchange="updateGroupChatConfigField('${config.id}', 'avatar', this.value)">
                                <option value="">请选择人设</option>
                                ${getAvatarOptions(config.avatar)}
                            </select>
                        </div>
                    </div>
                    
                    <!-- @触发开关 -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" 
                                   id="atTrigger_${config.id}" 
                                   ${config.enableAtTrigger !== false ? 'checked' : ''}
                                   onchange="updateGroupChatConfigField('${config.id}', 'enableAtTrigger', this.checked)">
                            <label class="form-check-label" for="atTrigger_${config.id}">
                                <i class="bi bi-at me-1"></i>启用@机器人名字触发
                            </label>
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            开启后，@机器人名字也会触发回复（建议保持开启）
                        </div>
                    </div>

                    <!-- 触发词配置 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-chat-left-quote me-1"></i>触发词设置
                            <span class="text-danger">*</span>
                        </label>
                        <div class="form-text mb-2">
                            <i class="bi bi-info-circle me-1"></i>
                            群聊中包含这些词语时会触发回复（如：角色名、小名、昵称等）
                        </div>
                        
                        <div class="input-group mb-2">
                            <input type="text" class="form-control" 
                                   id="triggerInput_${config.id}"
                                   placeholder="请输入触发词">
                            <button class="btn btn-primary" type="button"
                                    onclick="addTriggerWord('${config.id}')" title="添加触发词">
                                添加 <i class="bi bi-plus-lg"></i>
                            </button>
                        </div>
                        
                        <div class="list-group" id="triggerList_${config.id}">
                            ${config.triggers.map((trigger, triggerIndex) => `
                                <div class="list-group-item d-flex justify-content-between align-items-center" data-trigger-index="${triggerIndex}">
                                    ${trigger}
                                    <button type="button" class="btn btn-danger btn-sm" 
                                            onclick="removeTriggerWordByIndex('${config.id}', ${triggerIndex})" 
                                            title="删除触发词">
                                        <i class="bi bi-x-lg"></i>
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                        
                        ${config.triggers.length === 0 ? `
                            <div class="text-muted small mt-2">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                请至少添加一个触发词
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('');
            
            // 更新添加按钮状态
            updateAddGroupChatButton();
        }
        
        // 获取人设选项（需要从现有的AVATAR_DIR选项中获取）
        function getAvatarOptions(selectedValue = '') {
            const avatarSelect = document.querySelector('select[name="AVATAR_DIR"]');
            if (!avatarSelect) return '<option value="">暂无可用人设</option>';
            
            let options = '';
            for (let option of avatarSelect.options) {
                if (option.value) {
                    const avatarName = option.value.split('/').pop();
                    const selected = option.value === selectedValue ? 'selected' : '';
                    options += `<option value="${option.value}" ${selected}>${avatarName}</option>`;
                }
            }
            return options || '<option value="">暂无可用人设</option>';
        }

        // 获取用户列表选项（从LISTEN_LIST中获取）
        function getUserListOptions(selectedValue = '') {
            const userListElement = document.getElementById('selected_users_LISTEN_LIST');
            if (!userListElement) return '<option value="">暂无可用用户</option>';
            
            const userElements = userListElement.querySelectorAll('.list-group-item');
            let options = '';
            
            userElements.forEach(element => {
                const userName = element.textContent.trim().replace('×', '').trim();
                if (userName) {
                    const selected = userName === selectedValue ? 'selected' : '';
                    options += `<option value="${userName}" ${selected}>${userName}</option>`;
                }
            });
            
            return options || '<option value="">暂无可用用户</option>';
        }
        
        // 更新群聊配置字段
        function updateGroupChatConfigField(configId, field, value) {
            const config = window.groupChatConfigs.find(c => c.id === configId);
            if (config) {
                config[field] = value;
                updateGroupChatConfigData();
            }
        }

        // 更新所有群聊配置中的群聊名称选择框
        function updateGroupChatConfigSelects() {
            // 重新渲染群聊配置列表以更新选择框选项
            renderGroupChatConfigList();
        }
        
        // 添加触发词
        function addTriggerWord(configId) {
            const input = document.getElementById(`triggerInput_${configId}`);
            const triggerWord = input.value.trim();
            
            if (!triggerWord) {
                alert('请输入触发词');
                return;
            }
            
            const config = window.groupChatConfigs.find(c => c.id === configId);
            if (config) {
                if (!config.triggers.includes(triggerWord)) {
                    config.triggers.push(triggerWord);
                    updateGroupChatConfigData();
                    renderGroupChatConfigList();
                    input.value = '';
                } else {
                    alert('触发词已存在');
                }
            }
        }
        
        // 删除触发词
        function removeTriggerWord(configId, triggerWord) {
            const config = window.groupChatConfigs.find(c => c.id === configId);
            if (config) {
                config.triggers = config.triggers.filter(t => t !== triggerWord);
                updateGroupChatConfigData();
                renderGroupChatConfigList();
            }
        }
        
        // 通过索引删除触发词
        function removeTriggerWordByIndex(configId, triggerIndex) {
            const config = window.groupChatConfigs.find(c => c.id === configId);
            if (config && config.triggers[triggerIndex] !== undefined) {
                config.triggers.splice(triggerIndex, 1);
                updateGroupChatConfigData();
                renderGroupChatConfigList();
            }
        }
        
        // 删除群聊配置
        function removeGroupChatConfig(configId) {
            if (confirm('确定要删除此群聊配置吗？')) {
                window.groupChatConfigs = window.groupChatConfigs.filter(c => c.id !== configId);
                updateGroupChatConfigData();
                renderGroupChatConfigList();
                updateAddGroupChatButton();
            }
        }
        
        // 更新隐藏字段的数据
        function updateGroupChatConfigData() {
            const configInput = document.getElementById('GROUP_CHAT_CONFIG');
            if (configInput) {
                configInput.value = JSON.stringify(window.groupChatConfigs);
            }
        }
        
        // 更新添加群聊配置按钮状态
        function updateAddGroupChatButton() {
            const addButton = document.getElementById('addGroupChatBtn');
            if (!addButton) return;
            
            if (window.groupChatConfigs.length >= 1) {
                addButton.disabled = true;
                addButton.classList.remove('btn-primary');
                addButton.classList.add('btn-secondary');
                addButton.innerHTML = '<i class="bi bi-check-lg me-1"></i>已达配置上限';
                addButton.title = '当前版本仅支持一个群聊配置';
            } else {
                addButton.disabled = false;
                addButton.classList.remove('btn-secondary');
                addButton.classList.add('btn-primary');
                addButton.innerHTML = '<i class="bi bi-plus-lg me-1"></i>添加群聊配置';
                addButton.title = '添加新的群聊配置';
            }
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initGroupChatConfig, 500);
        });
    </script>
    
    <style>
        .config-item {
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.02);
        }
        
        .config-item:hover {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .config-item .list-group:empty::after {
            content: "暂无触发词，请添加";
            color: var(--bs-secondary);
            font-size: 0.875rem;
            font-style: italic;
            display: block;
            text-align: center;
            padding: 1rem;
            border: 1px dashed var(--bs-border-color);
            border-radius: 0.375rem;
            background: rgba(255, 255, 255, 0.02);
        }
        

    </style>
{% elif key == 'DEEPSEEK_BASE_URL' %}
    <div class="mb-3">
        <select class="form-select mb-2" id="api_provider_select" onchange="updateApiProvider(this.value)" aria-label="选择API提供商">
            <!-- 移除请选择服务提供商选项 -->
            <!-- 预设常用选项，减少依赖异步加载 -->
            <option value="kourichat-global" data-url="https://api.kourichat.com/v1" data-register="https://api.kourichat.com/register">KouriChat API </option>
            <option value="siliconflow" data-url="https://api.siliconflow.cn/v1/" data-register="https://www.siliconflow.cn">硅基流动 API</option>
            <option value="deepseek" data-url="https://api.deepseek.com/v1" data-register="https://platform.deepseek.com">DeepSeek API</option>
            <option value="ollama" data-url="http://localhost:11434/api/chat" data-register="https://ollama.ai">本地 Ollama</option>
            <option value="custom">自定义API提供商</option>
        </select>

        <!-- 添加自定义 API 输入框 -->
        <div id="customApiInput" class="mb-2" style="display: none;">
            <input type="text" class="form-control"
                   placeholder="请输入自定义 API 地址"
                   onchange="updateCustomApi(this.value)">
        </div>

        <!-- 注册链接容器 -->
        <div id="register_links" class="d-none">
            <!-- 注册链接将通过JavaScript动态添加 -->
        </div>

        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            readonly
            style="display: none;">
    </div>

    <script>
    let modelConfigs = null;

    // 获取配置数据 - 添加超时控制
    async function fetchModelConfigs() {
        try {
            // 设置一个2秒的超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 2000);

            const response = await fetch('/get_model_configs', {
                signal: controller.signal
            });
            clearTimeout(timeoutId);

            const data = await response.json();
            modelConfigs = data;
            // 只更新模型列表，不更新提供商列表
            updateModelSelectsFromConfig();
        } catch (error) {
            console.error('获取配置失败或超时:', error);
            // 发生错误或超时时，仍然设置初始值
            setInitialValues();
        }
    }

    // 设置初始值
    function setInitialValues() {
        // 检查必要元素
        const baseUrlInput = document.getElementById('DEEPSEEK_BASE_URL');
        const apiSelect = document.getElementById('api_provider_select');
        const modelInput = document.getElementById('MODEL');
        
        if (!baseUrlInput || !apiSelect) {
            console.error("初始化失败：必要元素未找到");
            return;
        }
        
        // 获取当前值
        const currentUrl = baseUrlInput.value;
        const currentModel = modelInput ? modelInput.value : '';
        
        console.log("初始化值 - 当前URL:", currentUrl, "当前模型:", currentModel);
        
        // 确定当前使用的API提供商
        let currentProviderId = 'kourichat-global'; // 默认值
        let found = false;
        
        // 查找匹配的API提供商
        for (let i = 0; i < apiSelect.options.length; i++) {
            const option = apiSelect.options[i];
            if (option.dataset.url === currentUrl) {
                currentProviderId = option.value;
                apiSelect.value = option.value;
                found = true;
                break;
            }
        }
        
        console.log("当前识别的API提供商:", currentProviderId);
        
        // 如果没有找到匹配项，可能是自定义API
        if (!found && currentUrl) {
            console.log("使用自定义API提供商");
            currentProviderId = 'custom';
            apiSelect.value = 'custom';
            
            // 显示自定义API输入框
            if (typeof showCustomApiInput === 'function') {
                showCustomApiInput(currentUrl);
            } else {
                const customApiInput = document.getElementById('customApiInput');
                if (customApiInput) {
                    customApiInput.style.display = 'block';
                    const input = customApiInput.querySelector('input');
                    if (input) input.value = currentUrl;
                }
            }
        }
        
        // 确保隐藏输入框的值与显示值一致
        const hiddenInput = document.querySelector(`input[name="DEEPSEEK_BASE_URL"]`);
        if (hiddenInput) {
            hiddenInput.value = currentUrl;
        }
        
        // 延迟初始化模型选择器，确保DOM已更新
        setTimeout(() => {
            // 初始化模型选择器（传递当前API提供商ID）
            if (typeof initializeModelSelect === 'function') {
                initializeModelSelect(currentProviderId);
            } else {
                console.warn('initializeModelSelect函数未定义，可能导致模型选择器初始化失败');
                
                // 备用：如果没有initializeModelSelect函数，但有updateModelSelect函数
                if (typeof window.updateModelSelect === 'function') {
                    console.log("使用updateModelSelect作为备用");
                    window.updateModelSelect(currentProviderId);
                }
            }
        }, 0);
    }

    // 从配置更新模型列表
    function updateModelSelectsFromConfig() {
        console.log("正在从配置更新所有模型选择器");
        if (!modelConfigs) return;

        // 获取当前选择的提供商
        const apiSelect = document.getElementById('api_provider_select');
        if (apiSelect.value) {
            console.log("从配置更新模型列表:", apiSelect.value);
            setTimeout(() => {
                updateModelSelect(apiSelect.value);
            }, 100);
        }
    }

    // 显示自定义API输入框
    function showCustomApiInput(value = '') {
        const customApiInput = document.getElementById('customApiInput');
        const hiddenInput = document.querySelector(`input[name="DEEPSEEK_BASE_URL"]`);
        customApiInput.style.display = 'block';
        if (value) {
            const input = customApiInput.querySelector('input');
            input.value = value;
            // 同时更新隐藏输入框
            if (hiddenInput) {
                hiddenInput.value = value;
            }
        }
    }

    // 更新API提供商
    function updateApiProvider(value) {
        console.log("更新API提供商:", value);
        const baseUrlInput = document.getElementById('DEEPSEEK_BASE_URL');
        const customApiInput = document.getElementById('customApiInput');
        const registerLinks = document.getElementById('register_links');
        const hiddenInput = document.querySelector(`input[name="DEEPSEEK_BASE_URL"]`);
        const modelSelect = document.getElementById('model_select');

        // 重置所有状态
        customApiInput.style.display = 'none';
        registerLinks.classList.add('d-none');
        registerLinks.innerHTML = '';

        // 处理自定义选项
        if (value === 'custom') {
            showCustomApiInput();
            console.log("处理自定义API提供商");
            // 确保updateModelSelect函数存在后再调用
            if (typeof updateModelSelect === 'function') {
                setTimeout(() => updateModelSelect('custom'), 100);
            } else {
                console.warn('updateModelSelect函数未定义，延迟初始化');
                setTimeout(() => {
                    if (typeof updateModelSelect === 'function') {
                        updateModelSelect('custom');
                    } else {
                        console.error('updateModelSelect函数未定义');
                    }
                }, 500);
            }
            return;
        }

        // 处理未选择情况
        if (!value) {
            // 确保updateModelSelect函数存在后再调用
            if (typeof updateModelSelect === 'function') {
                updateModelSelect('');
            }
            return;
        }

        // 获取选中的提供商配置
        const selectedOption = document.querySelector(`#api_provider_select option[value="${value}"]`);
        if (!selectedOption) return;

        // 更新API URL
        const apiUrl = selectedOption.dataset.url;
        baseUrlInput.value = apiUrl;
        // 同时更新隐藏输入框
        if (hiddenInput) {
            hiddenInput.value = apiUrl;
        }

        // 添加标记是否为 Ollama
        if (value === 'ollama') {
            baseUrlInput.dataset.isOllama = 'true';
        } else {
            baseUrlInput.dataset.isOllama = 'false';
        }

        // 创建注册按钮
        const registerUrl = selectedOption.dataset.register;
        if (registerUrl) {
            const link = document.createElement('a');
            link.href = registerUrl;
            link.className = 'btn btn-outline-primary w-100';
            link.target = '_blank';
            link.innerHTML = `<i class="bi bi-box-arrow-up-right me-1"></i>前往${selectedOption.textContent.replace(' API', '')}注册`;
            registerLinks.innerHTML = '';
            registerLinks.appendChild(link);
            registerLinks.classList.remove('d-none');
        }

        // 确保updateModelSelect函数存在后再调用
        if (typeof updateModelSelect === 'function') {
            console.log("直接调用updateModelSelect:", value);
            // 使用延时确保DOM已更新
            setTimeout(() => updateModelSelect(value), 100);
        } else {
            console.warn('updateModelSelect函数未定义，延迟初始化');
            // 尝试延迟调用，确保函数已加载
            setTimeout(() => {
                if (typeof updateModelSelect === 'function') {
                    console.log("延迟调用updateModelSelect:", value);
                    updateModelSelect(value);
                } else {
                    console.error('updateModelSelect函数未定义');
                    // 初始化模型选择框作为后备方案
                    if (typeof initializeModelSelect === 'function') {
                        console.log("回退到initializeModelSelect");
                        initializeModelSelect(value);
                    }
                }
            }, 500);
        }
    }

    // 更新自定义API
    function updateCustomApi(value) {
        const baseUrlInput = document.getElementById('DEEPSEEK_BASE_URL');
        const hiddenInput = document.querySelector(`input[name="DEEPSEEK_BASE_URL"]`);
        if (value) {
            baseUrlInput.value = value;
            // 同时更新隐藏输入框
            if (hiddenInput) {
                hiddenInput.value = value;
            }
            
        }
    }

                // 页面加载时初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 设置初始值
                setInitialValues();
                
                // 设置自定义输入框事件监听器
                setTimeout(function() {
                    const customModelInput = document.getElementById('customModelInput');
                    const customModelInputField = document.getElementById('customModelInputField');
                    const modelInput = document.getElementById('MODEL');
                    const modelSelect = document.getElementById('model_select');
                    
                    if (customModelInputField && modelInput) {
                        // 确保自定义输入框事件监听器已设置
                        if (typeof setupCustomModelInputListeners === 'function') {
                            setupCustomModelInputListeners();
                        }
                        
                        // 检查是否应该显示自定义输入框
                        if (modelSelect && modelSelect.value === 'custom' && modelInput.value) {
                            customModelInput.style.display = 'block';
                            customModelInputField.value = modelInput.value;
                        }
                    }
                }, 800);
                
                // 异步获取完整配置
                fetchModelConfigs();
            });
    </script>
{% elif key == 'PROMPT_ENHANCEMENT' %}
    <select class="form-select" id="{{ key }}" name="{{ key }}">
        <option value="True" {% if config.value %}selected{% endif %}>启用</option>
        <option value="False" {% if not config.value %}selected{% endif %}>停用</option>
    </select>
{% elif key == 'AVATAR_DIR' %}
    <select class="form-select" id="{{ key }}" name="{{ key }}">
        {% for option in config.options %}
        <option value="{{ option }}" {% if option == config.value %}selected{% endif %}>
            {{ option.split('/')[-1] }}
        </option>
        {% endfor %}
    </select>
{% elif key == 'NETWORK_SEARCH_ENABLED' or key == 'INTELLIGENT_SEARCH_ENABLED' or key == 'WEBLENS_ENABLED' or key == 'VOICE_CALL_ANTI_ECHO_ENABLED' %}
    <div class="form-check form-switch d-flex align-items-center" style="padding: 6px 0; min-height: 38px;">
        <input class="form-check-input me-2" type="checkbox" role="switch"
            id="{{ key }}" name="{{ key }}"
            {% if config.value %}checked{% endif %}
            style="margin: 0;"
            onchange="updateSwitchLabel(this)">
        <label class="form-check-label mb-0" id="{{ key }}_label" for="{{ key }}" style="line-height: 24px;">
            {{ '启用' if config.value else '停用' }}
        </label>
    </div>
    <script>
        function updateSwitchLabel(checkbox) {
            const label = document.getElementById(checkbox.id + '_label');
            if (label) {
                label.textContent = checkbox.checked ? '启用' : '停用';
            }

            // 使用正常的标记逻辑
            if (typeof markConfigChanged === 'function') {
                markConfigChanged(checkbox.id, checkbox.checked);
            }

            // 在控制台输出当前状态，便于调试
            console.log(`${checkbox.id} 状态已更新为: ${checkbox.checked}`);
        }

        // 确保页面加载时初始化开关状态
        document.addEventListener('DOMContentLoaded', function() {
            const checkbox = document.getElementById('{{ key }}');
            if (checkbox) {
                updateSwitchLabel(checkbox);

                // 添加额外的事件监听器，确保状态变化时触发
                checkbox.addEventListener('change', function() {
                    updateSwitchLabel(this);
                });
            }
        });
    </script>
{% elif config.value is boolean %}
    <div class="form-check form-switch d-flex align-items-center" style="padding: 6px 0; min-height: 38px;">
        <input class="form-check-input me-2" type="checkbox" role="switch"
            id="{{ key }}" name="{{ key }}"
            {% if config.value %}checked{% endif %}
            style="margin: 0;"
            onchange="updateSwitchLabel(this)">
        <label class="form-check-label mb-0" id="{{ key }}_label" for="{{ key }}" style="line-height: 24px;">
            {{ '启用' if config.value else '停用' }}
        </label>
    </div>
    <script>
        function updateSwitchLabel(checkbox) {
            const label = document.getElementById(checkbox.id + '_label');
            if (label) {
                label.textContent = checkbox.checked ? '启用' : '停用';
            }

            // 使用正常的标记逻辑
            if (typeof markConfigChanged === 'function') {
                markConfigChanged(checkbox.id, checkbox.checked);
            }

            // 在控制台输出当前状态，便于调试
            console.log(`${checkbox.id} 状态已更新为: ${checkbox.checked}`);
        }

        // 确保页面加载时初始化开关状态
        document.addEventListener('DOMContentLoaded', function() {
            const checkbox = document.getElementById('{{ key }}');
            if (checkbox) {
                updateSwitchLabel(checkbox);
            }
        });
    </script>
{% elif key == 'MODEL' %}
    <div class="mb-3">
        <select class="form-select mb-2" id="model_select" aria-label="选择模型">
            <!-- 模型选项会在页面加载时根据选择的API提供商动态更新 -->
            <option value="custom">自定义模型</option>
        </select>
        
        <script>
                // 全局执行函数，确保可从其他位置访问
    window.modelSelectHandler = function() {
        console.log("初始化模型选择器处理");
        
        // 获取元素
        const modelSelect = document.getElementById('model_select');
        const modelInput = document.getElementById('MODEL');
        const customModelInput = document.getElementById('customModelInput');
        
        // 检查必要元素
        if (!modelSelect) {
            console.error("模型选择器未找到!");
            return;
        }
        
        if (!modelInput) {
            console.error("MODEL输入框未找到!");
            return;
        }
        
        // 获取当前保存的模型值
        const savedModelValue = modelInput.value || '';
        
        // 模型选择变更处理函数
        function handleModelChange(value) {
            console.log("处理模型选择变更:", value);
            
            if (!customModelInput) {
                console.error("自定义模型输入框未找到!");
                return;
            }
            
            if (value === 'custom') {
                console.log("显示自定义模型输入框");
                customModelInput.style.display = 'block';
                const inputField = customModelInput.querySelector('input');
                if (inputField) {
                    // 如果有保存的值，填充到输入框
                    if (savedModelValue && !isPresetModel(savedModelValue)) {
                        inputField.value = savedModelValue;
                    }
                    // 聚焦输入框
                    inputField.focus();
                }
            } else {
                console.log("隐藏自定义模型输入框");
                customModelInput.style.display = 'none';
                if (value) {
                    modelInput.value = value;
                    // 触发change事件确保值被保存
                    modelInput.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }
        }
        
        // 检查值是否是预设模型
        function isPresetModel(value) {
            if (!modelSelect) return false;
            const options = Array.from(modelSelect.options);
            return options.some(opt => opt.value === value && opt.value !== 'custom');
        }
        
        // 移除所有现有事件监听器
        const newSelect = modelSelect.cloneNode(true);
        modelSelect.parentNode.replaceChild(newSelect, modelSelect);
        
        // 添加新事件监听器
        newSelect.addEventListener('change', function() {
            console.log("选择框变更:", this.value);
            handleModelChange(this.value);
        });
        
        // 检查是否有保存的模型值
        if (savedModelValue) {
            console.log("检查保存的模型值:", savedModelValue);
            
            // 检查保存的值是否是预设选项
            if (isPresetModel(savedModelValue)) {
                // 如果是预设选项，直接选中
                console.log("使用预设模型:", savedModelValue);
                newSelect.value = savedModelValue;
                if (customModelInput) customModelInput.style.display = 'none';
            } else {
                // 如果不是预设选项，切换到自定义模式
                console.log("使用自定义模型:", savedModelValue);
                newSelect.value = 'custom';
                
                if (customModelInput) {
                    customModelInput.style.display = 'block';
                    const inputField = customModelInput.querySelector('input');
                    if (inputField) {
                        inputField.value = savedModelValue;
                    }
                }
            }
        } else {
            // 没有保存的值，选择第一个选项
            console.log("无保存的模型值，使用默认值");
            if (newSelect.options.length > 0) {
                newSelect.selectedIndex = 0;
                modelInput.value = newSelect.value;
                if (customModelInput) customModelInput.style.display = 'none';
            }
        }
    };
    
    // 立即执行
    window.modelSelectHandler();
    
    // 在页面加载完成后再次执行
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(window.modelSelectHandler, 500);
    });
        </script>

        <!-- 添加自定义模型输入框 -->
        <div id="customModelInput" class="mb-2" style="display: none;">
            <input type="text" class="form-control"
                   placeholder="请输入自定义模型名称"
                   id="customModelInputField">
        </div>
        
        <script>
            // 为自定义模型输入框添加事件监听器
            document.addEventListener('DOMContentLoaded', function() {
                console.log("为自定义模型输入框添加事件监听器");
                setupCustomModelInputListeners();
                
                // 页面加载完成后延迟执行，确保事件监听器正确添加
                setTimeout(setupCustomModelInputListeners, 500);
            });
            
            function setupCustomModelInputListeners() {
                const customModelInputField = document.getElementById('customModelInputField');
                const modelSelect = document.getElementById('model_select');
                const modelInput = document.getElementById('MODEL');
                
                if (customModelInputField && modelSelect && modelInput) {
                    console.log("设置自定义模型输入框监听器");
                    
                    // 清除现有事件，防止重复绑定
                    const newField = customModelInputField.cloneNode(true);
                    customModelInputField.parentNode.replaceChild(newField, customModelInputField);
                    
                    // 添加输入事件监听
                    newField.addEventListener('input', function() {
                        console.log("自定义模型名称输入:", this.value);
                        if (modelInput) {
                            modelInput.value = this.value;
                            // 触发change事件确保值被保存
                            modelInput.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                    });
                    
                    // 添加失焦事件监听
                    newField.addEventListener('blur', function() {
                        console.log("自定义模型输入框失焦:", this.value);
                        if (this.value && modelInput) {
                            modelInput.value = this.value;
                            // 触发change事件确保值被保存
                            modelInput.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                    });
                    
                    // 检查当前模型值
                    const currentValue = modelInput.value;
                    if (modelSelect.value === 'custom' && currentValue) {
                        console.log("填充自定义模型值:", currentValue);
                        newField.value = currentValue;
                        
                        // 确保自定义输入框可见
                        const customModelInput = document.getElementById('customModelInput');
                        if (customModelInput) {
                            customModelInput.style.display = 'block';
                        }
                    }
                }
            }
        </script>

        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            readonly
            style="display: none;">
    </div>
{% elif key == 'NETWORK_SEARCH_BASE_URL' %}
    <div class="mb-3">
        <input type="text" class="form-control" id="{{ key }}" name="{{ key }}" value="https://api.kourichat.com/v1" readonly>
        <div class="form-text">联网搜索API地址固定使用KouriChat官方服务</div>
    </div>
{% elif key == 'NETWORK_SEARCH_MODEL' %}
    <div class="mb-3">
        <input type="text" class="form-control" id="{{ key }}" name="{{ key }}" value="kourichat-search" readonly>
        <div class="form-text">使用KouriChat-Search模型进行网络搜索</div>
    </div>
{% elif key == 'NETWORK_SEARCH_API_KEY' %}
    <div class="mb-3">
        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="请输入API密钥">
        <div id="network_search_api_key_help" class="mt-2">
            <p class="small text-muted">API密钥用于访问网络搜索服务，请在KouriChat官网获取</p>
            <a href="https://api.kourichat.com/register" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                <i class="bi bi-box-arrow-up-right"></i> 获取API密钥
            </a>
        </div>
    </div>
{% elif key == 'NETWORK_SEARCH_PROMPT' %}
    <input type="hidden" id="{{ key }}" name="{{ key }}" value="">

{% elif key == 'WEBLENS_MODEL' %}
    <div class="mb-3">
        <input type="text" class="form-control" id="{{ key }}" name="{{ key }}" value="kourichat-weblens" readonly>
        <div class="form-text">使用KouriChat-WebLens模型提取网页内容</div>
    </div>
{% elif key == 'WEBLENS_PROMPT' %}
    <input type="hidden" id="{{ key }}" name="{{ key }}" value="">


    <script>
    // 初始化模型选择框 - 直接显示当前模型，不依赖API调用
    function initializeModelSelect(passedProviderId) {
        console.log("调用initializeModelSelect，提供商:", passedProviderId);
        
        const modelSelect = document.getElementById('model_select');
        const modelInput = document.getElementById('MODEL');
        const customModelInput = document.getElementById('customModelInput');
        
        // 检查必要元素
        if (!modelSelect) {
            console.error("初始化失败：模型选择器未找到");
            return;
        }
        
        if (!modelInput) {
            console.error("初始化失败：MODEL输入框未找到");
            return;
        }
        
        // 获取保存的模型值
        const savedModel = modelInput.value || '';
        
        // 获取当前选择的API提供商 - 优先使用传入的providerId
        const apiSelect = document.getElementById('api_provider_select');
        const providerId = passedProviderId || (apiSelect ? apiSelect.value : 'kourichat-global');
        
        console.log("初始化模型选择器，当前提供商:", providerId, "保存的模型:", savedModel);
        
        // 清空选择框，准备添加与当前API提供商对应的模型选项
        modelSelect.innerHTML = '';
        
        // 根据提供商添加相应的模型选项
        if (providerId === 'kourichat-global') {
            modelSelect.innerHTML = `
                <option value="gemini-2.5-flash">gemini-2.5-flash</option>
                <option value="gemini-2.5-pro">gemini-2.5-pro</option>
                <option value="kourichat-v3">kourichat-v3</option>
                <option value="gpt-4o">gpt-4o</option>
                <option value="grok-3">grok-3</option>
            `;
        } else if (providerId === 'siliconflow') {
            modelSelect.innerHTML = `
                <option value="deepseek-ai/DeepSeek-V3">deepseek-ai/DeepSeek-V3</option>
                <option value="deepseek-ai/DeepSeek-R1">deepseek-ai/DeepSeek-R1</option>
                <option value="Pro/deepseek-ai/DeepSeek-V3">Pro/deepseek-ai/DeepSeek-V3</option>
            `;
        } else if (providerId === 'deepseek') {
            modelSelect.innerHTML = `
                <option value="deepseek-chat">deepseek-chat</option>
                <option value="deepseek-reasoner">deepseek-reasoner</option>
            `;
        }
        
        // 确保自定义选项存在 - 这适用于所有提供商
        if (!modelSelect.querySelector('option[value="custom"]')) {
            modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
        }
        
        // 处理不同情况
        if (providerId === 'ollama' || providerId === 'custom') {
            // 1. 自定义或Ollama提供商
            console.log("使用自定义/Ollama提供商");
            modelSelect.value = 'custom';
            
            // 显示自定义输入框
            if (customModelInput) {
                customModelInput.style.display = 'block';
                const inputField = customModelInput.querySelector('input');
                
                // 如果有保存的值，填充输入框
                if (inputField && savedModel) {
                    inputField.value = savedModel;
                } else if (inputField) {
                    inputField.value = '';
                }
            }
        } else if (savedModel) {
            // 2. 有保存的模型值
            // 检查保存的值是否在选项列表中
            const modelExists = Array.from(modelSelect.options).some(opt => opt.value === savedModel);
            
            if (modelExists) {
                // 如果在列表中，直接选择
                console.log("选择已保存的模型:", savedModel);
                modelSelect.value = savedModel;
                
                // 确保自定义输入框隐藏
                if (customModelInput) {
                    customModelInput.style.display = 'none';
                }
            } else {
                // 如果不在列表中，视为自定义模型
                console.log("使用自定义模型:", savedModel);
                modelSelect.value = 'custom';
                
                // 显示并填充自定义输入框
                if (customModelInput) {
                    customModelInput.style.display = 'block';
                    const inputField = customModelInput.querySelector('input');
                    if (inputField) {
                        inputField.value = savedModel;
                    }
                }
            }
        } else {
            // 3. 没有保存的模型值，使用默认值
            console.log("无保存的模型值，使用默认值");
            if (modelSelect.options.length > 0) {
                modelSelect.selectedIndex = 0;
                modelInput.value = modelSelect.value;
                
                // 隐藏自定义输入框
                if (customModelInput) {
                    customModelInput.style.display = 'none';
                }
            }
        }
        
        // 触发模型选择器变更事件
        if (typeof window.modelSelectHandler === 'function') {
            setTimeout(window.modelSelectHandler, 0);
        }
    }

    // 此处原有的updateModelSelect函数已移至全局范围
    // 我们将它保留为一个简单的转发函数，调用全局函数
    function updateModelSelect(providerId) {
        console.log("原始updateModelSelect被调用，转发到全局函数", providerId);
        // 延时调用可确保全局函数已注册
        setTimeout(() => {
            if (typeof window.updateModelSelect === 'function') {
                window.updateModelSelect(providerId);
            } else {
                console.error("全局updateModelSelect函数未定义!");
            }
        }, 50);
    }

    // 这些函数被外部代码调用，确保它们能正确工作
    // 更新模型选择
    function updateModel(value) {
        console.log("全局 updateModel 被调用", value);
        const modelInput = document.getElementById('MODEL');
        const customModelInput = document.getElementById('customModelInput');
        const modelSelect = document.getElementById('model_select');

        if (value === 'custom') {
            // 显示自定义输入框
            customModelInput.style.display = 'block';
            const inputField = customModelInput.querySelector('input');
            if (inputField) {
                // 如果已有隐藏字段值且不是预设选项，则填充
                const currentValue = modelInput.value;
                if (currentValue && modelSelect) {
                    const isPreset = Array.from(modelSelect.options)
                        .some(opt => opt.value === currentValue && opt.value !== 'custom');
                    
                    if (!isPreset) {
                        inputField.value = currentValue;
                    }
                }
                inputField.focus();
            }
        } else {
            // 隐藏自定义输入框
            customModelInput.style.display = 'none';
            if (value) {
                modelInput.value = value;
                // 触发change事件确保值被保存
                modelInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
        
        // 更新选择框
        if (modelSelect && modelSelect.value !== value) {
            modelSelect.value = value;
        }
    }

    // 更新自定义模型值
    function updateCustomModel(value) {
        console.log("全局 updateCustomModel 被调用", value);
        const modelInput = document.getElementById('MODEL');
        if (value) {
            modelInput.value = value;
            // 触发change事件确保值被保存
            modelInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            // 确保选择框为custom
            const modelSelect = document.getElementById('model_select');
            if (modelSelect && modelSelect.value !== 'custom') {
                modelSelect.value = 'custom';
            }
        }
    }

    // 页面加载时获取配置
    document.addEventListener('DOMContentLoaded', function() {
        // 首先初始化模型选择框，确保当前模型值能正确显示
        initializeModelSelect();
        // 然后获取更多模型配置数据（如果可用）
        fetchModelConfigs();
    });
    </script>
{% elif key == 'TEMPERATURE' %}
    <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>当前值: <strong id="{{ key }}_display" class="temperature-value">{{ config.value }}</strong></span>
            <span class="badge bg-primary">创造性参数</span>
        </div>
        <input type="range" id="{{ key }}_slider"
            class="temperature-slider"
            min="{{ config.min|default(0) }}"
            max="{{ config.max|default(2) }}"
            step="0.1"
            value="{{ config.value }}"
            oninput="updateTemperature('{{ key }}', this.value)">
        <div class="d-flex justify-content-between mt-1">
            <span class="small text-muted">低温 (更确定)</span>
            <span class="small text-muted">高温 (更创意)</span>
        </div>
        <input type="hidden"
            id="{{ key }}"
            name="{{ key }}"
            value="{{ config.value }}">
    </div>

    <script>
        // 确保页面加载时初始化温度值
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('{{ key }}_slider');
            if (slider) {
                updateTemperature('{{ key }}', slider.value);
            }
        });
    </script>
{% elif key == 'USE_MULTIMODAL_LLM' %}
    <div class="mb-3">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="{{ key }}" name="{{ key }}"
                   {% if config.value %}checked{% endif %}
                   onchange="toggleVisionMode(this.checked)">
            <label class="form-check-label" for="{{ key }}">
                {{ config.description }}
            </label>
        </div>
        <div class="mt-2">
            <p class="small text-muted">
                开启后将使用多模态LLM直接进行图像识别，无需额外的图像识别API。
                关闭后使用传统的图像识别API服务。
            </p>
        </div>
    </div>

    <script>
    function toggleVisionMode(useMultimodal) {
        // 获取所有图像识别相关的配置项
        const visionElements = [
            'VISION_BASE_URL',
            'VISION_API_KEY',
            'VISION_MODEL',
            'VISION_TEMPERATURE'
        ];

        visionElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                // 找到包含该元素的最近的配置项容器
                let container = element.closest('.mb-3');
                if (!container) {
                    container = element.closest('.config-item');
                }
                if (!container) {
                    container = element.parentElement;
                }

                if (container) {
                    if (useMultimodal) {
                        container.style.display = 'none';
                    } else {
                        container.style.display = '';
                    }
                }
            }
        });

        // 同时隐藏/显示相关的配置组标题
        const visionConfigSections = document.querySelectorAll('[data-vision-config]');
        visionConfigSections.forEach(section => {
            if (useMultimodal) {
                section.style.display = 'none';
            } else {
                section.style.display = '';
            }
        });
    }

    // 页面加载时根据当前状态设置显示/隐藏
    document.addEventListener('DOMContentLoaded', function() {
        const checkbox = document.getElementById('USE_MULTIMODAL_LLM');
        if (checkbox) {
            toggleVisionMode(checkbox.checked);
        }
    });
    </script>

{% elif key == 'VISION_BASE_URL' %}
    <div class="mb-3" data-vision-config>
        <select class="form-select mb-2" id="vision_api_provider_select" onchange="updateVisionApiProvider(this.value)" aria-label="选择图像识别API提供商">
            <!-- 移除请选择服务提供商选项 -->
            <!-- 预设常用选项，减少依赖异步加载 -->
            <option value="kourichat-global" data-url="https://api.kourichat.com/v1" data-register="https://api.kourichat.com/register">KouriChat API </option>
            <option value="moonshot" data-url="https://api.moonshot.cn/v1" data-register="https://platform.moonshot.cn/console/api-keys">Moonshot AI</option>
            <option value="openai" data-url="https://api.openai.com/v1" data-register="https://platform.openai.com/api-keys">OpenAI</option>
            <option value="custom">自定义服务提供商</option>
        </select>

        <!-- 添加自定义 API 输入框 -->
        <div id="customVisionApiInput" class="mb-2" style="display: none;">
            <input type="text" class="form-control"
                   placeholder="请输入自定义服务地址"
                   onchange="updateCustomVisionApi(this.value)">
        </div>

        <!-- 注册链接容器 -->
        <div id="vision_register_links" class="d-none">
            <!-- 注册链接将通过JavaScript动态添加 -->
        </div>

        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            readonly
            style="display: none;">
    </div>

    <script>
    let visionModelConfigs = {
        api_providers: [
            {
                id: "kourichat-global",
                name: "KouriChat API (推荐)",
                url: "https://api.kourichat.com/v1",
                register_url: "https://api.kourichat.com/register"
            },
            {
                id: "moonshot",
                name: "Moonshot AI",
                url: "https://api.moonshot.cn/v1",
                register_url: "https://platform.moonshot.cn/console/api-keys"
            },
            {
                id: "openai",
                name: "OpenAI",
                url: "https://api.openai.com/v1",
                register_url: "https://platform.openai.com/api-keys"
            }
        ],
        models: {
            "kourichat-global": [
                {id: "kourichat-vision", name: "KouriChat Vision (推荐)"},
                {id: "gemini-2.5-pro", name: "Gemini 2.5 Pro"},
                {id: "gpt-4o", name: "GPT-4o"}
            ],
            "moonshot": [
                {id: "moonshot-v1-8k-vision-preview", name: "Moonshot V1 8K Vision (推荐)"},
                {id: "moonshot-v1-32k-vision", name: "Moonshot V1 32K Vision"}
            ],
            "openai": [
                {id: "gpt-4o", name: "GPT-4o (推荐)"},
                {id: "gpt-4-vision-preview", name: "GPT-4 Vision"}
            ]
        }
    };

    // 获取配置数据 - 添加超时控制，但优先使用预设数据
    async function fetchVisionModelConfigs() {
        try {
            // 设置一个2秒的超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 2000);

            const response = await fetch('/get_vision_api_configs', {
                signal: controller.signal
            });
            clearTimeout(timeoutId);

            const data = await response.json();

            if (data.status === 'success') {
                // 合并服务器返回的数据到本地预设数据
                data.api_providers.forEach(provider => {
                    // 检查是否已存在相同ID的提供商
                    const existingIndex = visionModelConfigs.api_providers.findIndex(p => p.id === provider.id);
                    if (existingIndex >= 0) {
                        // 更新现有提供商
                        visionModelConfigs.api_providers[existingIndex] = provider;
                    } else {
                        // 添加新提供商
                        visionModelConfigs.api_providers.push(provider);
                    }
                });

                // 合并模型数据
                Object.keys(data.models || {}).forEach(providerId => {
                    visionModelConfigs.models[providerId] = data.models[providerId];
                });
            }
        } catch (error) {
            console.error('获取图像识别配置失败或超时:', error);
        } finally {
            // 无论成功失败都初始化选择框
            setVisionInitialValues();
        }
    }

    // 设置初始值
    function setVisionInitialValues() {
        // 根据当前配置设置初始值
        const currentUrl = document.getElementById('VISION_BASE_URL').value;
        const apiSelect = document.getElementById('vision_api_provider_select');
        const currentModel = document.getElementById('VISION_MODEL').value;

        // 查找匹配的选项
        let found = false;
        for (let i = 0; i < apiSelect.options.length; i++) {
            const option = apiSelect.options[i];
            if (option.dataset.url === currentUrl) {
                apiSelect.value = option.value;
                updateVisionApiProvider(option.value);
                found = true;
                break;
            }
        }

        // 如果没有找到匹配项，使用自定义选项
        if (!found && currentUrl) {
            apiSelect.value = 'custom';
            showCustomVisionApiInput(currentUrl);

            // 对于自定义API提供商，显示自定义模型输入框并设置值
            const customModelInput = document.getElementById('customVisionModelInput');
            if (customModelInput && currentModel) {
                customModelInput.style.display = 'block';
                customModelInput.querySelector('input').value = currentModel;

                // 设置模型选择框
                const modelSelect = document.getElementById('vision_model_select');
                if (modelSelect) {
                    // 添加自定义选项
                    if (!modelSelect.querySelector('option[value="custom"]')) {
                        modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
                    }
                    modelSelect.value = 'custom';
                }
            }

            updateVisionModelSelect('custom');
        }

        // 如果自定义模型输入框需要显示
        const customModelInput = document.getElementById('customVisionModelInput');
        if (apiSelect.value === 'custom' && customModelInput && currentModel) {
            customModelInput.style.display = 'block';
            customModelInput.querySelector('input').value = currentModel;
        }
    }

    // 显示自定义图像识别API输入框
    function showCustomVisionApiInput(value = '') {
        const customApiInput = document.getElementById('customVisionApiInput');
        customApiInput.style.display = 'block';
        if (value) {
            customApiInput.querySelector('input').value = value;
        }
    }

    // 更新自定义图像识别API
    function updateCustomVisionApi(value) {
        const baseUrlInput = document.getElementById('VISION_BASE_URL');
        if (value) {
            baseUrlInput.value = value;
        }
    }

    // 更新图像识别API提供商
    function updateVisionApiProvider(providerId) {
        // 获取URL输入框和显示区域
        const urlInput = document.getElementById('VISION_BASE_URL');
        const registerLinks = document.getElementById('vision_register_links');
        
        // 获取选择器和当前选择的选项
        const selector = document.getElementById('vision_api_provider_select');
        const selectedOption = selector.options[selector.selectedIndex];
        
        // 自定义服务提供商处理
        const customApiInput = document.getElementById('customVisionApiInput');
        
        if (providerId === 'custom') {
            // 显示自定义输入框
            customApiInput.style.display = 'block';
            // 获取当前URL，放入自定义输入框
            customApiInput.querySelector('input').value = urlInput.value || '';
            // 隐藏注册链接
            registerLinks.classList.add('d-none');
        } else {
            // 隐藏自定义输入框
            customApiInput.style.display = 'none';
            
            // 从选项中获取API URL
            const apiUrl = selectedOption.getAttribute('data-url');
            if (apiUrl) {
                urlInput.value = apiUrl;
            }
            
            // 显示注册链接（如果有）
            const registerUrl = selectedOption.getAttribute('data-register');
            if (registerUrl) {
                registerLinks.innerHTML = `
                    <a href="${registerUrl}" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                        <i class="bi bi-box-arrow-up-right"></i> 获取API密钥
                    </a>`;
                registerLinks.classList.remove('d-none');
            } else {
                registerLinks.classList.add('d-none');
            }
        }
        
        // 更新对应的模型选择器
        updateVisionModelSelect(providerId);
    }

    // 添加缺失的函数：更新图像识别模型选择框
    async function updateVisionModelSelect(providerId) {
        const modelSelect = document.getElementById('vision_model_select');
        const modelInput = document.getElementById('VISION_MODEL');
        const customModelInput = document.getElementById('customVisionModelInput');
        
        if (!modelSelect || !modelInput) return;
        
        // 保存当前模型值，确保后续操作不会丢失
        const currentModelValue = modelInput.value;
        
        // 重置选择框
        modelSelect.innerHTML = '';
        
        if (providerId === 'custom') {
            modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
            modelSelect.value = 'custom';
            
            // 显示自定义输入框并设置当前值
            if (customModelInput) {
                customModelInput.style.display = 'block';
                customModelInput.querySelector('input').value = currentModelValue;
            }
            return;
        }
        
        if (!providerId) return;
        
        try {
            // 加载该提供商下的模型列表
            let models = [];
            
            // 如果已缓存配置数据
            if (visionModelConfigs && visionModelConfigs.models && visionModelConfigs.models[providerId]) {
                models = visionModelConfigs.models[providerId];
            }
            
            // 添加模型选项
            if (models.length) {
                models.forEach(model => {
                    // 检查是否已存在该选项
                    if (!modelSelect.querySelector(`option[value="${model.id}"]`)) {
                        modelSelect.innerHTML += `
                            <option value="${model.id}">
                                ${model.name}
                            </option>`;
                    }
                });
            }
            
            // 添加自定义模型选项
            modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
            
        } catch (error) {
            console.error('更新图像识别模型选项失败:', error);
        } finally {
            // 检查当前值是否存在于模型列表中
            const modelExists = Array.from(modelSelect.options).some(opt => opt.value === currentModelValue);
            
            if (modelExists) {
                // 如果当前值是预设模型之一
                modelSelect.value = currentModelValue;
                if (customModelInput) customModelInput.style.display = 'none';
            } else if (currentModelValue) {
                // 如果当前值不在预设列表中且不为空，视为自定义模型
                modelSelect.value = 'custom';
                
                // 显示自定义输入框并设置值
                if (customModelInput) {
                    customModelInput.style.display = 'block';
                    customModelInput.querySelector('input').value = currentModelValue;
                }
                
                // 确保隐藏输入框的值是自定义的值
                modelInput.value = currentModelValue;
            } else if (modelSelect.options.length > 1) {
                // 如果没有当前模型值，选择第一个有效选项
                modelSelect.selectedIndex = 0;
                
                // 更新隐藏的值
                const selectedModel = modelSelect.value;
                modelInput.value = selectedModel;
                
                // 确保自定义输入框隐藏
                if (customModelInput) customModelInput.style.display = 'none';
            }
        }
    }

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 立即设置初始值，不等待异步请求
        setVisionInitialValues();
        // 然后异步获取更多配置
        fetchVisionModelConfigs();
    });
    </script>

{% elif key == 'VISION_API_KEY' %}
    <div class="mb-3" data-vision-config>
        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="请输入API密钥">
        <div id="api_key_help" class="mt-2">
            <p class="small text-muted">API密钥用于访问图像识别服务，请在选择的服务提供商官网获取</p>
        </div>
    </div>

{% elif key == 'VISION_MODEL' %}
    <div class="mb-3" data-vision-config>
        <select class="form-select mb-2" id="vision_model_select" onchange="updateVisionModel(this.value)" aria-label="选择图像识别模型">
            <!-- 移除请选择模型选项 -->
        </select>

        <!-- 添加自定义模型输入框 -->
        <div id="customVisionModelInput" class="mb-2" style="display: none;">
            <input type="text" class="form-control"
                   placeholder="请输入自定义模型名称"
                   onchange="updateCustomVisionModel(this.value)">
        </div>

        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            readonly
            style="display: none;">
    </div>

    <script>
    // 更新图像识别模型选择框
    function updateVisionModelSelect(providerId) {
        const modelSelect = document.getElementById('vision_model_select');
        const modelInput = document.getElementById('VISION_MODEL');
        const customModelInput = document.getElementById('customVisionModelInput');

        // 保存当前模型值，确保后续操作不会丢失
        const currentModelValue = modelInput.value;

        // 重置选择框 - 移除"请选择模型"选项
        modelSelect.innerHTML = '';
        customModelInput.style.display = 'none';

        // 预设一些常用模型选项，确保始终有这些选项
        const defaultModels = {
            "kourichat-global": [
                {id: "kourichat-vision", name: "KouriChat Vision (推荐)"}
            ],
            "moonshot": [
                {id: "moonshot-v1-8k-vision-preview", name: "Moonshot V1 8K Vision (推荐)"},
                {id: "moonshot-v1-32k-vision", name: "Moonshot V1 32K Vision"}
            ],
            "openai": [
                {id: "gpt-4o", name: "GPT-4o (推荐)"},
                {id: "gpt-4-vision-preview", name: "GPT-4 Vision"}
            ]
        };

        // 如果是自定义提供商或没有选择提供商
        if (providerId === 'custom' || !providerId) {
            if (providerId === 'custom') {
                // 添加自定义选项
                modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
                modelSelect.value = 'custom';
                modelSelect.disabled = false;

                // 显示自定义输入框并设置当前值
                customModelInput.style.display = 'block';
                customModelInput.querySelector('input').value = currentModelValue;
            } else {
                modelSelect.disabled = true;
            }
            return;
        }

        // 启用选择框
        modelSelect.disabled = false;

        // 获取提供商对应的模型列表
        let models = [];
        if (visionModelConfigs && visionModelConfigs.models && visionModelConfigs.models[providerId]) {
            models = visionModelConfigs.models[providerId];
        } else if (defaultModels[providerId]) {
            models = defaultModels[providerId];
        }

        // 添加模型选项
        models.forEach(model => {
            modelSelect.innerHTML += `<option value="${model.id}">${model.name}</option>`;
        });

        // 添加自定义模型选项 - 无论选择哪个供应商都添加
        modelSelect.innerHTML += '<option value="custom">自定义模型</option>';

        // 检查当前值是否存在于模型列表中
        const modelExists = models.some(m => m.id === currentModelValue);

        if (modelExists) {
            // 如果当前值是预设模型之一
            modelSelect.value = currentModelValue;
            customModelInput.style.display = 'none';
        } else if (currentModelValue) {
            // 如果当前值不在预设列表中且不为空，视为自定义模型
            // 添加自定义选项到下拉列表
            modelSelect.value = 'custom';

            // 显示自定义输入框并设置值
            customModelInput.style.display = 'block';
            customModelInput.querySelector('input').value = currentModelValue;

            // 确保隐藏输入框的值是自定义的值
            modelInput.value = currentModelValue;
        } else if (models.length > 0) {
            // 默认选择第一个推荐模型
            const recommendedModel = models.find(m => m.name.includes('推荐'));
            if (recommendedModel) {
                modelSelect.value = recommendedModel.id;
                modelInput.value = recommendedModel.id;
            } else {
                // 没有推荐模型，选择第一个
                modelSelect.value = models[0].id;
                modelInput.value = models[0].id;
            }
            // 确保自定义输入框隐藏
            customModelInput.style.display = 'none';
        }
    }

    // 更新选中的图像识别模型
    function updateVisionModel(value) {
        const modelInput = document.getElementById('VISION_MODEL');
        const customModelInput = document.getElementById('customVisionModelInput');

        if (value === 'custom') {
            customModelInput.style.display = 'block';
            customModelInput.querySelector('input').focus();
        } else {
            customModelInput.style.display = 'none';
            if (value) {
                modelInput.value = value;
            }
        }
    }

    // 更新自定义图像识别模型
    function updateCustomVisionModel(value) {
        const modelInput = document.getElementById('VISION_MODEL');
        if (value) {
            modelInput.value = value;
        }
    }
    </script>
{% elif key == 'VISION_TEMPERATURE' %}
    <div class="mb-3" data-vision-config>
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>当前值: <strong id="{{ key }}_display" class="temperature-value">{{ config.value }}</strong></span>
            <span class="badge bg-primary">创造性参数</span>
        </div>
        <input type="range" id="{{ key }}_slider"
            class="temperature-slider"
            min="{{ config.min|default(0) }}"
            max="{{ config.max|default(1) }}"
            step="0.1"
            value="{{ config.value }}"
            oninput="updateTemperature('{{ key }}', this.value)">
        <div class="d-flex justify-content-between mt-1">
            <span class="small text-muted">低温 (更确定)</span>
            <span class="small text-muted">高温 (更创意)</span>
        </div>
        <input type="hidden"
            id="{{ key }}"
            name="{{ key }}"
            value="{{ config.value }}">
    </div>

    <script>
        // 确保页面加载时初始化温度值
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('{{ key }}_slider');
            if (slider) {
                updateTemperature('{{ key }}', slider.value);
            }
        });
    </script>
{% elif key == 'QUEUE_TIMEOUT' %}
    <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>当前值: <strong id="{{ key }}_display">{{ config.value }}</strong></span>
            <span class="badge bg-primary">响应速度</span>
        </div>
        <input type="range" id="{{ key }}_slider"
            class="queue-timeout-slider"
            min="0"
            max="{{ config.max }}"
            step="{{ config.step|default(1) }}"
            value="{{ config.value }}"
            oninput="updateRangeValue('{{ key }}', this.value)">
        <div class="d-flex justify-content-between mt-1">
            <span class="small text-muted">快速响应 (0)</span>
            <span class="small text-muted">慢速响应 ({{ config.max }})</span>
        </div>
        <input type="hidden"
            id="{{ key }}"
            name="{{ key }}"
            value="{{ config.value }}">
        <div class="mt-2 small text-muted">
            <i class="bi bi-info-circle me-1"></i>注意！时间小于8秒会提高封号风险！！！如果在短时间内喜欢多段发送内容，或者打字速度较慢，建议可以设置更大数值
        </div>
    </div>

    <script>
        // 确保页面加载时初始化滑块值
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('{{ key }}_slider');
            if (slider) {
                updateRangeValue('{{ key }}', slider.value);
            }
        });
    </script>
{% elif config.type == 'number' and config.min is defined and config.max is defined %}
    <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>当前值: <strong id="{{ key }}_display">{{ config.value }}</strong></span>
        </div>
        <input type="range" id="{{ key }}_slider"
            class="form-range"
            min="{{ config.min }}"
            max="{{ config.max }}"
            step="{{ config.step|default(1) }}"
            value="{{ config.value }}"
            oninput="updateRangeValue('{{ key }}', this.value)">
        <div class="d-flex justify-content-between mt-1">
            <span class="small text-muted">{{ config.min }}</span>
            <span class="small text-muted">{{ config.max }}</span>
        </div>
        <input type="hidden"
            id="{{ key }}"
            name="{{ key }}"
            value="{{ config.value }}">
    </div>

    <script>
        // 确保页面加载时初始化滑块值
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('{{ key }}_slider');
            if (slider) {
                updateRangeValue('{{ key }}', slider.value);
            }
        });
    </script>
{% elif key == 'NETWORK_SEARCH_MODEL' %}
    <div class="mb-3">
        <input type="text" class="form-control" id="{{ key }}" name="{{ key }}" value="kourichat-search" readonly>
        <div class="form-text">使用KouriChat-Search模型进行网络搜索</div>
    </div>
{% elif key == 'WEBLENS_MODEL' %}
    <div class="mb-3">
        <input type="text" class="form-control" id="{{ key }}" name="{{ key }}" value="kourichat-weblens" readonly>
        <div class="form-text">使用KouriChat-WebLens模型提取网页内容</div>
    </div>
{% elif key == 'NETWORK_SEARCH_PROMPT' %}
    <input type="hidden" id="{{ key }}" name="{{ key }}" value="">

{% elif key == 'WEBLENS_PROMPT' %}
    <input type="hidden" id="{{ key }}" name="{{ key }}" value="">

{% elif key == 'VOICE_CALL_ASR_DEVICE_KEYWORD' %}
    <div class="mb-3">
        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="例如: CABLE Input">
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            ASR音频输入设备关键词，用于语音识别。常见值：
            <br>• <code>CABLE Input</code> - VB-Audio Virtual Cable
            <br>• <code>VB-Audio</code> - VB-Audio Virtual Cable
            <br>• <code>VOICEMEETER</code> - VoiceMeeter Input
        </div>
    </div>

{% elif key == 'VOICE_CALL_TTS_DEVICE_KEYWORD' %}
    <div class="mb-3">
        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="例如: VOICEMEETER">
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            TTS音频输出设备关键词，用于语音播放。常见值：
            <br>• <code>VOICEMEETER</code> - VoiceMeeter Input
            <br>• <code>VB-Audio</code> - VB-Audio Virtual Cable
            <br>• <code>CABLE</code> - VB-Audio Virtual Cable
        </div>
    </div>

{% elif key == 'VOICE_CALL_ASR_TIMEOUT_SECONDS' %}
    <div class="mb-3">
        <label for="{{ key }}_slider" class="form-label">
            ASR发送给LLM的等待时间: <span id="{{ key }}_value">{{ config.value }}</span> 秒
        </label>
        <input type="range" class="form-range"
            id="{{ key }}_slider"
            min="{{ config.min or 1.0 }}"
            max="{{ config.max or 10.0 }}"
            step="0.1"
            value="{{ config.value }}"
            style="background: linear-gradient(to right, #ff69b4 0%, #ff1493 100%);">
        <input type="hidden" id="{{ key }}" name="{{ key }}" value="{{ config.value }}">
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            控制ASR识别结果发送给LLM的等待时间。较短的时间会让对话更快响应，但可能会截断长句子；较长的时间会等待完整句子，但响应会稍慢。
        </div>
    </div>
    <script>
        (function() {
            const slider = document.getElementById('{{ key }}_slider');
            const valueSpan = document.getElementById('{{ key }}_value');
            const hiddenInput = document.getElementById('{{ key }}');

            slider.addEventListener('input', function() {
                const value = parseFloat(this.value);
                valueSpan.textContent = value.toFixed(1);
                hiddenInput.value = value;
                markAsUnsaved();
            });
        })();
    </script>

{% elif key == 'OPENAI_TTS_API_KEY' %}
    <div class="mb-3">
        <input type="password" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="请输入OpenAI兼容TTS API密钥">
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            TTS API密钥，兼容Fish Audio等服务
            <br>• 支持Fish Audio、OpenAI等兼容服务
        </div>
    </div>

{% elif key == 'OPENAI_TTS_BASE_URL' %}
    <div class="mb-3">
        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="请输入API基础URL">
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            TTS API基础URL，兼容Fish Audio等服务
            <br>• Fish Audio: <code>https://api.fish.audio/v1</code>
            <br>• OpenAI: <code>https://api.openai.com/v1</code>
        </div>
    </div>

{% elif key == 'OPENAI_TTS_MODEL' %}
    <div class="mb-3">
        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="请输入TTS模型名称">
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            TTS模型名称或ID
            <br>• Fish Audio: 使用您的模型ID
            <br>• OpenAI: <code>tts-1</code> 或 <code>tts-1-hd</code>
        </div>
    </div>

{% elif key == 'OPENAI_TTS_VOICE' %}
    <div class="mb-3">
        <input type="text" class="form-control"
            id="{{ key }}" name="{{ key }}"
            value="{{ config.value }}"
            placeholder="请输入语音音色名称">
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            语音音色名称或ID
            <br>• Fish Audio: 使用您的音色ID
            <br>• OpenAI: <code>alloy</code>, <code>echo</code>, <code>fable</code>, <code>onyx</code>, <code>nova</code>, <code>shimmer</code>
        </div>
    </div>

{% elif key == 'OPENAI_TTS_TEMPERATURE' %}
    <div class="mb-3">
        <label for="{{ key }}" class="form-label">
            <i class="bi bi-thermometer-half me-2"></i>TTS 温度参数
        </label>
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>当前值: <strong id="{{ key }}_display" class="temperature-value">{{ config.value }}</strong></span>
            <span class="badge bg-primary">创造性参数</span>
        </div>
        <!-- 隐藏的实际提交值 -->
        <input type="hidden" id="{{ key }}" name="{{ key }}" value="{{ config.value }}">
        <input type="range" id="{{ key }}_slider"
            class="openai-tts-temp-slider"
            min="{{ config.min|default(0) }}"
            max="{{ config.max|default(1) }}"
            step="{{ config.step|default(0.1) }}"
            value="{{ config.value }}"
            data-slider-type="temperature"
            oninput="updateTemperature('{{ key }}', this.value); fixFishSliderStep(this)">
        <div class="d-flex justify-content-between mt-1">
            <small class="text-muted">稳定 (更确定)</small>
            <small class="text-muted">随机 (更创造)</small>
        </div>
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            Fish Audio 温度参数，控制语音合成的随机性。值越高越随机，值越低越稳定
        </div>
    </div>
    <script>
        // 确保页面加载时初始化温度值和步长
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('{{ key }}_slider');
            if (slider) {
                updateTemperature('{{ key }}', slider.value);
                fixFishSliderStep(slider);

                // 额外强制设置步长
                setTimeout(() => {
                    fixFishSliderStep(slider);
                }, 100);
            }
        });
    </script>

{% elif key == 'OPENAI_TTS_TOP_P' %}
    <div class="mb-3">
        <label for="{{ key }}" class="form-label">
            <i class="bi bi-sliders me-2"></i>TTS Top-P参数
        </label>
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>当前值: <strong id="{{ key }}_display" class="temperature-value">{{ config.value }}</strong></span>
            <span class="badge bg-success">多样性参数</span>
        </div>
        <!-- 隐藏的实际提交值 -->
        <input type="hidden" id="{{ key }}" name="{{ key }}" value="{{ config.value }}">
        <input type="range" id="{{ key }}_slider"
            class="openai-tts-top-p-slider"
            min="{{ config.min|default(0) }}"
            max="{{ config.max|default(1) }}"
            step="{{ config.step|default(0.1) }}"
            value="{{ config.value }}"
            data-slider-type="top-p"
            oninput="updateTemperature('{{ key }}', this.value); fixFishSliderStep(this)">
        <div class="d-flex justify-content-between mt-1">
            <small class="text-muted">保守 (更一致)</small>
            <small class="text-muted">多样 (更变化)</small>
        </div>
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            Fish Audio Top-P参数，控制语音合成的多样性。建议值：0.7
        </div>
    </div>
    <script>
        // 确保页面加载时初始化温度值和步长
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('{{ key }}_slider');
            if (slider) {
                updateTemperature('{{ key }}', slider.value);
                fixFishSliderStep(slider);

                // 额外强制设置步长
                setTimeout(() => {
                    fixFishSliderStep(slider);
                }, 100);
            }
        });
    </script>

{% elif key == 'OPENAI_TTS_SPEED' %}
    <div class="mb-3">
        <label for="{{ key }}" class="form-label">
            <i class="bi bi-speedometer2 me-2"></i>TTS 语速
        </label>
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>当前值: <strong id="{{ key }}_display" class="temperature-value">{{ config.value }}</strong></span>
            <span class="badge bg-warning">语速参数</span>
        </div>
        <!-- 隐藏的实际提交值 -->
        <input type="hidden" id="{{ key }}" name="{{ key }}" value="{{ config.value }}">
        <input type="range" id="{{ key }}_slider"
            class="openai-tts-speed-slider"
            min="{{ config.min|default(0.5) }}"
            max="{{ config.max|default(2.0) }}"
            step="{{ config.step|default(0.1) }}"
            value="{{ config.value }}"
            data-slider-type="speed"
            oninput="updateTemperature('{{ key }}', this.value); fixFishSliderStep(this)">
        <div class="d-flex justify-content-between mt-1">
            <small class="text-muted">慢速 (0.5x)</small>
            <small class="text-muted">正常 (1.0x)</small>
            <small class="text-muted">快速 (2.0x)</small>
        </div>
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            Fish Audio 语速参数。1.0为正常语速，小于1.0较慢，大于1.0较快
        </div>
    </div>
    <script>
        // 确保页面加载时初始化温度值和步长
        document.addEventListener('DOMContentLoaded', function() {
            const slider = document.getElementById('{{ key }}_slider');
            if (slider) {
                updateTemperature('{{ key }}', slider.value);
                fixFishSliderStep(slider);

                // 额外强制设置步长
                setTimeout(() => {
                    fixFishSliderStep(slider);
                }, 100);
            }
        });
    </script>

{% elif config.type == 'textarea' %}
    <textarea class="form-control"
        id="{{ key }}" name="{{ key }}"
        rows="5">{{ config.value }}</textarea>
{% elif config.type == 'boolean' %}
    <div class="form-check form-switch">
        <input class="form-check-input" type="checkbox"
               id="{{ key }}" name="{{ key }}"
               {% if config.value == 'on' or config.value == true or config.value == 'true' %}checked{% endif %}
               onchange="updateBooleanSwitchLabel(this)">
        <label class="form-check-label" for="{{ key }}" id="{{ key }}_label">
            {% if config.value == 'on' or config.value == true or config.value == 'true' %}启用{% else %}停用{% endif %}
        </label>
    </div>
    <script>
        function updateBooleanSwitchLabel(checkbox) {
            const label = document.getElementById(checkbox.id + '_label');
            if (label) {
                label.textContent = checkbox.checked ? '启用' : '停用';
            }

            // 标记配置已更改
            if (typeof markConfigChanged === 'function') {
                markConfigChanged(checkbox.id, checkbox.checked);
            }

            // 在控制台输出当前状态，便于调试
            console.log(`${checkbox.id} 状态已更新为: ${checkbox.checked}`);
        }

        // 确保页面加载时初始化开关状态
        document.addEventListener('DOMContentLoaded', function() {
            const checkbox = document.getElementById('{{ key }}');
            if (checkbox) {
                updateBooleanSwitchLabel(checkbox);

                // 添加额外的事件监听器，确保状态变化时触发
                checkbox.addEventListener('change', function() {
                    updateBooleanSwitchLabel(this);
                });
            }
        });
    </script>
{% elif config.type == 'button' %}
    <button type="button" class="btn btn-primary"
            id="{{ key }}"
            onclick="handleButtonAction('{{ config.action }}', '{{ key }}')">
        {% if config.action == 'classify_story' %}
            <i class="bi bi-magic me-2"></i>{{ config.value }}
        {% elif config.action == 'create_database_structure' %}
            <i class="bi bi-folder-plus me-2"></i>{{ config.value }}
        {% else %}
            <i class="bi bi-gear me-2"></i>{{ config.value }}
        {% endif %}
    </button>
    <div id="{{ key }}_status" class="mt-2" style="display: none;">
        <div class="alert alert-info" role="alert">
            <i class="bi bi-hourglass-split me-2"></i>正在处理中，请稍候...
        </div>
    </div>
{% elif config.type == 'select' %}
    <select class="form-select" id="{{ key }}" name="{{ key }}">
        {% for option in config.options %}
        <option value="{{ option }}" {% if option == config.value %}selected{% endif %}>
            {% if config.option_labels and config.option_labels[option] %}
                {{ config.option_labels[option] }}
            {% else %}
                {{ option }}
            {% endif %}
        </option>
        {% endfor %}
    </select>
{% elif config.get('type') == 'hidden' %}
    <!-- 隐藏配置项，不显示在界面上 -->
    <input type="hidden"
        id="{{ key }}" name="{{ key }}"
        value="{{ config.value }}">
{% else %}
    <input type="text" class="form-control"
        id="{{ key }}" name="{{ key }}"
        value="{{ config.value }}">
{% endif %}
    {% endmacro %}

    <!-- 添加全局脚本处理滑块控件 -->
    <script>
        // 更新数值滑块的值
        function updateRangeValue(key, value) {
            const display = document.getElementById(`${key}_display`);
            const input = document.getElementById(key);
            if (display) {
                display.textContent = value;
            }
            if (input) {
                input.value = value;
            }
        }

        // 处理boolean开关状态变化
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有boolean类型的开关添加事件监听器
            const booleanSwitches = document.querySelectorAll('input[type="checkbox"][id$="_enabled"], input[type="checkbox"][id$="_rules"], input[type="checkbox"][id$="_sync"]');
            booleanSwitches.forEach(function(switchElement) {
                switchElement.addEventListener('change', function() {
                    const label = document.getElementById(this.id + '_label');
                    if (label) {
                        label.textContent = this.checked ? '启用' : '停用';
                    }
                });
            });
        });
    </script>
</head>
<body>
    {% include 'navbar.html' %}

    <main class="container-fluid py-4">
        <div class="row">
            <!-- 左侧基础配置 -->
            <div class="col-md-6 pe-md-2">
                <div class="config-section h-100">
                    <h4 class="mb-4">
                        <i class="bi bi-gear-fill me-2"></i>基础配置
                        <div class="float-end">
                            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="exportConfigBtn">
                                <i class="bi bi-upload me-1"></i>导出配置
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="importConfigBtn">
                                <i class="bi bi-download me-1"></i>导入配置
                            </button>
                        </div>
                    </h4>
                    <form id="configForm">
                        {% for group_name, configs in config_groups.items() %}
                            {% if group_name == '基础配置' %}
                            <div class="accordion mb-3">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#{{ group_name|replace(' ', '-') }}">
                                            {{ group_name }}
                                        </button>
                                    </h2>
                                    <div id="{{ group_name|replace(' ', '-') }}" class="accordion-collapse collapse show">
                                        <div class="accordion-body">
                                            {% for key, config in configs.items() %}
                                            {% if config.get('type') != 'hidden' %}
                                            <div class="mb-4">
                                                {{ render_config_item(key, config) }}
                                            </div>
                                            {% else %}
                                                {{ render_config_item(key, config) }}
                                            {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </form>
                </div>
            </div>

            <!-- 右侧其他配置 -->
            <div class="col-md-6 ps-md-2">
                <div class="config-section h-100">
                    <h4 class="mb-4">
                        <i class="bi bi-sliders me-2"></i>高级配置
                    </h4>



                    <form id="otherConfigForm">
                        {% for group_name, configs in config_groups.items() %}
                            {% if group_name != '基础配置' and group_name != '定时任务配置' %}  {# 排除定时任务配置 #}
                            <div class="accordion mb-3">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#{{ group_name|replace(' ', '-') }}">
                                            {{ group_name }}
                                        </button>
                                    </h2>
                                    <div id="{{ group_name|replace(' ', '-') }}" class="accordion-collapse collapse">
                                        <div class="accordion-body">
                                            {% for key, config in configs.items() %}
                                            {% if config.get('type') != 'hidden' %}
                                            <div class="mb-4">
                                                {{ render_config_item(key, config) }}
                                            </div>
                                            {% else %}
                                                {{ render_config_item(key, config) }}
                                            {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}

                        <!-- 单独添加定时任务配置部分 -->
                        <div class="accordion mb-3">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target="#schedule-settings">
                                        定时任务配置
                                    </button>
                                </h2>
                                <div id="schedule-settings" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        <!-- 定时任务配置的内容 -->
                                        <input type="hidden"
                                               id="TASKS"
                                               name="TASKS"
                                               value='{{ tasks_json|safe }}'>

                                        <!-- 添加和管理任务的按钮 -->
                                        <div class="mb-3 list-group">
                                            <a href="#" class="list-group-item list-group-item-action"
                                               data-bs-toggle="modal" data-bs-target="#addTaskModal">
                                                <i class="bi bi-plus-lg me-2"></i>
                                                添加定时任务
                                                <i class="bi bi-chevron-right float-end mt-1"></i>
                                            </a>
                                        </div>

                                        <!-- 任务列表管理按钮 -->
                                        <div class="mb-3 list-group">
                                            <a href="#" class="list-group-item list-group-item-action"
                                               data-bs-toggle="modal" data-bs-target="#taskListModal">
                                                <i class="bi bi-list-ul me-2"></i>
                                                任务列表管理
                                                <i class="bi bi-chevron-right float-end mt-1"></i>
                                            </a>
                                        </div>

                                        <!-- 说明文字 -->
                                        <div class="text-muted small">
                                            <i class="bi bi-info-circle me-1"></i>
                                            可以添加定时发送消息的任务，支持Cron表达式和时间间隔两种方式
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                    </form>
                </div>
            </div>
        </div>

        <!-- 保存按钮固定在底部 -->
        <div class="position-fixed bottom-0 start-0 w-100 bg-body p-3 shadow-lg">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <button type="button"
                            class="btn btn-primary btn-lg w-100"
                            id="saveButton">
                            <i class="bi bi-save me-2"></i>保存所有设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div class="toast-container">
        <div class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-body">
                <i class="bi bi-exclamation-triangle-fill text-danger"></i>
                <span class="toast-message"></span>
            </div>
        </div>
    </div>

    <!-- 在 body 标签下添加顶部通知区域 -->
    <div class="position-fixed top-0 start-50 translate-middle-x p-3 notification-container">
        <div id="saveNotification" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <span id="saveNotificationMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="关闭通知"></button>
            </div>
        </div>
    </div>

    <!-- 监听用户列表为空的确认对话框 -->
    <div class="modal fade" id="emptyListenListModal" tabindex="-1" aria-labelledby="emptyListenListModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="emptyListenListModalLabel">
                        <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>提示
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>您未填写监听用户，是否继续保存？</p>
                    <p class="text-muted small">未填写监听用户将导致机器人无法响应任何消息。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">否</button>
                    <button type="button" class="btn btn-secondary" id="confirmSaveBtn">是</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 人设选择提醒对话框 -->
    <div class="modal fade" id="avatarReminderModal" tabindex="-1" aria-labelledby="avatarReminderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="avatarReminderModalLabel">
                        <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>人设提醒
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <p>您选择的人设是：<span id="selectedAvatarName" class="fw-bold"></span></p>
                    <p class="text-muted small">请确认这是您要使用的人设。如需修改人设内容，请前往"角色设定"页面。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAvatarBtn">确认并保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务列表模态框 -->
    <div class="modal fade" id="taskListModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-list-check me-2"></i>定时任务列表
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div id="taskListContainer" class="list-group">
                        <!-- 默认显示无任务提示 -->
                        <div class="text-center text-muted p-4">
                            <i class="bi bi-inbox fs-2"></i>
                            <p class="mt-2">暂无定时任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加任务模态框 -->
    <div class="modal fade" id="addTaskModal" tabindex="-1" aria-labelledby="addTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTaskModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>添加定时任务
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <div class="row">
                            <!-- 左侧：基本信息 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-key me-2"></i>任务ID
                                    </label>
                                    <input type="text" class="form-control" id="taskId" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-person me-2"></i>发送对象
                                    </label>
                                    <select class="form-select" id="taskChatId" required>
                                        <option value="">请选择发送对象</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-chat-text me-2"></i>消息内容
                                    </label>
                                    <textarea class="form-control" id="taskContent" rows="3" required></textarea>
                                </div>
                            </div>

                            <!-- 右侧：定时设置 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-alarm me-2"></i>定时类型
                                    </label>
                                    <select class="form-select" id="scheduleType" onchange="toggleScheduleInput()">
                                        <option value="cron">Cron表达式</option>
                                        <option value="interval">时间间隔</option>
                                    </select>
                                </div>

                                <!-- Cron表达式设置 -->
                                <div id="cronInputGroup" class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-calendar3 me-2"></i>执行时间
                                    </label>
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <select class="form-select" id="cronHour">
                                                <option value="*">每小时</option>
                                                <option value="0">0点</option>
                                                <option value="1">1点</option>
                                                <option value="2">2点</option>
                                                <option value="3">3点</option>
                                                <option value="4">4点</option>
                                                <option value="5">5点</option>
                                                <option value="6">6点</option>
                                                <option value="7">7点</option>
                                                <option value="8">8点</option>
                                                <option value="9">9点</option>
                                                <option value="10">10点</option>
                                                <option value="11">11点</option>
                                                <option value="12">12点</option>
                                                <option value="13">13点</option>
                                                <option value="14">14点</option>
                                                <option value="15">15点</option>
                                                <option value="16">16点</option>
                                                <option value="17">17点</option>
                                                <option value="18">18点</option>
                                                <option value="19">19点</option>
                                                <option value="20">20点</option>
                                                <option value="21">21点</option>
                                                <option value="22">22点</option>
                                                <option value="23">23点</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <select class="form-select" id="cronMinute">
                                                <option value="0">整点</option>
                                                <option value="30">30分</option>
                                                <option value="15">15分</option>
                                                <option value="45">45分</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <label class="form-label">
                                            <i class="bi bi-calendar-week me-2"></i>执行周期
                                        </label>
                                        <div class="btn-group w-100 flex-wrap" role="group">
                                            <input type="checkbox" class="btn-check" id="cronWeekday1" autocomplete="off">
                                            <label class="btn btn-outline-primary flex-fill" for="cronWeekday1">一</label>

                                            <input type="checkbox" class="btn-check" id="cronWeekday2" autocomplete="off">
                                            <label class="btn btn-outline-primary flex-fill" for="cronWeekday2">二</label>

                                            <input type="checkbox" class="btn-check" id="cronWeekday3" autocomplete="off">
                                            <label class="btn btn-outline-primary flex-fill" for="cronWeekday3">三</label>

                                            <input type="checkbox" class="btn-check" id="cronWeekday4" autocomplete="off">
                                            <label class="btn btn-outline-primary flex-fill" for="cronWeekday4">四</label>

                                            <input type="checkbox" class="btn-check" id="cronWeekday5" autocomplete="off">
                                            <label class="btn btn-outline-primary flex-fill" for="cronWeekday5">五</label>

                                            <input type="checkbox" class="btn-check" id="cronWeekday6" autocomplete="off">
                                            <label class="btn btn-outline-primary flex-fill" for="cronWeekday6">六</label>

                                            <input type="checkbox" class="btn-check" id="cronWeekday7" autocomplete="off">
                                            <label class="btn btn-outline-primary flex-fill" for="cronWeekday7">日</label>
                                        </div>
                                    </div>
                                    <!-- 添加隐藏的cron表达式输入框 -->
                                    <input type="hidden" id="cronExpression" value="">
                                </div>

                                <!-- 时间间隔设置 -->
                                <div id="intervalInputGroup" class="mb-3" style="display: none;">
                                    <label class="form-label">
                                        <i class="bi bi-hourglass-split me-2"></i>间隔时间
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="intervalValue"
                                               min="1" step="1" placeholder="输入数值">
                                        <select class="form-select" id="intervalUnit" style="max-width: 120px;">
                                            <option value="60">分钟</option>
                                            <option value="3600">小时</option>
                                            <option value="86400">天</option>
                                        </select>
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        常用间隔：
                                        <div class="btn-group mt-1">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(30, '60')">30分钟</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(1, '3600')">1小时</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(2, '3600')">2小时</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setInterval(24, '3600')">1天</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 预览 -->
                                <div class="mt-3">
                                    <label class="form-label">
                                        <i class="bi bi-eye me-2"></i>执行时间预览
                                    </label>
                                    <div id="schedulePreview" class="form-control">
                                        请选择定时类型和设置
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveTask()">保存</button>
                </div>
            </div>
        </div>
    </div>



    <!-- 在页面中添加隐藏的配置数据 -->
    <script id="config_data" type="application/json">
        {{ config_json|safe }}
    </script>

    <script>
        // 初始化工具提示
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // 初始化所有开关滑块
        function initializeSwitches() {
            // 获取所有开关滑块（包括role="switch"和普通checkbox）
            const switches = document.querySelectorAll('input[type="checkbox"][role="switch"], input[type="checkbox"].form-check-input');
            switches.forEach(switchElem => {
                // 获取对应的标签
                const label = document.getElementById(switchElem.id + '_label');
                if (label) {
                    // 更新标签文本
                    label.textContent = switchElem.checked ? '启用' : '停用';
                }
                
                // 特殊处理网络搜索和人设重置相关的开关，确保与服务器配置同步
                if (switchElem.id === 'NETWORK_SEARCH_ENABLED' || 
                    switchElem.id === 'INTELLIGENT_SEARCH_ENABLED' || 
                    switchElem.id === 'WEBLENS_ENABLED' ||
                    switchElem.id === 'CHARACTER_RESET_ENABLED') {
                    
                    // 尝试从隐藏的配置数据中获取正确值
                    try {
                        const configDataElement = document.getElementById('config_data');
                        if (configDataElement && configDataElement.textContent) {
                            const configData = JSON.parse(configDataElement.textContent);
                            const networkSearchConfig = configData['网络搜索配置'];
                            
                            if (networkSearchConfig && networkSearchConfig[switchElem.id] !== undefined) {
                                const correctValue = networkSearchConfig[switchElem.id];
                                switchElem.checked = correctValue;
                                if (label) {
                                    label.textContent = correctValue ? '启用' : '停用';
                                }
                                console.log(`同步网络搜索开关 ${switchElem.id}: ${correctValue ? '启用' : '停用'} (从配置数据)`);
                            }
                            
                            // 同步人设配置的开关
                            const characterConfig = configData['人设配置'];
                            if (characterConfig && characterConfig[switchElem.id] !== undefined) {
                                const correctValue = characterConfig[switchElem.id];
                                switchElem.checked = correctValue;
                                if (label) {
                                    label.textContent = correctValue ? '启用' : '停用';
                                }
                                console.log(`同步人设配置开关 ${switchElem.id}: ${correctValue ? '启用' : '停用'} (从配置数据)`);
                            }
                        }
                    } catch (e) {
                        console.warn('无法从配置数据同步开关状态:', e);
                    }
                }
                
                console.log(`初始化开关 ${switchElem.id}: ${switchElem.checked ? '启用' : '停用'}`);
            });
            
            // 同步人设重置间隔滑块
            const characterResetInterval = document.getElementById('CHARACTER_RESET_INTERVAL');
            if (characterResetInterval) {
                try {
                    const configDataElement = document.getElementById('config_data');
                    if (configDataElement && configDataElement.textContent) {
                        const configData = JSON.parse(configDataElement.textContent);
                        const characterConfig = configData['人设配置'];
                        
                        if (characterConfig && characterConfig['CHARACTER_RESET_INTERVAL'] !== undefined) {
                            const correctValue = characterConfig['CHARACTER_RESET_INTERVAL'];
                            characterResetInterval.value = correctValue;
                            
                            // 更新显示值
                            const displayElement = document.getElementById('CHARACTER_RESET_INTERVAL_display');
                            if (displayElement) {
                                displayElement.textContent = correctValue;
                            }
                            
                            // 更新滑块位置
                            const sliderElement = document.getElementById('CHARACTER_RESET_INTERVAL_slider');
                            if (sliderElement) {
                                sliderElement.value = correctValue;
                            }
                            
                            console.log(`同步人设重置间隔滑块: ${correctValue} (从配置数据)`);
                        }
                    }
                } catch (e) {
                    console.warn('无法从配置数据同步人设重置间隔滑块:', e);
                }
            }
        }

        // 全局变量：存储待保存的配置更改
        let pendingConfigChanges = {};
        let hasUnsavedChanges = false;
        let isInitialState = true; // 标记是否为初始状态（未修改过）
        let isInitializing = true; // 标记是否正在初始化

        // 标记配置已更改但未保存
        function markConfigChanged(key, value) {
            // 如果还在初始化阶段，不标记为已更改
            if (isInitializing) {
                console.log(`初始化阶段，忽略配置项 ${key} 的更改`);
                return;
            }

            pendingConfigChanges[key] = value;
            hasUnsavedChanges = true;
            isInitialState = false; // 不再是初始状态

            // 更新保存按钮状态
            updateSaveButtonState();

            console.log(`配置项 ${key} 已更改为: ${value} (待保存)`);
        }

        // 更新保存按钮状态
        function updateSaveButtonState() {
            const saveButton = document.getElementById('saveButton');
            if (saveButton) {
                if (hasUnsavedChanges) {
                    // 有未保存更改 - 粉色
                    saveButton.innerHTML = '<i class="bi bi-save me-2"></i>保存所有设置 <span class="badge" style="background: linear-gradient(135deg, #f8bbd9, #f472b6); color: white; box-shadow: 0 2px 8px rgba(248, 187, 217, 0.3);">有未保存更改</span>';
                    saveButton.style.background = 'linear-gradient(135deg, #f8bbd9, #f472b6)';
                    saveButton.style.borderColor = '#f472b6';
                    saveButton.style.color = 'white';
                    saveButton.style.boxShadow = '0 4px 12px rgba(248, 187, 217, 0.4)';
                    saveButton.classList.remove('btn-primary', 'btn-warning');
                } else if (isInitialState) {
                    // 初始状态（未修改过） - 淡蓝色
                    saveButton.innerHTML = '<i class="bi bi-save me-2"></i>保存所有设置';
                    saveButton.style.background = 'linear-gradient(135deg, #bfdbfe, #60a5fa)';
                    saveButton.style.borderColor = '#60a5fa';
                    saveButton.style.color = 'white';
                    saveButton.style.boxShadow = '0 4px 12px rgba(96, 165, 250, 0.3)';
                    saveButton.classList.remove('btn-primary', 'btn-warning');
                } else {
                    // 已保存状态 - 保持淡蓝色（这个状态由showSaveSuccessState处理）
                    saveButton.innerHTML = '<i class="bi bi-save me-2"></i>保存所有设置';
                    saveButton.style.background = 'linear-gradient(135deg, #bfdbfe, #60a5fa)';
                    saveButton.style.borderColor = '#60a5fa';
                    saveButton.style.color = 'white';
                    saveButton.style.boxShadow = '0 4px 12px rgba(96, 165, 250, 0.3)';
                    saveButton.classList.remove('btn-primary', 'btn-warning');
                }
            }
        }

        // 显示保存成功状态
        function showSaveSuccessState() {
            const saveButton = document.getElementById('saveButton');
            if (saveButton) {
                saveButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>保存成功';
                saveButton.style.background = 'linear-gradient(135deg, #bfdbfe, #60a5fa)';
                saveButton.style.borderColor = '#60a5fa';
                saveButton.style.color = 'white';
                saveButton.style.boxShadow = '0 4px 12px rgba(96, 165, 250, 0.3)';
                saveButton.classList.remove('btn-primary', 'btn-warning');

                // 清除待保存的更改，标记为非初始状态
                pendingConfigChanges = {};
                hasUnsavedChanges = false;
                isInitialState = false; // 已经保存过，不再是初始状态
                // 不调用 updateSaveButtonState()，保持当前的淡蓝色样式
            }
        }

        // 清除待保存的更改
        function clearPendingChanges() {
            pendingConfigChanges = {};
            hasUnsavedChanges = false;
            isInitialState = false; // 清除更改后不再是初始状态
            updateSaveButtonState();
        }

        // 初始化所有输入框的change事件监听器
        function initializeInputChangeListeners() {
            // 为所有输入框、选择框、文本域添加change事件监听器
            const inputs = document.querySelectorAll('input[id], select[id], textarea[id]');
            inputs.forEach(input => {
                // 跳过一些特殊的输入框
                if (input.type === 'file' || input.type === 'button' || input.type === 'submit' ||
                    input.id === 'saveButton' || input.id.includes('custom') ||
                    input.id.includes('schedule') || input.id.includes('task')) {
                    return;
                }

                // 添加change事件监听器
                input.addEventListener('change', function() {
                    let value = this.value;

                    // 对于复选框，使用checked属性
                    if (this.type === 'checkbox') {
                        value = this.checked;
                    }

                    // 特别处理智能搜索开关，添加调试信息
                    if (this.id === 'INTELLIGENT_SEARCH_ENABLED') {
                        console.log(`智能搜索开关状态变化: ${value}`);
                    }

                    // 标记配置已更改
                    markConfigChanged(this.id, value);
                });
            });

        }

        // 页面加载时初始化所有配置数据
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM内容加载完成，开始初始化...');
            
            // 初始化黑夜模式
            const darkMode = localStorage.getItem('darkMode');
            if (darkMode === 'enabled') {
                document.body.setAttribute('data-bs-theme', 'dark');
                const darkModeToggle = document.getElementById('darkModeToggle');
                if (darkModeToggle) {
                    darkModeToggle.checked = true;
                }
            }

            // 强制设置初始化状态，防止在页面加载时触发配置更改
            isInitializing = true;
            console.log('强制设置初始化状态为true，防止页面加载时触发配置更改');
            
            // 先初始化所有开关滑块，确保它们有正确的初始状态
            initializeSwitches();
            
            // 移除这里的初始化，避免竞态条件，等配置数据加载后再初始化

            // 初始化输入框change事件监听器
            initializeInputChangeListeners();

            // 初始化保存按钮状态（淡蓝色初始状态）
            updateSaveButtonState();

            // 延迟重置状态，确保初始状态正确
            setTimeout(() => {
                if (isInitialState && !hasUnsavedChanges) {
                    updateSaveButtonState();
                    console.log("重置保存按钮为初始状态");
                }
            }, 1000);

            // 获取最新的配置数据
            fetch('/get_all_configs')
                .then(response => response.json())
                .then(data => {
                                            if (data.status === 'success') {
                            console.log("成功获取配置数据");
                            
                            // 保存当前模型值
                            const currentModel = document.getElementById('MODEL').value;
                            console.log("保存当前模型值:", currentModel);
                            
                            // 更新所有配置项
                            updateAllConfigs(data.configs);

                            // 更新任务列表
                            const tasksInput = document.getElementById('TASKS');
                            if (tasksInput && data.tasks) {
                                tasksInput.value = JSON.stringify(data.tasks);
                                updateTaskList();
                            }

                            // 在配置更新后立即初始化开关状态
                            // 注意：这里不调用initializeSwitches()，避免重复初始化
                            // updateAllConfigs函数中已经处理了所有开关的状态
                            
                            // 强制同步所有开关状态，确保UI与配置数据一致
                            console.log('强制同步所有开关状态...');
                            const allSwitches = document.querySelectorAll('input[type="checkbox"][role="switch"], input[type="checkbox"].form-check-input');
                            allSwitches.forEach(switchElement => {
                                // 触发change事件以确保标签更新
                                switchElement.dispatchEvent(new Event('change'));
                            });
                            
                            // 延时处理，确保自定义模型值正确
                            setTimeout(function() {
                                // 确保自定义模型输入框事件监听器已设置
                                if (typeof setupCustomModelInputListeners === 'function') {
                                    setupCustomModelInputListeners();
                                }
                                
                                // 初始化模型选择器
                                if (typeof window.modelSelectHandler === 'function') {
                                    window.modelSelectHandler();
                                }
                                
                                // 特殊检查模型选择器
                                const modelSelect = document.getElementById('model_select');
                                const modelInput = document.getElementById('MODEL');
                                const customModelInput = document.getElementById('customModelInput');
                                const customModelInputField = document.getElementById('customModelInputField');
                                
                                if (modelSelect && modelSelect.value === 'custom' && 
                                    customModelInput && customModelInputField && modelInput.value) {
                                    console.log("确保自定义模型输入框显示并有正确的值");
                                    customModelInput.style.display = 'block';
                                    customModelInputField.value = modelInput.value;
                                }

                                // 初始化完成，允许标记配置更改
                                setTimeout(() => {
                                    // 重置所有更改标记
                                    pendingConfigChanges = {};
                                    hasUnsavedChanges = false;
                                    isInitialState = true;
                                    
                                    // 强制再次同步所有开关状态，确保UI与配置数据完全一致
                                    console.log('最终强制同步所有开关状态...');
                                    const finalSwitches = document.querySelectorAll('input[type="checkbox"][role="switch"], input[type="checkbox"].form-check-input');
                                    finalSwitches.forEach(switchElement => {
                                        // 强制触发change事件以确保标签更新
                                        switchElement.dispatchEvent(new Event('change'));
                                    });
                                    
                                    // 最后才允许标记配置更改
                                    isInitializing = false;
                                    console.log("页面初始化完成，现在可以检测配置更改");
                                    
                                    // 确保按钮状态正确
                                    updateSaveButtonState();
                                    console.log("最终确认保存按钮为初始状态");
                                    
                                    // 验证所有开关状态是否正确
                                    const switches = document.querySelectorAll('input[type="checkbox"][role="switch"], input[type="checkbox"].form-check-input');
                                    switches.forEach(switchElem => {
                                        const label = document.getElementById(switchElem.id + '_label');
                                        if (label) {
                                            console.log(`验证开关 ${switchElem.id}: ${switchElem.checked ? '启用' : '停用'}, 标签: ${label.textContent}`);
                                        }
                                    });
                                    
                                    // 添加全局调试信息
                                    console.log('=== 页面初始化完成 ===');
                                    console.log('isInitializing:', isInitializing);
                                    console.log('hasUnsavedChanges:', hasUnsavedChanges);
                                    console.log('isInitialState:', isInitialState);
                                    console.log('pendingConfigChanges:', pendingConfigChanges);
                                    
                                    // 特殊修复：处理三个无法持久化的控件
                                    setTimeout(() => {
                                        fixThreePersistentControls();
                                    }, 2000);
                                }, 1500);
                            }, 300);
                        } else {
                        console.error('获取配置数据失败:', data.message);
                        // 使用本地配置数据
                        fallbackToLocalConfig();
                        // 初始化完成
                        setTimeout(() => {
                            // 开关状态已经在前面初始化过了
                            
                            // 强制再次同步所有开关状态，确保UI与配置数据完全一致
                            console.log('最终强制同步所有开关状态（本地配置）...');
                            const finalSwitches = document.querySelectorAll('input[type="checkbox"][role="switch"], input[type="checkbox"].form-check-input');
                            finalSwitches.forEach(switchElement => {
                                // 强制触发change事件以确保标签更新
                                switchElement.dispatchEvent(new Event('change'));
                            });
                            
                            // 然后重置所有更改标记
                            pendingConfigChanges = {};
                            hasUnsavedChanges = false;
                            isInitialState = true;
                            
                            // 最后才允许标记配置更改
                            isInitializing = false;
                            console.log("页面初始化完成（使用本地配置）");
                            // 确保按钮状态正确
                            updateSaveButtonState();
                            console.log("最终确认保存按钮为初始状态（本地配置）");
                        }, 1500);
                    }
                })
                .catch(error => {
                    console.error('获取配置数据请求失败:', error);
                    // 如果请求失败，使用页面加载时的初始数据
                    fallbackToLocalConfig();
                    // 初始化完成
                    setTimeout(() => {
                        // 强制再次同步所有开关状态，确保UI与配置数据完全一致
                        console.log('最终强制同步所有开关状态（请求失败）...');
                        const finalSwitches = document.querySelectorAll('input[type="checkbox"][role="switch"], input[type="checkbox"].form-check-input');
                        finalSwitches.forEach(switchElement => {
                            // 强制触发change事件以确保标签更新
                            switchElement.dispatchEvent(new Event('change'));
                        });
                        
                        // 然后重置所有更改标记
                        pendingConfigChanges = {};
                        hasUnsavedChanges = false;
                        isInitialState = true;
                        
                        // 最后才允许标记配置更改
                        isInitializing = false;
                        console.log("页面初始化完成（请求失败，使用本地配置）");
                        // 确保按钮状态正确
                        updateSaveButtonState();
                        console.log("最终确认保存按钮为初始状态（请求失败）");
                    }, 1500);
                });

            // 如果获取配置数据失败，使用页面加载时的初始数据
            function fallbackToLocalConfig() {
                console.log("使用页面初始配置数据");
                const tasksInput = document.getElementById('TASKS');
                if (tasksInput) {
                    updateTaskList();
                }
                
                // 初始化API选择器
                const apiSelect = document.getElementById('api_provider_select');
                if (apiSelect && typeof updateApiProvider === 'function') {
                    setTimeout(function() {
                        updateApiProvider(apiSelect.value);
                    }, 300);
                }
                
                // 初始化视觉API选择器
                const visionApiSelect = document.getElementById('vision_api_provider_select');
                if (visionApiSelect && typeof updateVisionApiProvider === 'function') {
                    setTimeout(function() {
                        updateVisionApiProvider(visionApiSelect.value);
                    }, 300);
                }
            }

            // 获取保存按钮
            const saveButton = document.getElementById('saveButton');

            // 添加点击事件监听器
            saveButton.addEventListener('click', function() {
                // 如果没有未保存的更改，直接返回（不显示提示）
                if (!hasUnsavedChanges) {
                    return;
                }

                const mainForm = document.getElementById('configForm');
                const otherForm = document.getElementById('otherConfigForm');
                const config = {};

                // 首先获取所有表单数据
                const formData = new FormData(mainForm);

                // 处理表单数据
                for (let [key, value] of formData.entries()) {
                    processFormValue(config, key, value);
                }

                if (otherForm) {
                    const otherFormData = new FormData(otherForm);
                    for (let [key, value] of otherFormData.entries()) {
                        processFormValue(config, key, value);
                    }
                }

                // 合并待保存的配置更改
                Object.assign(config, pendingConfigChanges);

                // 特别处理复选框（开关滑块）
                // 对于没有在formData中出现的复选框，设置为false
                const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    // 如果复选框没有被选中，它不会出现在formData中
                    if (!formData.has(checkbox.name) && !otherForm?.querySelector(`[name="${checkbox.name}"]`)) {
                        config[checkbox.name] = false;
                        console.log(`设置未选中的复选框 ${checkbox.name} 为 false`);
                    }
                });

                // 特别处理网络搜索相关的开关
                const networkSearchEnabled = document.getElementById('NETWORK_SEARCH_ENABLED');
                const intelligentSearchEnabled = document.getElementById('INTELLIGENT_SEARCH_ENABLED');
                const weblensEnabled = document.getElementById('WEBLENS_ENABLED');
                const networkSearchBaseUrl = document.getElementById('NETWORK_SEARCH_BASE_URL');

                if (networkSearchEnabled) {
                    // 强制设置布尔值
                    config['NETWORK_SEARCH_ENABLED'] = networkSearchEnabled.checked;
                    console.log(`网络搜索开关状态: ${networkSearchEnabled.checked} (类型: ${typeof networkSearchEnabled.checked})`);
                    // 添加到表单中
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'NETWORK_SEARCH_ENABLED';
                    hiddenInput.value = networkSearchEnabled.checked;
                    mainForm.appendChild(hiddenInput);
                }

                if (intelligentSearchEnabled) {
                    // 强制设置布尔值
                    config['INTELLIGENT_SEARCH_ENABLED'] = intelligentSearchEnabled.checked;
                    console.log(`智能联网搜索开关状态: ${intelligentSearchEnabled.checked} (类型: ${typeof intelligentSearchEnabled.checked})`);
                    // 添加到表单中
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'INTELLIGENT_SEARCH_ENABLED';
                    hiddenInput.value = intelligentSearchEnabled.checked ? 'true' : 'false';
                    mainForm.appendChild(hiddenInput);
                }
                
                // 网络搜索API地址已固定为KouriChat
                config['NETWORK_SEARCH_BASE_URL'] = 'https://api.kourichat.com/v1';
                
                // 处理网络搜索API密钥
                const networkSearchApiKey = document.getElementById('NETWORK_SEARCH_API_KEY');
                if (networkSearchApiKey) {
                    config['NETWORK_SEARCH_API_KEY'] = networkSearchApiKey.value;
                }

                if (weblensEnabled) {
                    // 强制设置布尔值
                    config['WEBLENS_ENABLED'] = weblensEnabled.checked;
                    console.log(`网页内容提取开关状态: ${weblensEnabled.checked} (类型: ${typeof weblensEnabled.checked})`);
                    // 添加到表单中
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'WEBLENS_ENABLED';
                    hiddenInput.value = weblensEnabled.checked;
                    mainForm.appendChild(hiddenInput);
                }

                // 特别处理任务数据 - 确保直接从隐藏字段获取
                const tasksInput = document.getElementById('TASKS');
                if (tasksInput) {
                    try {
                        const tasksValue = tasksInput.value;
                        if (tasksValue) {
                            // 解析为JSON对象并添加到配置中
                            config['TASKS'] = JSON.parse(tasksValue);
                        }
                    } catch (e) {
                        // 出错时使用空数组
                        config['TASKS'] = [];
                    }
                }

                // 特别处理群聊配置数据 - 确保直接从隐藏字段获取
                const groupChatInput = document.getElementById('GROUP_CHAT_CONFIG');
                if (groupChatInput) {
                    try {
                        const groupChatValue = groupChatInput.value;
                        if (groupChatValue) {
                            // 解析为JSON对象并添加到配置中
                            config['GROUP_CHAT_CONFIG'] = JSON.parse(groupChatValue);
                            console.log('群聊配置数据已添加到保存配置中:', config['GROUP_CHAT_CONFIG']);
                        } else {
                            config['GROUP_CHAT_CONFIG'] = [];
                        }
                    } catch (e) {
                        console.error('解析群聊配置数据失败:', e);
                        // 出错时使用空数组
                        config['GROUP_CHAT_CONFIG'] = [];
                    }
                }

                // 特别检查温度值
                const temperatureSlider = document.getElementById('TEMPERATURE_slider');
                const temperatureInput = document.getElementById('TEMPERATURE');
                if (temperatureSlider && temperatureInput) {
                    const tempValue = parseFloat(temperatureSlider.value);
                    if (!isNaN(tempValue)) {
                        config['TEMPERATURE'] = tempValue;
                    }
                }

                // 检查页面上是否有用户列表元素
                const userListElement = document.getElementById('selected_users_LISTEN_LIST');
                const hasUsers = userListElement && userListElement.querySelectorAll('.list-group-item').length > 0;

                // 获取人设选择
                const avatarDirSelect = document.querySelector('select[name="AVATAR_DIR"]');
                let avatarPath = '';
                let avatarName = '';

                if (avatarDirSelect && avatarDirSelect.value) {
                    avatarPath = avatarDirSelect.value;
                    // 从路径中提取人设名称
                    avatarName = avatarPath.split('/').pop();
                }

                // 根据是否有用户和人设选择决定是否显示确认对话框
                if (!hasUsers) {
                    // 创建确认对话框
                    const confirmDialog = new bootstrap.Modal(document.getElementById('emptyListenListModal'));
                    confirmDialog.show();

                    // 设置确认按钮的事件
                    document.getElementById('confirmSaveBtn').onclick = function() {
                        confirmDialog.hide();

                        // 检查人设选择
                        if (avatarPath) {
                            // 显示人设提醒对话框
                            document.getElementById('selectedAvatarName').textContent = avatarName;
                            const avatarDialog = new bootstrap.Modal(document.getElementById('avatarReminderModal'));
                            avatarDialog.show();

                            // 设置确认按钮的事件
                            document.getElementById('confirmAvatarBtn').onclick = function() {
                                avatarDialog.hide();
                                saveConfig(config);
                            };
                        } else {
                            saveConfig(config);
                        }
                    };
                } else if (avatarPath) {
                    // 直接显示人设提醒对话框
                    document.getElementById('selectedAvatarName').textContent = avatarName;
                    const avatarDialog = new bootstrap.Modal(document.getElementById('avatarReminderModal'));
                    avatarDialog.show();

                    // 设置确认按钮的事件
                    document.getElementById('confirmAvatarBtn').onclick = function() {
                        avatarDialog.hide();
                        saveConfig(config);
                    };
                } else {
                    // 直接保存配置
                    saveConfig(config);
                }
            });

            // 初始化背景
            fetch('/get_background')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.path) {
                        document.body.style.backgroundImage = `url('${data.path}')`;
                    }
                })
                .catch(error => console.error('Error:', error));
        });

        // 更新所有配置项的函数
        function updateAllConfigs(configs) {
            console.log('更新所有配置项');

            // 确保在更新配置时仍处于初始化状态，防止触发change事件
            const wasInitializing = isInitializing;
            isInitializing = true;
            console.log('updateAllConfigs: 设置初始化状态为true，防止触发change事件');

            // 创建一个队列，存储需要特殊处理的配置项
            const specialConfigs = {
                'MODEL': null,
                'DEEPSEEK_BASE_URL': null,
                'VISION_MODEL': null,
                'VISION_BASE_URL': null
            };

            // 遍历所有配置组和配置项
            for (const groupKey in configs) {
                const group = configs[groupKey];
                for (const configKey in group) {
                    const config = group[configKey];
                    const element = document.getElementById(configKey);
                    if (element) {
                        // 获取实际值，处理各种嵌套结构
                        let value;
                        if (config !== null && typeof config === 'object') {
                            if (config.value !== undefined) {
                                if (typeof config.value === 'object' && config.value.value !== undefined) {
                                    // 处理嵌套格式 {"value": {"value": true, "type": "boolean"}}
                                    value = config.value.value;
                                } else {
                                    value = config.value;
                                }
                            } else {
                                value = config.default !== undefined ? config.default : null;
                            }
                        } else {
                            value = config;
                        }
                        
                        console.log(`设置配置项 ${configKey} = ${JSON.stringify(value)}`);
                        
                        // 保存特殊配置项的值，稍后处理
                        if (configKey in specialConfigs) {
                            specialConfigs[configKey] = value;
                        }

                        // 根据元素类型设置值
                        if (element.type === 'checkbox') {
                            // 确保布尔值正确处理
                            let isChecked = false;

                            // 添加详细的调试日志
                            console.log(`处理复选框 ${configKey}: 原始值=${value}, 类型=${typeof value}`);

                            if (typeof value === 'boolean') {
                                isChecked = value;
                            } else if (typeof value === 'string') {
                                // 支持 'on'/'off' 和 'true'/'false' 格式
                                isChecked = value.toLowerCase() === 'true' || value === 'on';
                            } else if (typeof value === 'number') {
                                isChecked = value === 1;
                            } else {
                                isChecked = Boolean(value);
                            }

                            console.log(`复选框 ${configKey}: 计算后状态=${isChecked}`);
                            element.checked = isChecked;

                            // 强制触发change事件以确保UI更新
                            // 注意：这里可能会触发markConfigChanged，但由于isInitializing=true，会被忽略
                            element.dispatchEvent(new Event('change'));
                            console.log(`复选框 ${configKey}: 已触发change事件，当前checked=${element.checked}`);

                            // 如果是开关滑块，更新标签
                            const label = document.getElementById(element.id + '_label');
                            if (label) {
                                label.textContent = element.checked ? '启用' : '停用';
                                console.log(`更新开关 ${element.id}: ${element.checked ? '启用' : '停用'}`);
                            }
                        } else if (element.tagName === 'SELECT') {
                            if (value !== null && value !== undefined) {
                                element.value = value;
                            }
                        } else {
                            if (value !== null && value !== undefined) {
                                element.value = value;
                            }
                        }

                        // 特殊处理滑块
                        const slider = document.getElementById(`${configKey}_slider`);
                        if (slider) {
                            if (value !== null && value !== undefined) {
                                slider.value = value;
                                const display = document.getElementById(`${configKey}_display`);
                                if (display) {
                                    display.textContent = typeof value === 'number' ?
                                        (configKey === 'TEMPERATURE' ? value.toFixed(1) : value) :
                                        value;
                                }
                            }
                        }

                        // 特殊处理视觉模型
                        if (configKey === 'VISION_MODEL') {
                            const apiSelect = document.getElementById('vision_api_provider_select');
                            const customModelInput = document.getElementById('customVisionModelInput');
                            const modelSelect = document.getElementById('vision_model_select');

                            // 如果是自定义API提供商，确保自定义模型输入框显示值
                            if (apiSelect && apiSelect.value === 'custom' && value) {
                                // 显示自定义输入框并设置值
                                if (customModelInput) {
                                    customModelInput.style.display = 'block';
                                    customModelInput.querySelector('input').value = value;
                                }

                                // 设置下拉框为自定义选项
                                if (modelSelect) {
                                    // 确保有自定义选项
                                    if (!modelSelect.querySelector('option[value="custom"]')) {
                                        modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
                                    }
                                    modelSelect.value = 'custom';
                                }
                            }
                            // 常规模型选项
                            else if (modelSelect && value) {
                                // 检查是否为预设模型
                                let found = false;
                                for (let i = 0; i < modelSelect.options.length; i++) {
                                    if (modelSelect.options[i].value === value) {
                                        modelSelect.value = value;
                                        found = true;
                                        break;
                                    }
                                }

                                // 如果不是预设模型，可能是自定义值
                                if (!found && value) {
                                    // 添加自定义选项
                                    if (!modelSelect.querySelector('option[value="custom"]')) {
                                        modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
                                    }
                                    modelSelect.value = 'custom';

                                    // 显示自定义输入框
                                    if (customModelInput) {
                                        customModelInput.style.display = 'block';
                                        customModelInput.querySelector('input').value = value;
                                    }
                                }
                            }
                        }

                        // 特殊处理用户列表
                        if (configKey === 'LISTEN_LIST') {
                            let userList = [];
                            
                            // 处理不同形式的值
                            if (Array.isArray(value)) {
                                userList = value;
                            } else if (typeof value === 'string') {
                                userList = value.split(',').map(item => item.trim()).filter(item => item);
                            } else if (value && typeof value === 'object' && value.value) {
                                if (Array.isArray(value.value)) {
                                    userList = value.value;
                                } else if (typeof value.value === 'string') {
                                    userList = value.value.split(',').map(item => item.trim()).filter(item => item);
                                }
                            }
                            
                            if (userList.length > 0) {
                                const userListElement = document.getElementById(`selected_users_${configKey}`);
                                if (userListElement) {
                                    userListElement.innerHTML = '';
                                    userList.forEach(user => {
                                        if (user) {
                                            const userDiv = document.createElement('div');
                                            userDiv.className = 'list-group-item d-flex justify-content-between align-items-center';
                                            userDiv.innerHTML = `
                                                ${user}
                                                <button type="button" class="btn btn-danger btn-sm" onclick="removeUser('${configKey}', '${user}')">
                                                    <i class="bi bi-x-lg"></i>
                                                </button>
                                            `;
                                            userListElement.appendChild(userDiv);
                                        }
                                    });
                                    
                                    // 同时更新隐藏的输入字段
                                    if (element) {
                                        element.value = userList.join(',');
                                    }
                                }
                            }
                        }

                        // 特殊处理群聊配置 - 只在初始加载时更新，避免覆盖用户修改
                        if (configKey === 'GROUP_CHAT_CONFIG') {
                            // 只有在群聊配置还未初始化时才加载服务器数据
                            if (!window.groupChatConfigs || window.groupChatConfigs.length === 0) {
                                let groupChatConfigData = [];
                                
                                // 处理不同形式的值
                                if (Array.isArray(value)) {
                                    groupChatConfigData = value;
                                } else if (typeof value === 'string') {
                                    try {
                                        groupChatConfigData = JSON.parse(value);
                                    } catch (e) {
                                        console.error('解析群聊配置失败:', e);
                                        groupChatConfigData = [];
                                    }
                                } else if (value && typeof value === 'object' && value.value) {
                                    if (Array.isArray(value.value)) {
                                        groupChatConfigData = value.value;
                                    } else if (typeof value.value === 'string') {
                                        try {
                                            groupChatConfigData = JSON.parse(value.value);
                                        } catch (e) {
                                            console.error('解析群聊配置失败:', e);
                                            groupChatConfigData = [];
                                        }
                                    }
                                }
                                
                                console.log('初始加载群聊配置数据:', groupChatConfigData);
                                
                                // 更新全局变量和界面
                                window.groupChatConfigs = groupChatConfigData;
                                
                                // 更新隐藏字段的值
                                if (element) {
                                    element.value = JSON.stringify(groupChatConfigData);
                                }
                                
                                // 重新渲染群聊配置列表
                                setTimeout(() => {
                                    if (typeof renderGroupChatConfigList === 'function') {
                                        renderGroupChatConfigList();
                                    }
                                }, 100);
                            } else {
                                console.log('群聊配置已初始化，跳过服务器数据覆盖，保持用户修改');
                            }
                        }

                        // 特殊处理图像识别API配置
                        if (configKey === 'VISION_BASE_URL') {
                            // 获取值（可能在不同层级）
                            let baseUrl = value;
                            if (value && typeof value === 'object' && value.value) {
                                baseUrl = value.value;
                            }
                            
                            if (!baseUrl) continue;
                            
                            // 触发BASE_URL选择器更新
                            const apiSelect = document.getElementById('vision_api_provider_select');
                            if (apiSelect) {
                                // 根据值查找匹配的选项
                                let found = false;
                                for (let i = 0; i < apiSelect.options.length; i++) {
                                    const option = apiSelect.options[i];
                                    if (option.dataset.url === baseUrl) {
                                        apiSelect.value = option.value;
                                        updateVisionApiProvider(option.value);
                                        found = true;
                                        break;
                                    }
                                }

                                // 如果没有找到匹配项，使用自定义选项
                                if (!found && baseUrl) {
                                    apiSelect.value = 'custom';
                                    if (typeof showCustomVisionApiInput === 'function') {
                                        showCustomVisionApiInput(baseUrl);
                                    }
                                    if (typeof updateVisionModelSelect === 'function') {
                                        updateVisionModelSelect('custom');
                                    }
                                }
                            }
                        }
                        // 特殊处理DEEPSEEK API配置
                        else if (configKey === 'DEEPSEEK_BASE_URL') {
                            // 获取值（可能在不同层级）
                            let baseUrl = value;
                            if (value && typeof value === 'object' && value.value) {
                                baseUrl = value.value;
                            }
                            
                            if (!baseUrl) continue;
                            
                            // 触发API提供商选择器更新
                            const apiSelect = document.getElementById('api_provider_select');
                            if (apiSelect) {
                                // 根据值查找匹配的选项
                                let found = false;
                                for (let i = 0; i < apiSelect.options.length; i++) {
                                    const option = apiSelect.options[i];
                                    if (option.dataset.url === baseUrl) {
                                        apiSelect.value = option.value;
                                        updateApiProvider(option.value);
                                        found = true;
                                        break;
                                    }
                                }

                                // 如果没有找到匹配项，使用自定义选项
                                if (!found && baseUrl) {
                                    apiSelect.value = 'custom';
                                    showCustomApiInput(baseUrl);
                                    updateModelSelect('custom');
                                }
                            }
                        }
                        else if (configKey === 'VISION_MODEL') {
                            // 获取值（可能在不同层级）
                            let modelValue = value;
                            if (value && typeof value === 'object' && value.value) {
                                modelValue = value.value;
                            }
                            
                            if (!modelValue) continue;
                            
                            // 尝试更新模型选择器
                            const modelSelect = document.getElementById('vision_model_select');
                            const customModelInput = document.getElementById('customVisionModelInput');

                            if (modelSelect && modelValue) {
                                // 检查是否为预设选项
                                let option = modelSelect.querySelector(`option[value="${modelValue}"]`);

                                if (option) {
                                    // 如果是预设选项，直接选择
                                    modelSelect.value = modelValue;
                                } else {
                                    // 如果不是预设选项，可能是自定义模型
                                    // 确保有自定义选项
                                    if (!modelSelect.querySelector('option[value="custom"]')) {
                                        modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
                                    }

                                    // 设置为自定义选项
                                    modelSelect.value = 'custom';

                                    // 显示自定义输入框
                                    if (customModelInput) {
                                        customModelInput.style.display = 'block';
                                        customModelInput.querySelector('input').value = modelValue;
                                    }
                                }
                            }
                        }
                        else if (configKey === 'MODEL') {
                            // 获取值（可能在不同层级）
                            let modelValue = value;
                            if (value && typeof value === 'object' && value.value) {
                                modelValue = value.value;
                            }
                            
                            if (!modelValue) continue;
                            
                            // 尝试更新LLM模型选择器
                            const modelSelect = document.getElementById('model_select');
                            const customModelInput = document.getElementById('customModelInput');

                            if (modelSelect && modelValue) {
                                // 检查是否为预设选项
                                let option = modelSelect.querySelector(`option[value="${modelValue}"]`);

                                if (option) {
                                    // 如果是预设选项，直接选择
                                    modelSelect.value = modelValue;
                                } else {
                                    // 如果不是预设选项，可能是自定义模型
                                    // 确保有自定义选项
                                    if (!modelSelect.querySelector('option[value="custom"]')) {
                                        modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
                                    }

                                    // 设置为自定义选项
                                    modelSelect.value = 'custom';

                                    // 显示自定义输入框
                                    if (customModelInput) {
                                        customModelInput.style.display = 'block';
                                        const inputField = customModelInput.querySelector('input');
                                        if (inputField) {
                                            inputField.value = modelValue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 最后确保所有开关状态正确同步
            // initializeSwitches(); // 注释掉，避免重复初始化
            
            // 恢复初始化状态
            isInitializing = wasInitializing;
            console.log('配置更新完成，恢复初始化状态:', isInitializing);
        }

        // 保存配置的函数
        function saveConfig(config) {

            // 在发送前进行最终数据格式检查
            for (const key in config) {
                // 确保列表类型的数据是数组而非字符串
                if (key === 'LISTEN_LIST' && typeof config[key] === 'string') {
                    config[key] = config[key].split(',')
                        .map(item => item.trim())
                        .filter(item => item);
                }
                // 确保群聊配置是数组格式
                else if (key === 'GROUP_CHAT_CONFIG') {
                    if (typeof config[key] === 'string') {
                        try {
                            config[key] = JSON.parse(config[key]);
                        } catch (e) {
                            console.error('解析群聊配置失败:', e);
                            config[key] = [];
                        }
                    } else if (!Array.isArray(config[key])) {
                        config[key] = [];
                    }
                }
                // 确保数字类型的数据是数字而非字符串
                else if (['MAX_TOKEN', 'TEMPERATURE', 'VISION_TEMPERATURE',
                          'OPENAI_TTS_TEMPERATURE', 'OPENAI_TTS_TOP_P', 'OPENAI_TTS_SPEED',
                          'MIN_COUNTDOWN_HOURS', 'MAX_COUNTDOWN_HOURS', 'MAX_GROUPS', 'QUEUE_TIMEOUT',
                          'MEMORY_ARCHIVE_DAYS', 'MAX_STORY_RESULTS', 'CHARACTER_RESET_INTERVAL'].includes(key)) {
                    const numValue = parseFloat(config[key]);
                    if (!isNaN(numValue)) {
                        config[key] = numValue;
                        // 对于整数类型配置，转为整数
                        if (['MAX_TOKEN', 'MAX_GROUPS', 'QUEUE_TIMEOUT', 'MEMORY_ARCHIVE_DAYS', 'MAX_STORY_RESULTS'].includes(key)) {
                            config[key] = Math.round(numValue);
                        }
                    }
                }
                // 确保布尔类型的数据是布尔值
                else if (['NETWORK_SEARCH_ENABLED', 'INTELLIGENT_SEARCH_ENABLED', 'WEBLENS_ENABLED', 'VOICE_CALL_ANTI_ECHO_ENABLED', 'USE_MULTIMODAL_LLM',
                         'ENABLE_STORY_DATABASE', 'AUTO_MEMORY_SUMMARY', 'STORY_AUTO_CLASSIFY', 'STORY_QUERY_ENABLED', 'AUTO_ARCHIVE_MEMORY',
                         'ENABLE_AUTO_RECONNECT', 'EMAIL_ENABLED', 'CHARACTER_RESET_ENABLED', 'BRACKETS_FILTERING_ENABLED', 
                         'BRACKETS_FILTER_ROUND', 'BRACKETS_FILTER_SQUARE', 'BRACKETS_FILTER_CURLY'].includes(key)) {
                    // 对于开关滑块，检查元素是否存在
                    const checkbox = document.getElementById(key);
                    if (checkbox && checkbox.type === 'checkbox') {
                        // 使用元素的checked属性，保持布尔值格式
                        config[key] = checkbox.checked;
                        console.log(`保存配置 - 布尔开关 ${key}: 元素状态=${checkbox.checked}, 保存值=${config[key]}`);
                    } else {
                        // 如果元素不存在或不是复选框，则尝试解析值
                        if (typeof config[key] === 'string') {
                            config[key] = config[key].toLowerCase() in ['true', 'on', '1'];
                        } else {
                            config[key] = Boolean(config[key]);
                        }
                        console.log(`保存配置 - 布尔开关 ${key}: 元素不存在，使用默认值=${config[key]}`);
                    }
                }
            }

            // 确保API相关配置被正确保存
            const baseUrlInput = document.getElementById('DEEPSEEK_BASE_URL');
            const modelInput = document.getElementById('MODEL');
            const apiKeyInput = document.getElementById('DEEPSEEK_API_KEY');

            if (baseUrlInput) {
                config['DEEPSEEK_BASE_URL'] = baseUrlInput.value;
            }
            if (modelInput) {
                config['MODEL'] = modelInput.value;
            }
            if (apiKeyInput) {
                config['DEEPSEEK_API_KEY'] = apiKeyInput.value;
            }

            // 确保图像识别API相关配置被正确保存
            const visionBaseUrlInput = document.getElementById('VISION_BASE_URL');
            const visionModelInput = document.getElementById('VISION_MODEL');
            const visionApiKeyInput = document.getElementById('VISION_API_KEY');
            
            if (visionBaseUrlInput) {
                config['VISION_BASE_URL'] = visionBaseUrlInput.value;
            }
            if (visionModelInput) {
                config['VISION_MODEL'] = visionModelInput.value;
            }
            if (visionApiKeyInput) {
                config['VISION_API_KEY'] = visionApiKeyInput.value;
            }

            // 直接发送简单的键值对配置，而不是嵌套结构
            console.log("发送配置数据:", config);

            // 发送保存请求
            fetch('/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(config)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    showSaveNotification(data.message);

                    // 保存成功后显示淡蓝色成功状态
                    showSaveSuccessState();

                    // 保存成功后，不刷新群聊配置，避免覆盖用户修改
                    console.log('群聊配置保存成功，保持当前界面状态');
                } else {
                    showSaveNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存配置失败:', error);
                showSaveNotification('保存配置失败: ' + error.message, 'error');
            });
        }

        function processFormValue(config, key, value) {
            // 处理列表类型
            if (key === 'LISTEN_LIST') {
                // 直接保存为字符串，在最终提交前再转换为数组
                config[key] = value;
            }
            // 处理数字类型
            else if (['TEMPERATURE', 'VISION_TEMPERATURE', 'MAX_TOKEN',
                     'OPENAI_TTS_TEMPERATURE', 'OPENAI_TTS_TOP_P', 'OPENAI_TTS_SPEED',
                     'MIN_COUNTDOWN_HOURS', 'MAX_COUNTDOWN_HOURS', 'MAX_GROUPS', 'QUEUE_TIMEOUT',
                     'MEMORY_ARCHIVE_DAYS', 'MAX_STORY_RESULTS', 'CHARACTER_RESET_INTERVAL'].includes(key)) {
                // 转换为数字类型
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    config[key] = numValue;
                    // 整数类型配置转为整数
                    if (['MAX_TOKEN', 'MAX_GROUPS', 'QUEUE_TIMEOUT', 'MEMORY_ARCHIVE_DAYS', 'MAX_STORY_RESULTS'].includes(key)) {
                        config[key] = Math.round(numValue);
                    }
                } else {
                    config[key] = value; // 如果无法转换，保留原值
                }
            }
            // 处理任务配置
            else if (key === 'TASKS') {
                try {
                    config[key] = JSON.parse(value);
                } catch (e) {
                    console.error("解析任务数据失败:", e);
                    config[key] = [];
                }
            }
            // 处理群聊配置
            else if (key === 'GROUP_CHAT_CONFIG') {
                try {
                    config[key] = JSON.parse(value);
                } catch (e) {
                    console.error("解析群聊配置失败:", e);
                    config[key] = [];
                }
            }
            // 处理布尔值
            else if (['NETWORK_SEARCH_ENABLED', 'INTELLIGENT_SEARCH_ENABLED', 'WEBLENS_ENABLED', 'VOICE_CALL_ANTI_ECHO_ENABLED', 'USE_MULTIMODAL_LLM',
                     'ENABLE_STORY_DATABASE', 'AUTO_MEMORY_SUMMARY', 'STORY_AUTO_CLASSIFY', 'STORY_QUERY_ENABLED', 'AUTO_ARCHIVE_MEMORY',
                     'ENABLE_AUTO_RECONNECT', 'EMAIL_ENABLED'].includes(key)) {
                // 对于开关滑块，检查元素是否存在
                const checkbox = document.getElementById(key);
                if (checkbox && checkbox.type === 'checkbox') {
                    // 使用元素的checked属性
                    config[key] = checkbox.checked;
                    console.log(`处理表单值 - 布尔开关 ${key}: 元素状态=${checkbox.checked}, 处理值=${config[key]}`);
                } else {
                    // 如果元素不存在或不是复选框，则尝试解析值
                    if (typeof value === 'string') {
                        config[key] = value.toLowerCase() === 'true';
                    } else {
                        config[key] = Boolean(value);
                    }
                    console.log(`处理表单值 - 布尔开关 ${key}: 元素不存在，使用默认值=${config[key]}`);
                }
            }
            else if (value.toLowerCase() === 'true' || value.toLowerCase() === 'false') {
                config[key] = value.toLowerCase() === 'true';
            }
            // 其他类型直接保存
            else {
                config[key] = value;
            }
        }

        // 添加新函数
        function addToListFromSelect(key, value) {
            if (value === 'add_new') {
                document.getElementById('new_input_' + key).style.display = 'flex';
                setTimeout(() => {
                    document.querySelector(`select[onchange*="${key}"]`).value = '';
                }, 100);
                return;
            }

            if (value) {
                const targetElement = document.getElementById(key);
                const currentValues = targetElement.value ? targetElement.value.split(',') : [];
                if (!currentValues.includes(value)) {
                    currentValues.push(value);
                    targetElement.value = currentValues.join(',');

                    // 添加到用户列表显示
                    const userListElement = document.getElementById('selected_users_' + key);
                    const userDiv = document.createElement('div');
                    userDiv.className = 'list-group-item d-flex justify-content-between align-items-center';
                    userDiv.innerHTML = `
                        ${value}
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeUser('${key}', '${value}')">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    `;
                    userListElement.appendChild(userDiv);
                }
            }
        }

        function addNewUser(key) {
            const inputElement = document.getElementById('input_' + key);
            const newValue = inputElement.value.trim();

            if (newValue) {
                const targetElement = document.getElementById(key);
                const currentValues = targetElement.value ? targetElement.value.split(',') : [];
                if (!currentValues.includes(newValue)) {
                    currentValues.push(newValue);
                    targetElement.value = currentValues.join(',');

                    // 添加到用户列表显示
                    const userListElement = document.getElementById('selected_users_' + key);
                    const userDiv = document.createElement('div');
                    userDiv.className = 'list-group-item d-flex justify-content-between align-items-center';
                    userDiv.innerHTML = `
                        ${newValue}
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeUser('${key}', '${newValue}')">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    `;
                    userListElement.appendChild(userDiv);

                    // 清空输入框
                    inputElement.value = '';
                }
            }
            updateTaskChatIdOptions();
            // 更新群聊配置的选择框
            if (key === 'LISTEN_LIST') {
                updateGroupChatConfigSelects();
            }
        }

        function removeUser(key, userToRemove) {
            const targetElement = document.getElementById(key);
            const userListElement = document.getElementById('selected_users_' + key);

            // 更新隐藏的input值
            let currentValues = targetElement.value ? targetElement.value.split(',') : [];
            currentValues = currentValues.filter(user => user !== userToRemove);
            targetElement.value = currentValues.join(',');

            // 从显示列表中移除
            const userElements = userListElement.getElementsByClassName('list-group-item');
            for (let element of userElements) {
                if (element.textContent.trim() === userToRemove) {
                    element.remove();
                    break;
                }
            }
            updateTaskChatIdOptions();
            // 更新群聊配置的选择框
            if (key === 'LISTEN_LIST') {
                updateGroupChatConfigSelects();
            }
        }

        // 添加背景图片上传处理
        document.getElementById('backgroundInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('background', file);

                fetch('/upload_background', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.body.style.backgroundImage = `url('${data.path}')`;
                        showToast(data.message, 'success');
                    } else {
                        showToast(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showToast('上传失败：' + error, 'danger');
                });
            }
        });

        // 重置背景函数
        function resetBackground() {
            fetch('/reset_background', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 移除背景图片
                    document.body.style.backgroundImage = '';
                    showToast('背景已重置', 'success');
                } else {
                    showToast(data.message, 'danger');
                }
            })
            .catch(error => {
                showToast('重置背景失败：' + error, 'danger');
            });
        }

        function showSaveNotification(message, type = 'success') {
            const notification = document.getElementById('saveNotification');
            const messageElement = document.getElementById('saveNotificationMessage');

            // 移除现有的背景色类
            notification.classList.remove('bg-success', 'bg-danger');

            // 根据类型设置样式
            if (type === 'success') {
                notification.classList.add('bg-success');
            } else {
                notification.classList.add('bg-danger');
            }

            messageElement.textContent = message;

            const toast = new bootstrap.Toast(notification, {
                animation: true,
                autohide: true,
                delay: 3000
            });
            toast.show();
        }

        // 添加设置时间间隔的辅助函数
        function setInterval(value, unit) {
            document.getElementById('intervalValue').value = value;
            document.getElementById('intervalUnit').value = unit;
            updateSchedulePreview();
        }

        // 修改更新预览的函数
        function updateSchedulePreview() {
            const scheduleType = document.getElementById('scheduleType').value;
            const preview = document.getElementById('schedulePreview');

            if (scheduleType === 'cron') {
                const hour = document.getElementById('cronHour').value;
                const minute = document.getElementById('cronMinute').value;
                const weekdays = [];

                // 获取选中的星期
                for (let i = 1; i <= 7; i++) {
                    if (document.getElementById(`cronWeekday${i}`).checked) {
                        // 将1-7转换为0-6（0代表周日）
                        weekdays.push(i === 7 ? 0 : i - 1);
                    }
                }

                if (weekdays.length === 0) {
                    preview.textContent = '请选择执行周期';
                    return;
                }

                let previewText = `每天 ${hour === '*' ? '每小时' : hour + '点'} ${minute}分`;
                if (weekdays.length < 7) {
                    previewText = `每周 ${weekdays.map(w => ['日', '一', '二', '三', '四', '五', '六'][w]).join('、')} ${hour === '*' ? '每小时' : hour + '点'} ${minute}分`;
                }

                preview.textContent = previewText;

                // 更新cron表达式 - 修改为5字段格式
                const cronExp = `${minute} ${hour} * * ${weekdays.join(',')}`;
                document.getElementById('cronExpression').value = cronExp;
            } else {
                const value = document.getElementById('intervalValue').value;
                const unit = document.getElementById('intervalUnit').value;

                if (!value) {
                    preview.textContent = '请设置间隔时间';
                    return;
                }

                let unitText = '';
                switch(unit) {
                    case '60': unitText = '分钟'; break;
                    case '3600': unitText = '小时'; break;
                    case '86400': unitText = '天'; break;
                }

                preview.textContent = `每 ${value} ${unitText}`;
            }
        }

        // 修改格式化Cron表达式的函数
        function formatCronExpression(cronExp) {
            const [minute, hour, day, month, weekday] = cronExp.split(' ');
            let result = '';

            // 处理星期
            if (weekday !== '*') {
                const weekdays = weekday.split(',').map(w => ['日', '一', '二', '三', '四', '五', '六'][parseInt(w)]);
                result += `每周${weekdays.join('、')} `;
            } else {
                result += '每天 ';
            }

            // 处理时间
            if (hour === '*') {
                result += `每小时${minute}分`;
            } else {
                result += `${hour}点${minute}分`;
            }

            return result;
        }

        // 修改切换调度类型输入框的函数
        function toggleScheduleInput() {
            const scheduleType = document.getElementById('scheduleType').value;
            const cronInput = document.getElementById('cronInputGroup');
            const intervalInput = document.getElementById('intervalInputGroup');

            if (scheduleType === 'cron') {
                cronInput.style.display = 'block';
                intervalInput.style.display = 'none';
            } else {
                cronInput.style.display = 'none';
                intervalInput.style.display = 'block';
            }

            updateSchedulePreview();
        }

        // 为所有相关输入添加change事件监听
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = [
                'cronHour', 'cronMinute',
                'cronWeekday1', 'cronWeekday2', 'cronWeekday3',
                'cronWeekday4', 'cronWeekday5', 'cronWeekday6', 'cronWeekday7',
                'intervalValue', 'intervalUnit'
            ];

            inputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', updateSchedulePreview);
                }
            });
        });

        // 修改保存任务的函数
        function saveTask() {
            const taskId = document.getElementById('taskId').value.trim();
            const chatId = document.getElementById('taskChatId').value;
            const content = document.getElementById('taskContent').value.trim();
            const scheduleType = document.getElementById('scheduleType').value;

            // 验证必填字段
            if (!taskId || !chatId || !content) {
                showSaveNotification('请填写所有必填字段', 'error');
                return;
            }

            const task = {
                task_id: taskId,
                chat_id: chatId,
                content: content,
                schedule_type: scheduleType,
                is_active: true
            };

            // 根据调度类型设置相应的值
            if (scheduleType === 'cron') {
                const cronExp = document.getElementById('cronExpression').value;
                if (!cronExp) {
                    showSaveNotification('请设置执行时间', 'error');
                    return;
                }
                // 验证cron表达式格式
                const [minute, hour, day, month, weekday] = cronExp.split(' ');
                if (!/^\d+$/.test(minute) || !/^\d+$/.test(hour) || !/^[\d,]+$/.test(weekday)) {
                    showSaveNotification('Cron表达式格式不正确', 'error');
                    return;
                }
                task.schedule_time = cronExp;
            } else {
                const value = document.getElementById('intervalValue').value;
                const unit = document.getElementById('intervalUnit').value;

                if (!value) {
                    showSaveNotification('请设置间隔时间', 'error');
                    return;
                }

                // 计算总秒数
                const totalSeconds = parseInt(value) * parseInt(unit);
                task.schedule_time = totalSeconds.toString();
                task.interval = totalSeconds.toString();
            }

            // 获取现有的任务列表
            let tasks = [];
            try {
                const tasksInput = document.getElementById('TASKS');
                if (tasksInput && tasksInput.value) {
                    tasks = JSON.parse(tasksInput.value);
                }
            } catch (e) {
                console.error('解析任务列表失败:', e);
            }

            // 更新任务列表
            const existingIndex = tasks.findIndex(t => t.task_id === taskId);
            if (existingIndex >= 0) {
                tasks[existingIndex] = task;
            } else {
                tasks.push(task);
            }

            // 更新隐藏输入框的值
            document.getElementById('TASKS').value = JSON.stringify(tasks);

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addTaskModal'));
            modal.hide();

            // 显示成功提示
            showSaveNotification('任务已添加，请点击底部的"保存所有设置"按钮保存更改');

            // 刷新任务列表
            updateTaskList();
        }

        // 修改更新任务列表的函数
        function updateTaskList() {
            const container = document.getElementById('taskListContainer');
            if (!container) return;

            // 获取任务列表
            let tasks = [];
            try {
                const tasksInput = document.getElementById('TASKS');
                if (tasksInput && tasksInput.value) {
                    tasks = JSON.parse(tasksInput.value);
                }
            } catch (e) {
                console.error('解析任务列表失败:', e);
            }

            // 更新显示
            container.innerHTML = tasks.length === 0 ? `
                <div class="text-center text-muted p-4">
                    <i class="bi bi-inbox fs-2"></i>
                    <p class="mt-2">暂无定时任务</p>
                </div>
            ` : tasks.map(task => `
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="me-auto">
                            <div class="d-flex align-items-center mb-1">
                                <span class="badge bg-primary me-2">${task.task_id}</span>
                                <span class="badge ${task.is_active ? 'bg-success' : 'bg-secondary'} me-2">
                                    ${task.is_active ? '运行中' : '已暂停'}
                                </span>
                            </div>
                            <div class="mb-1">
                                <i class="bi bi-person me-1"></i>发送给：${task.chat_id}
                            </div>
                            <div class="mb-1">
                                <i class="bi bi-clock me-1"></i>执行时间：
                                ${task.schedule_type === 'cron' ?
                                    formatCronExpression(task.schedule_time) :
                                    formatInterval(task.schedule_time)}
                            </div>
                            <div class="text-muted small">
                                <i class="bi bi-chat-text me-1"></i>${task.content}
                            </div>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="toggleTask('${task.task_id}')" title="${task.is_active ? '暂停任务' : '启动任务'}">
                                <i class="bi bi-${task.is_active ? 'pause' : 'play'}-fill"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteTask('${task.task_id}')" title="删除任务">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 添加格式化时间间隔的函数
        function formatInterval(seconds) {
            const hours = parseInt(seconds) / 3600;
            if (hours >= 24) {
                return `每${hours/24}天`;
            } else if (hours >= 1) {
                return `每${hours}小时`;
            } else {
                return `每${hours*60}分钟`;
            }
        }

        // 添加切换任务状态的函数
        function toggleTask(taskId) {
            let tasks = [];
            try {
                const tasksInput = document.getElementById('TASKS');
                if (tasksInput && tasksInput.value) {
                    tasks = JSON.parse(tasksInput.value);
                }
            } catch (e) {
                return;
            }

            const taskIndex = tasks.findIndex(t => t.task_id === taskId);
            if (taskIndex >= 0) {
                tasks[taskIndex].is_active = !tasks[taskIndex].is_active;
                document.getElementById('TASKS').value = JSON.stringify(tasks);
                updateTaskList();
                showSaveNotification(`任务已${tasks[taskIndex].is_active ? '启动' : '暂停'}，请点击底部的"保存所有设置"按钮保存更改`);
            }
        }

        // 添加更新发送对象下拉框的函数
        function updateTaskChatIdOptions() {
            const chatSelect = document.getElementById('taskChatId');
            if (!chatSelect) return;

            // 保存当前选中的值
            const currentValue = chatSelect.value;

            // 清空现有选项
            chatSelect.innerHTML = '<option value="">请选择发送对象</option>';

            // 从监听列表获取用户
            const userElements = document.querySelectorAll('#selected_users_LISTEN_LIST .list-group-item');
            userElements.forEach(element => {
                const userName = element.textContent.trim().replace('×', '').trim();
                if (userName) {
                    chatSelect.innerHTML += `<option value="${userName}">${userName}</option>`;
                }
            });

            // 恢复之前选中的值
            if (currentValue) {
                chatSelect.value = currentValue;
            }
        }

        // 修改显示添加任务模态框时的处理
        document.getElementById('addTaskModal').addEventListener('show.bs.modal', function () {
            // 重置表单
            document.getElementById('taskForm').reset();

            // 更新发送对象下拉框
            updateTaskChatIdOptions();

            // 默认显示cron输入框
            toggleScheduleInput();
        });

        // 在用户列表变化时更新发送对象下拉框
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有温度滑块
            const temperatureSliders = document.querySelectorAll('[id$="_slider"].temperature-slider');
            temperatureSliders.forEach(slider => {
                const key = slider.id.replace('_slider', '');
                updateTemperature(key, slider.value);
            });

            // 监听用户列表的变化
            const userListElement = document.getElementById('selected_users_LISTEN_LIST');
            if (userListElement) {
                const observer = new MutationObserver(function(mutations) {
                    updateTaskChatIdOptions();
                    updateGroupChatConfigSelects();
                });

                observer.observe(userListElement, {
                    childList: true,
                    subtree: true
                });
            }

            // 初始化时更新一次
            updateTaskChatIdOptions();

            // 初始化网络搜索相关组件
            // 网络搜索API地址已固定为KouriChat，不需要初始化
            // 固定使用https://api.kourichat.com/v1
            
            // 初始化网络搜索模型选择
            const networkSearchModel = document.getElementById('NETWORK_SEARCH_MODEL');
            if (networkSearchModel) {
                // 如果不是kourichat-search且不是custom-model，设置为custom-model并填充自定义输入框
                if (networkSearchModel.value !== 'kourichat-search' && networkSearchModel.value !== 'custom-model' && networkSearchModel.value !== '') {
                    const customInput = document.getElementById('custom_network_search_model');
                    if (customInput) {
                        customInput.value = networkSearchModel.value;
                        customInput.style.display = 'block';
                    }
                    // 选中自定义选项
                    const options = networkSearchModel.querySelectorAll('option');
                    options.forEach(option => {
                        if (option.value === 'custom-model') {
                            option.selected = true;
                        }
                    });
                }
                // 触发变化事件以显示/隐藏提示词输入框
                if (typeof toggleNetworkSearchPrompt === 'function') {
                    toggleNetworkSearchPrompt(networkSearchModel.value);
                }
            }

            // 初始化网页内容提取模型选择
            const weblensModel = document.getElementById('WEBLENS_MODEL');
            if (weblensModel) {
                // 如果不是kourichat-weblens且不是custom-model，设置为custom-model并填充自定义输入框
                if (weblensModel.value !== 'kourichat-weblens' && weblensModel.value !== 'custom-model' && weblensModel.value !== '') {
                    const customInput = document.getElementById('custom_weblens_model');
                    if (customInput) {
                        customInput.value = weblensModel.value;
                        customInput.style.display = 'block';
                    }
                    // 选中自定义选项
                    const options = weblensModel.querySelectorAll('option');
                    options.forEach(option => {
                        if (option.value === 'custom-model') {
                            option.selected = true;
                        }
                    });
                }
                // 触发变化事件以显示/隐藏提示词输入框
                toggleWeblensPrompt(weblensModel.value);
            }
        });

        // 添加删除任务的函数
        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？')) {
                // 获取当前任务列表
                let tasks = [];
                try {
                    const tasksInput = document.getElementById('TASKS');
                    if (tasksInput && tasksInput.value) {
                        tasks = JSON.parse(tasksInput.value);
                    }
                } catch (e) {
                    return;
                }

                // 删除指定任务
                tasks = tasks.filter(t => t.task_id !== taskId);

                // 更新隐藏输入框的值
                document.getElementById('TASKS').value = JSON.stringify(tasks);

                // 刷新任务列表显示
                updateTaskList();

                showSaveNotification('任务已删除，请点击底部的"保存所有设置"按钮保存更改');
            }
        }

        // 添加页面关闭事件监听器
        window.addEventListener('beforeunload', function(e) {
            // 获取所有配置数据
            const mainForm = document.getElementById('configForm');
            const otherForm = document.getElementById('otherConfigForm');
            const config = {};

            // 获取所有表单数据
            const formData = new FormData(mainForm);

            // 处理表单数据
            for (let [key, value] of formData.entries()) {
                processFormValue(config, key, value);
            }

            if (otherForm) {
                const otherFormData = new FormData(otherForm);
                for (let [key, value] of otherFormData.entries()) {
                    processFormValue(config, key, value);
                }
            }

            // 特别处理任务数据
            const tasksInput = document.getElementById('TASKS');
            if (tasksInput) {
                try {
                    const tasksValue = tasksInput.value;
                    if (tasksValue) {
                        config['TASKS'] = JSON.parse(tasksValue);
                    }
                } catch (e) {
                    config['TASKS'] = [];
                }
            }

            // 特别检查温度值
            const temperatureSlider = document.getElementById('TEMPERATURE_slider');
            const temperatureInput = document.getElementById('TEMPERATURE');
            if (temperatureSlider && temperatureInput) {
                const tempValue = parseFloat(temperatureSlider.value);
                if (!isNaN(tempValue)) {
                    config['TEMPERATURE'] = tempValue;
                }
            }

            // 使用 navigator.sendBeacon 发送保存请求
            // 这种方式可以在页面关闭时可靠地发送数据
            const data = new FormData();
            data.append('config', JSON.stringify(config));

            // 发送保存请求
            navigator.sendBeacon('/save', data);
        });

        // 导出配置按钮事件监听
        document.getElementById('exportConfigBtn').addEventListener('click', function() {
            // 收集所有配置数据
            const mainForm = document.getElementById('configForm');
            const otherForm = document.getElementById('otherConfigForm');
            const config = {};

            // 获取所有表单数据
            const formData = new FormData(mainForm);
            for (let [key, value] of formData.entries()) {
                processFormValue(config, key, value);
            }

            if (otherForm) {
                const otherFormData = new FormData(otherForm);
                for (let [key, value] of otherFormData.entries()) {
                    processFormValue(config, key, value);
                }
            }

            // 特别处理任务数据
            const tasksInput = document.getElementById('TASKS');
            if (tasksInput) {
                try {
                    const tasksValue = tasksInput.value;
                    if (tasksValue) {
                        config['TASKS'] = JSON.parse(tasksValue);
                    }
                } catch (e) {
                    config['TASKS'] = [];
                }
            }

            // 创建JSON文件并下载
            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const now = new Date();
            const dateStr = now.toISOString().slice(0, 10);
            const filename = `KouriChat_配置_${dateStr}.json`;

            const downloadLink = document.createElement('a');
            downloadLink.href = URL.createObjectURL(dataBlob);
            downloadLink.download = filename;

            // 模拟点击下载
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // 显示成功通知
            showSaveNotification('配置已成功导出', 'success');
        });

        // 导入配置按钮事件监听
        document.getElementById('importConfigBtn').addEventListener('click', function() {
            // 创建文件输入元素
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'application/json';
            fileInput.style.display = 'none';

            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length === 0) return;

                const file = e.target.files[0];
                const reader = new FileReader();

                reader.onload = function(event) {
                    try {
                        const config = JSON.parse(event.target.result);
                        let mainApiUrlUpdated = false;
                        let mainApiNewValue = '';

                        // 填充表单数据
                        for (const [key, value] of Object.entries(config)) {
                            if (key === 'TASKS') {
                                // 特殊处理任务数据
                                const tasksInput = document.getElementById('TASKS');
                                if (tasksInput) {
                                    tasksInput.value = JSON.stringify(value);
                                    // 刷新任务列表显示
                                    if (typeof refreshTaskList === 'function') {
                                        refreshTaskList();
                                    }
                                }
                                continue;
                            }

                            // 检查是否更新了主API地址
                            if (key === 'DEEPSEEK_BASE_URL' && value) {
                                mainApiUrlUpdated = true;
                                mainApiNewValue = value;
                            }

                            // 处理普通输入字段
                            const input = document.querySelector(`[name="${key}"]`);
                            if (input) {
                                if (input.type === 'checkbox') {
                                    input.checked = Boolean(value);
                                } else {
                                    input.value = value;
                                }

                                // 特别处理滑块
                                if (key === 'TEMPERATURE' || key === 'VISION_TEMPERATURE' ||
                                    key === 'OPENAI_TTS_TEMPERATURE' || key === 'OPENAI_TTS_TOP_P' || key === 'OPENAI_TTS_SPEED') {
                                    const slider = document.getElementById(`${key}_slider`);
                                    if (slider) {
                                        slider.value = value;
                                        // 使用统一的updateTemperature函数更新显示
                                        updateTemperature(key, value);
                                    }
                                }
                            }
                        }

                        // 不再需要同步更新网络搜索API地址，因为已固定使用KouriChat
                        // 网络搜索API地址已固定为KouriChat

                        showSaveNotification('配置已成功导入', 'success');
                    } catch (error) {
                        console.error('导入配置失败:', error);
                        showSaveNotification('导入配置失败: ' + error.message, 'danger');
                    }
                };

                reader.readAsText(file);
            });

            document.body.appendChild(fileInput);
            fileInput.click();
            document.body.removeChild(fileInput);
        });

        // 添加任务相关函数
        function loadTasks() {
            fetch('/get_tasks')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // 更新隐藏的input字段
                        document.getElementById('TASKS').value = JSON.stringify(data.tasks);

                        // 更新任务列表显示
                        updateTaskListUI(data.tasks);

                        // 更新发送对象下拉选项
                        updateTaskChatIdOptions();
                    } else {
                        showToast('加载任务失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showToast('加载任务失败: ' + error, 'error');
                });
        }

        function updateTaskListUI(tasks) {
            const container = document.getElementById('taskListContainer');

            if (tasks && tasks.length > 0) {
                // 清空现有内容
                container.innerHTML = '';

                // 添加每个任务
                tasks.forEach(task => {
                    const taskItem = document.createElement('div');
                    taskItem.className = 'list-group-item';

                    let scheduleInfo = '';
                    if (task.schedule_type === 'cron') {
                        scheduleInfo = `<span class="badge bg-primary me-1">Cron</span> ${task.schedule_time}`;
                    } else {
                        scheduleInfo = `<span class="badge bg-success me-1">间隔</span> ${task.schedule_time}`;
                    }

                    taskItem.innerHTML = `
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">${task.task_id}</h5>
                            <small>
                                ${scheduleInfo}
                                <span class="badge ${task.is_active ? 'bg-success' : 'bg-secondary'} ms-2">
                                    ${task.is_active ? '启用' : '禁用'}
                                </span>
                            </small>
                        </div>
                        <p class="mb-1">发送给: ${task.chat_id}</p>
                        <p class="mb-1">内容: ${task.content}</p>
                        <div class="d-flex justify-content-end mt-2">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="editTask('${task.task_id}')">
                                <i class="bi bi-pencil"></i> 编辑
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="confirmDeleteTask('${task.task_id}')">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    `;

                    container.appendChild(taskItem);
                });
            } else {
                // 显示无任务提示
                container.innerHTML = `
                    <div class="text-center text-muted p-4">
                        <i class="bi bi-inbox fs-2"></i>
                        <p class="mt-2">暂无定时任务</p>
                    </div>
                `;
            }
        }

        function saveTask() {
            // 获取表单数据
            const taskId = document.getElementById('taskId').value.trim();
            const chatId = document.getElementById('taskChatId').value;
            const content = document.getElementById('taskContent').value.trim();
            const scheduleType = document.getElementById('scheduleType').value;
            let scheduleTime = '';

            // 根据任务类型获取调度时间
            if (scheduleType === 'cron') {
                scheduleTime = document.getElementById('cronExpression').value.trim();
            } else {
                const intervalValue = document.getElementById('intervalValue').value.trim();
                const intervalUnit = document.getElementById('intervalUnit').value;
                scheduleTime = `${intervalValue}${intervalUnit}`;
            }

            // 验证表单
            if (!taskId || !chatId || !content || !scheduleTime) {
                showToast('请填写所有必要字段', 'error');
                return;
            }

            // 创建任务对象
            const task = {
                task_id: taskId,
                chat_id: chatId,
                content: content,
                schedule_type: scheduleType,
                schedule_time: scheduleTime,
                is_active: true
            };

            // 发送保存请求
            fetch('/save_task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(task)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 隐藏模态框
                    bootstrap.Modal.getInstance(document.getElementById('addTaskModal')).hide();

                    // 重新加载任务列表
                    loadTasks();

                    // 显示成功提示
                    showToast(data.message, 'success');

                    // 重置表单
                    document.getElementById('taskForm').reset();
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                showToast('保存任务失败: ' + error, 'error');
            });
        }

        function confirmDeleteTask(taskId) {
            if (confirm(`确定要删除任务 "${taskId}" 吗？`)) {
                deleteTask(taskId);
            }
        }

        function deleteTask(taskId) {
            fetch('/delete_task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({task_id: taskId})
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 重新加载任务列表
                    loadTasks();

                    // 显示成功提示
                    showToast(data.message, 'success');
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                showToast('删除任务失败: ' + error, 'error');
            });
        }

        function editTask(taskId) {
            // 获取当前任务数据
            const tasksValue = document.getElementById('TASKS').value;
            let tasks = [];

            try {
                tasks = JSON.parse(tasksValue);
            } catch (e) {
                showToast('解析任务数据失败', 'error');
                return;
            }

            // 查找指定任务
            const task = tasks.find(t => t.task_id === taskId);
            if (!task) {
                showToast('未找到指定任务', 'error');
                return;
            }

            // 填充表单
            document.getElementById('taskId').value = task.task_id;
            document.getElementById('taskId').readOnly = true; // 编辑模式下不允许修改ID
            document.getElementById('taskChatId').value = task.chat_id;
            document.getElementById('taskContent').value = task.content;
            document.getElementById('scheduleType').value = task.schedule_type;

            // 根据任务类型设置调度时间
            toggleScheduleInput(); // 先切换显示正确的输入框

            if (task.schedule_type === 'cron') {
                document.getElementById('cronExpression').value = task.schedule_time;
            } else {
                // 解析间隔值和单位
                const match = task.schedule_time.match(/(\d+)([smhd])/);
                if (match) {
                    document.getElementById('intervalValue').value = match[1];
                    document.getElementById('intervalUnit').value = match[2];
                }
            }

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('addTaskModal'));
            modal.show();

            // 更新模态框标题
            document.getElementById('addTaskModalLabel').innerHTML =
                '<i class="bi bi-pencil-square me-2"></i>编辑定时任务';

            // 更改保存按钮文本
            const saveButton = document.querySelector('#addTaskModal .modal-footer .btn-primary');
            saveButton.textContent = '保存修改';
        }

        // 初始化任务功能
        document.addEventListener('DOMContentLoaded', function() {
            // 加载任务列表
            loadTasks();

            // 添加任务表单提交处理
            document.getElementById('taskForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveTask();
            });

            // 添加调度类型切换处理
            document.getElementById('scheduleType').addEventListener('change', toggleScheduleInput);

            // 初始隐藏间隔输入组
            document.getElementById('intervalInputGroup').style.display = 'none';

            // 添加模态框关闭事件处理
            document.getElementById('addTaskModal').addEventListener('hidden.bs.modal', function() {
                // 重置表单
                document.getElementById('taskForm').reset();
                document.getElementById('taskId').readOnly = false;
                document.getElementById('addTaskModalLabel').innerHTML =
                    '<i class="bi bi-plus-circle me-2"></i>添加定时任务';

                // 重置保存按钮文本
                const saveButton = document.querySelector('#addTaskModal .modal-footer .btn-primary');
                saveButton.textContent = '保存';

                // 默认显示Cron输入组
                document.getElementById('cronInputGroup').style.display = 'block';
                document.getElementById('intervalInputGroup').style.display = 'none';
            });

            // 由于网络搜索功能已简化，只使用KouriChat默认模型，不再需要初始化组件


        });


    </script>

    <!-- 确保关键函数在全局作用域中可用 -->
        <script>
        // 确保更新模型选择器函数在全局可用
        document.addEventListener('DOMContentLoaded', function() {
            console.log("注册全局updateModelSelect函数");
            
            // 定义全局模型选择更新函数
            window.updateModelSelect = function(providerId) {
                console.log('全局 updateModelSelect 被调用，参数:', providerId);
                
                const modelSelect = document.getElementById('model_select');
                const modelInput = document.getElementById('MODEL');
                const customModelInput = document.getElementById('customModelInput');
                
                if (!modelSelect) {
                    console.error("模型选择器未找到!");
                    return;
                }
                
                // 保存当前模型值，确保后续操作不会丢失
                const currentModelValue = modelInput ? modelInput.value : '';
                console.log("当前模型值:", currentModelValue);
                
                // 根据提供商重置选择框内容
                modelSelect.innerHTML = '';
                
                // 添加不同API提供商的默认模型选项
                if (providerId === 'kourichat-global') {
                    console.log("设置KouriChat模型选项");
                    modelSelect.innerHTML = `
                        <option value="kourichat-v3">kourichat-v3</option>
                        <option value="gemini-2.5-pro">gemini-2.5-pro</option>
                        <option value="gemini-2.5-flash">gemini-2.5-flash-0520</option>
                        <option value="gpt-4o">gpt-4o</option>
                        <option value="grok-3">grok-3</option>
                        <option value="grok-3-fast-beta">grok3-fast</option>
                        <option value="claude-3-5-sonnet-20240620">claude-3.5-sonnet</option>
                        <option value="claude-3-7-sonnet-20250219">claude-3.7-sonnet</option>
                    `;
                } else if (providerId === 'siliconflow') {
                    console.log("设置硅基流动模型选项");
                    modelSelect.innerHTML = `
                        <option value="deepseek-ai/DeepSeek-V3">deepseek-ai/DeepSeek-V3</option>
                        <option value="deepseek-ai/DeepSeek-R1">deepseek-ai/DeepSeek-R1</option>
                        <option value="Pro/deepseek-ai/DeepSeek-V3">Pro/deepseek-ai/DeepSeek-V3</option>
                    `;
                } else if (providerId === 'deepseek') {
                    console.log("设置DeepSeek模型选项");
                    modelSelect.innerHTML = `
                        <option value="deepseek-chat">deepseek-chat</option>
                        <option value="deepseek-reasoner">deepseek-reasoner</option>
                    `;
                }
                
                // 添加自定义选项
                modelSelect.innerHTML += '<option value="custom">自定义模型</option>';
                
                // 记录所有可用选项
                const availableOptions = Array.from(modelSelect.options).map(opt => opt.value);
                console.log("可用模型选项:", availableOptions);
                
                // 处理不同情况
                if (providerId === 'ollama' || providerId === 'custom') {
                    // 1. 如果是自定义或Ollama提供商
                    console.log("处理自定义/Ollama提供商");
                    modelSelect.value = 'custom';
                    
                    if (customModelInput) {
                        customModelInput.style.display = 'block';
                        const inputField = customModelInput.querySelector('input');
                        
                        // 保留已有的值
                        if (inputField && currentModelValue) {
                            inputField.value = currentModelValue;
                            // 确保隐藏字段也有值
                            if (modelInput && !modelInput.value) {
                                modelInput.value = currentModelValue;
                            }
                        }
                    }
                } else if (currentModelValue) {
                    // 2. 有现有值的情况
                    console.log("检查当前值是否在选项中:", currentModelValue);
                    
                    // 检查当前值是否在选项列表中
                    const valueInOptions = availableOptions.includes(currentModelValue);
                    
                    if (valueInOptions) {
                        // 2.1 当前值在选项中
                        console.log("当前值在选项中，选择:", currentModelValue);
                        modelSelect.value = currentModelValue;
                        
                        // 确保自定义输入框隐藏
                        if (customModelInput) {
                            customModelInput.style.display = 'none';
                        }
                    } else {
                        // 2.2 当前值不在选项中，视为自定义模型
                        console.log("当前值不在选项中，设为自定义模型:", currentModelValue);
                        modelSelect.value = 'custom';
                        
                        // 显示并填充自定义输入框
                        if (customModelInput) {
                            customModelInput.style.display = 'block';
                            const inputField = customModelInput.querySelector('input');
                            if (inputField) {
                                inputField.value = currentModelValue;
                            }
                        }
                    }
                } else {
                    // 3. 无现有值，选择第一个选项
                    console.log("无现有值，选择第一个选项");
                    if (modelSelect.options.length > 0) {
                        modelSelect.selectedIndex = 0;
                        
                        // 更新隐藏字段的值
                        if (modelInput && modelSelect.value !== 'custom') {
                            modelInput.value = modelSelect.value;
                        }
                        
                        // 隐藏自定义输入框
                        if (customModelInput && modelSelect.value !== 'custom') {
                            customModelInput.style.display = 'none';
                        }
                    }
                }
                
                // 确保隐藏的MODEL字段有值
                if (modelInput && !modelInput.value && modelSelect.value !== 'custom') {
                    modelInput.value = modelSelect.value;
                }
                
                // 如果选择了自定义模型但没有输入值，确保输入框可见
                if (modelSelect.value === 'custom' && customModelInput) {
                    customModelInput.style.display = 'block';
                }
                
                // 触发自定义模型输入框的事件监听器重新设置
                if (typeof setupCustomModelInputListeners === 'function') {
                    setTimeout(setupCustomModelInputListeners, 100);
                }
            };
        });
    </script>

    <!-- 确保自定义模型输入框正常工作的调试脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟执行，确保所有其他脚本已加载
            setTimeout(function() {
                console.log("======== 调试模型选择器 ========");
                
                // 检查和修复模型选择器
                const modelSelect = document.getElementById('model_select');
                const customModelInput = document.getElementById('customModelInput');
                
                if (modelSelect && customModelInput) {
                                console.log("模型选择器存在");
            console.log("当前选中值:", modelSelect.value);
            console.log("当前模型输入框值:", document.getElementById('MODEL')?.value);
            console.log("当前自定义输入框显示状态:", customModelInput.style.display);
                    
                    // 强制添加事件监听器
                    modelSelect.onchange = function() {
                        console.log("强制检测到选择变更:", this.value);
                        if (this.value === 'custom') {
                            customModelInput.style.display = 'block';
                            console.log("已显示自定义输入框");
                            
                            // 聚焦输入框
                            const inputField = customModelInput.querySelector('input');
                            if (inputField) {
                                inputField.focus();
                                console.log("已聚焦输入框");
                            }
                        } else {
                            customModelInput.style.display = 'none';
                            console.log("已隐藏自定义输入框");
                        }
                    };
                    
                    // 如果当前选择的是自定义模型，确保显示输入框
                    if (modelSelect.value === 'custom') {
                        customModelInput.style.display = 'block';
                        console.log("已强制显示自定义输入框");
                    }
                    
                    // 如果当前显示的是自定义输入框但选择器不是custom，修正它
                    if (customModelInput.style.display !== 'none' && modelSelect.value !== 'custom') {
                        modelSelect.value = 'custom';
                        console.log("已修正选择器值为custom");
                    }
                }
            }, 800);
        });







        // 显示Toast通知
        function showToast(message, type = 'info') {
            // 如果已有showAlert函数，使用它
            if (typeof showAlert === 'function') {
                showAlert(type, message);
                return;
            }

            // 创建简单的通知
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' || type === 'danger' ? '#ef4444' : '#6366f1'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 显示通知消息（为剧情分类功能提供支持）
        function showNotification(message, type = 'info') {
            // 创建更显眼的通知样式
            const notification = document.createElement('div');

            // 根据类型设置不同的样式和图标
            let bgColor, icon, textColor = 'white';
            switch(type) {
                case 'success':
                    bgColor = '#10b981';
                    icon = '✅';
                    break;
                case 'error':
                    bgColor = '#ef4444';
                    icon = '❌';
                    break;
                case 'warning':
                    bgColor = '#f59e0b';
                    icon = '⚠️';
                    break;
                default:
                    bgColor = '#6366f1';
                    icon = 'ℹ️';
            }

            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: ${bgColor};
                color: ${textColor};
                padding: 16px 24px;
                border-radius: 12px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
                z-index: 10001;
                font-size: 15px;
                font-weight: 600;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                max-width: 350px;
                word-wrap: break-word;
                border: 2px solid rgba(255, 255, 255, 0.2);
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-size: 18px;">${icon}</span>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏（成功消息显示更久）
            const hideDelay = type === 'success' ? 4000 : 3000;
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            }, hideDelay);
        }

        // 处理按钮动作
        function handleButtonAction(action, buttonId) {
            if (action === 'classify_story') {
                classifyStoryContent(buttonId);
            } else if (action === 'create_database_structure') {
                createDatabaseStructure(buttonId);
            }
        }

        // 剧情分类处理函数
        function classifyStoryContent(buttonId) {
            const button = document.getElementById(buttonId);
            const statusDiv = document.getElementById(buttonId + '_status');

            // 获取当前选择的角色
            const avatarDirInput = document.getElementById('AVATAR_DIR');
            const currentAvatarDir = avatarDirInput ? avatarDirInput.value : '';
            const currentAvatarName = currentAvatarDir ? currentAvatarDir.split('/').pop() : '';

            if (!currentAvatarName) {
                showNotification('无法获取当前角色信息，请检查角色设置', 'error');
                return;
            }

            // 显示加载状态
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>分类中...';
            statusDiv.style.display = 'block';

            // 发送分类请求
            fetch('/api/classify_story', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    avatar_name: currentAvatarName
                })
            })
            .then(response => response.json())
            .then(data => {
                // 隐藏加载状态
                statusDiv.style.display = 'none';

                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-magic me-2"></i>立即分类剧情内容';

                // 显示结果
                if (data.status === 'success') {
                    showNotification(data.message, 'success');
                } else if (data.status === 'warning') {
                    showNotification(data.message, 'warning');
                } else {
                    showNotification(data.message || '分类失败', 'error');
                }
            })
            .catch(error => {
                console.error('剧情分类请求失败:', error);

                // 隐藏加载状态
                statusDiv.style.display = 'none';

                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-magic me-2"></i>立即分类剧情内容';

                // 显示错误
                showNotification('网络请求失败，请检查网络连接', 'error');
            });
        }

        // 创建数据库结构处理函数
        function createDatabaseStructure(buttonId) {
            const button = document.getElementById(buttonId);
            const statusDiv = document.getElementById(buttonId + '_status');

            // 获取当前选择的角色
            const avatarDirInput = document.getElementById('AVATAR_DIR');
            const currentAvatarDir = avatarDirInput ? avatarDirInput.value : '';
            const currentAvatarName = currentAvatarDir ? currentAvatarDir.split('/').pop() : '';

            if (!currentAvatarName) {
                showNotification('无法获取当前角色信息，请检查角色设置', 'error');
                return;
            }

            // 显示加载状态
            button.disabled = true;
            button.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>创建中...';
            statusDiv.style.display = 'block';

            // 发送创建请求
            fetch('/api/create_database_structure', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    avatar_name: currentAvatarName
                })
            })
            .then(response => response.json())
            .then(data => {
                // 隐藏加载状态
                statusDiv.style.display = 'none';

                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-folder-plus me-2"></i>一键创建数据库格式';

                // 显示结果
                if (data.status === 'success') {
                    showNotification(data.message, 'success');
                } else if (data.status === 'info') {
                    showNotification(data.message, 'info');
                } else if (data.status === 'warning') {
                    showNotification(data.message, 'warning');
                } else {
                    showNotification(data.message || '创建失败', 'error');
                }
            })
            .catch(error => {
                console.error('创建数据库结构请求失败:', error);

                // 隐藏加载状态
                statusDiv.style.display = 'none';

                // 恢复按钮状态
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-folder-plus me-2"></i>一键创建数据库格式';

                // 显示错误信息
                showNotification('创建数据库结构失败，请检查网络连接', 'error');
            });
        }

        // 特殊修复函数：处理三个无法持久化的控件
        function fixThreePersistentControls() {
            console.log('开始修复三个无法持久化的控件...');
            
            // 直接从配置文件读取这三个控件的值
            fetch('/get_all_configs')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const configs = data.configs;
                        const memoryConfig = configs['记忆和剧情配置'] || {};
                        
                        // 修复 CHARACTER_RESET_INTERVAL 滑块
                        const characterResetInterval = document.getElementById('CHARACTER_RESET_INTERVAL');
                        if (characterResetInterval && memoryConfig.CHARACTER_RESET_INTERVAL !== undefined) {
                            const value = memoryConfig.CHARACTER_RESET_INTERVAL;
                            console.log(`修复 CHARACTER_RESET_INTERVAL: ${value}`);
                            characterResetInterval.value = value;
                            
                            // 同时更新滑块和显示
                            const slider = document.getElementById('CHARACTER_RESET_INTERVAL_slider');
                            const display = document.getElementById('CHARACTER_RESET_INTERVAL_display');
                            if (slider) {
                                slider.value = value;
                            }
                            if (display) {
                                display.textContent = value;
                            }
                        }
                        
                        // 修复 CHARACTER_RESET_ENABLED 开关
                        const characterResetEnabled = document.getElementById('CHARACTER_RESET_ENABLED');
                        if (characterResetEnabled && memoryConfig.CHARACTER_RESET_ENABLED !== undefined) {
                            const value = memoryConfig.CHARACTER_RESET_ENABLED;
                            console.log(`修复 CHARACTER_RESET_ENABLED: ${value}`);
                            characterResetEnabled.checked = value;
                            
                            // 更新标签
                            const label = document.getElementById('CHARACTER_RESET_ENABLED_label');
                            if (label) {
                                label.textContent = value ? '启用' : '停用';
                            }
                        }
                        
                        // 修复 STORY_CLASSIFY_BUTTON 按钮文本
                        const storyClassifyButton = document.getElementById('STORY_CLASSIFY_BUTTON');
                        if (storyClassifyButton && memoryConfig.STORY_CLASSIFY_BUTTON !== undefined) {
                            const value = memoryConfig.STORY_CLASSIFY_BUTTON;
                            console.log(`修复 STORY_CLASSIFY_BUTTON: ${value}`);
                            // 更新按钮文本
                            storyClassifyButton.innerHTML = `<i class="bi bi-magic me-2"></i>${value}`;
                        }
                        
                        // 修复 CREATE_DATABASE_STRUCTURE_BUTTON 按钮文本
                        const createDatabaseButton = document.getElementById('CREATE_DATABASE_STRUCTURE_BUTTON');
                        if (createDatabaseButton && memoryConfig.CREATE_DATABASE_STRUCTURE_BUTTON !== undefined) {
                            const value = memoryConfig.CREATE_DATABASE_STRUCTURE_BUTTON;
                            console.log(`修复 CREATE_DATABASE_STRUCTURE_BUTTON: ${value}`);
                            // 更新按钮文本
                            createDatabaseButton.innerHTML = `<i class="bi bi-folder-plus me-2"></i>${value}`;
                        }
                        
                        console.log('三个控件修复完成！');
                    } else {
                        console.warn('获取配置数据失败，无法修复控件');
                    }
                })
                .catch(error => {
                    console.error('修复控件时出错:', error);
                });
        }


    </script>
</body>
</html>


import os
import json

def check_config_detailed():
    """详细检查配置文件"""
    
    # 检查可能的配置文件路径
    config_paths = [
        "src/config/config.json",
        "config/config.json", 
        "src/config.json",
        "config.json"
    ]
    
    print("=== 搜索配置文件 ===")
    for path in config_paths:
        print(f"检查路径: {path}")
        print(f"文件存在: {os.path.exists(path)}")
        if os.path.exists(path):
            print(f"文件大小: {os.path.getsize(path)} 字节")
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"JSON格式正确")
                print(f"配置内容: {json.dumps(config, ensure_ascii=False, indent=2)}")
            except Exception as e:
                print(f"读取配置文件失败: {e}")
        print()
    
    # 检查是否有其他配置文件
    print("=== 搜索所有JSON文件 ===")
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.json') and 'config' in file.lower():
                full_path = os.path.join(root, file)
                print(f"发现配置文件: {full_path}")

if __name__ == "__main__":
    check_config_detailed()
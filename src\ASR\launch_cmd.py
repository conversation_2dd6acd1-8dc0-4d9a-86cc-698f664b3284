import os
import subprocess
import sys

"""launch_cmd.py
在新的控制台窗口中启动 src/ASR/main.py。
"""


def launch():
    main_py = os.path.abspath(os.path.join(os.path.dirname(__file__), "main.py"))
    if not os.path.exists(main_py):
        print(f"[VolcASR] 找不到 main.py: {main_py}")
        return

    if sys.platform.startswith("win"):
        CREATE_NEW_CONSOLE = 0x00000010
        subprocess.Popen([sys.executable, main_py], creationflags=CREATE_NEW_CONSOLE)
    else:
        subprocess.Popen([sys.executable, main_py])


if __name__ == "__main__":
    launch() 
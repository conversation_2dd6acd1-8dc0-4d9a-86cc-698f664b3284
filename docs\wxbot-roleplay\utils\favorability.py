#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
好感度管理模块
参考My-Dream-Moments-WeChat-wxauto项目的好感度系统
"""

import re
import random
from datetime import datetime
from database import Session, User
from utils.logger import log


class FavorabilityManager:
    """好感度管理类"""

    @staticmethod
    def get_favorability(user_id: str) -> float:
        """获取用户好感度"""
        session = Session()
        try:
            user = session.query(User).filter_by(user_id=user_id).first()
            if not user:
                # 创建新用户，初始好感度为10.0
                user = User(user_id=user_id, favorability=10.0)
                session.add(user)
                session.commit()
                log(f"创建新用户: {user_id}, 初始好感度: 10.0")
                return 10.0
            return user.favorability
        except Exception as e:
            log(f"获取好感度失败: {str(e)}", "ERROR")
            return 10.0
        finally:
            session.close()

    @staticmethod
    def update_favorability(user_id: str, favor_change: float) -> float:
        """更新用户好感度"""
        session = Session()
        try:
            user = session.query(User).filter_by(user_id=user_id).first()
            if not user:
                user = User(user_id=user_id, favorability=10.0)
                session.add(user)

            # 检查当前好感度是否已达上限
            if user.favorability == 100 and favor_change > 0:
                log(f"用户 {user_id} 好感度已达上限")
                return 100

            # 根据当前好感度调整变化幅度（优化后的算法）
            base_delta = favor_change
            current_favor = user.favorability

            # 计算调整系数
            adjusted_delta = FavorabilityManager._calculate_adjusted_delta(current_favor, base_delta)

            # 计算新的好感度值
            old_favorability = user.favorability
            new_value = user.favorability + adjusted_delta

            # 限制好感度范围在 -100 到 100 之间
            user.favorability = max(-100, min(100, new_value))
            user.last_interaction = datetime.now()
            user.interaction_count += 1

            session.commit()

            log(f"用户 {user_id} 好感度更新: {old_favorability:.1f} -> {user.favorability:.1f} (变化: {adjusted_delta:.1f})")
            return user.favorability

        except Exception as e:
            log(f"更新好感度失败: {str(e)}", "ERROR")
            session.rollback()
            return 10.0
        finally:
            session.close()

    @staticmethod
    def _calculate_adjusted_delta(current_favor: float, base_delta: float) -> float:
        """
        计算调整后的好感度变化值

        优化策略：
        1. 负值区域：变化较小，不容易恢复到正值
        2. 低好感度区域(0-30)：变化较大，快速建立关系
        3. 中等好感度区域(30-70)：变化适中，稳定发展
        4. 高好感度区域(70-90)：变化较小，需要更多努力
        5. 极高好感度区域(90-100)：变化很小，珍贵的最后阶段

        Args:
            current_favor: 当前好感度值
            base_delta: 基础变化值

        Returns:
            调整后的变化值
        """
        # 对于大幅度变化（绝对值>5），减少调整幅度但不完全取消
        if abs(base_delta) > 5:
            # 大变化时也要考虑当前好感度，但调整幅度较小
            if current_favor >= 80:
                multiplier = 0.3  # 高好感度时大变化也要减弱
            elif current_favor >= 50:
                multiplier = 0.5  # 中等好感度时适度减弱
            else:
                multiplier = 0.7  # 低好感度时减弱较少
            return base_delta * multiplier

        # 小幅度变化的精细调整
        if current_favor <= -100:
            # 最低好感度：无法再降低，负面行为影响极小
            if base_delta < 0:
                multiplier = 0.0  # 不再降低
            else:
                multiplier = 0.2  # 极难提升
        elif current_favor < -50:
            # 极度负面：小幅增益，难以恢复
            multiplier = 0.5
        elif current_favor < 0:
            # 负面区域：较小增益，不易恢复
            multiplier = 0.8 + (current_favor / -100)  # 0.8-1.2之间
        elif current_favor < 20:
            # 初期阶段：较小变化，建立关系需要时间
            multiplier = 0.8 + (20 - current_favor) / 50  # 0.8-1.2之间
        elif current_favor < 40:
            # 发展阶段：缓慢变化
            multiplier = 0.6 + (40 - current_favor) / 67  # 0.6-0.9之间
        elif current_favor < 70:
            # 稳定阶段：慢速变化
            multiplier = 0.4 + (70 - current_favor) / 100  # 0.4-0.7之间
        elif current_favor < 85:
            # 高好感度：变化很小，需要持续努力
            multiplier = 0.2 + (85 - current_favor) / 75  # 0.2-0.4之间
        elif current_favor < 95:
            # 很高好感度：变化极小
            multiplier = 0.1 + (95 - current_favor) / 100  # 0.1-0.2之间
        else:
            # 极高好感度：变化极微，极难提升
            multiplier = 0.05 + (100 - current_favor) / 100  # 0.05-0.1之间

        # 对负变化给予额外惩罚（负面行为影响更大）
        if base_delta < 0:
            if current_favor > 70:
                # 高好感度时负面影响更大
                multiplier *= 1.3
            elif current_favor > 40:
                # 中等好感度时负面影响适中
                multiplier *= 1.1

        # 添加随机波动，使变化更自然（±5%）
        random_factor = 1.0 + (random.random() - 0.5) * 0.1

        return base_delta * multiplier * random_factor

    @staticmethod
    def get_favorability_info(user_id: str) -> dict:
        """获取用户完整好感度信息"""
        session = Session()
        try:
            user = session.query(User).filter_by(user_id=user_id).first()
            if not user:
                return {
                    "favorability": 10.0,
                    "level": "❄️ 萍水相逢",
                    "interaction_count": 0,
                    "last_interaction": None,
                    "created_at": None
                }

            return {
                "favorability": user.favorability,
                "level": user.get_favorability_level(),
                "interaction_count": user.interaction_count,
                "last_interaction": user.last_interaction,
                "created_at": user.created_at
            }
        except Exception as e:
            log(f"获取好感度信息失败: {str(e)}", "ERROR")
            return {
                "favorability": 10.0,
                "level": "❄️ 萍水相逢",
                "interaction_count": 0,
                "last_interaction": None,
                "created_at": None
            }
        finally:
            session.close()

    @staticmethod
    def parse_favorability_change(reply: str) -> float:
        """从AI回复中解析好感度变化"""
        favor_change = 0.0

        # 定义匹配模式
        favor_patterns = [
            # 原有格式： [+好感X.X] 或 [好感+X.X]
            re.compile(r'[\[【][+-]?好感\s*([+-]?\d+\.?\d*)[\]】]'),
            re.compile(r'[\[【]好感\s*([+-]?\d+\.?\d*)[\]】]'),
            # 新增格式： 好感度±X
            re.compile(r'好感度\s*([+-]?\d+\.?\d*)\b'),
            # 支持中文描述格式： 亲密度增加了X
            re.compile(r'(?:亲密度|好感)(?:度)?\s*(?:增加|提升了?|上涨|下降|减少)\s*([+-]?\d+\.?\d*)'),
            # 支持直接数字格式： +1.5 或 -0.5
            re.compile(r'[\[【]([+-]?\d+\.?\d*)[\]】]')
        ]

        for pattern in favor_patterns:
            for match in pattern.finditer(reply):
                try:
                    # 提取数值部分
                    groups = match.groups()
                    value_str = groups[-1]  # 总取最后一个group为数值
                    # 清洗数值
                    value_str = re.sub(r'[^0-9+-.]', '', value_str)
                    if value_str:
                        change = float(value_str)
                        favor_change += change
                        log(f"解析到好感度变化: {change}")
                except Exception as e:
                    log(f"好感度解析失败: {str(e)}", "DEBUG")
                    continue

        return favor_change

    @staticmethod
    def get_favorability_prompt(user_id: str) -> str:
        """获取好感度提示词"""
        favorability = FavorabilityManager.get_favorability(user_id)

        # 创建临时对象来获取等级描述
        temp_user = type('TempUser', (), {'favorability': favorability})()
        level = User.get_favorability_level(temp_user)

        favor_prompt = f"""[好感度系统]
你对当前用户的好感度：{favorability:.1f}/100
等级：{level}"""

        return favor_prompt

    @staticmethod
    def get_all_users_favorability() -> list:
        """获取所有用户的好感度信息"""
        session = Session()
        try:
            users = session.query(User).order_by(User.favorability.desc()).all()
            result = []
            for user in users:
                result.append({
                    "user_id": user.user_id,
                    "favorability": user.favorability,
                    "level": user.get_favorability_level(),
                    "interaction_count": user.interaction_count,
                    "last_interaction": user.last_interaction
                })
            return result
        except Exception as e:
            log(f"获取所有用户好感度失败: {str(e)}", "ERROR")
            return []
        finally:
            session.close()

    @staticmethod
    def reset_favorability(user_id: str, new_value: float = 10.0) -> bool:
        """重置用户好感度"""
        session = Session()
        try:
            user = session.query(User).filter_by(user_id=user_id).first()
            if not user:
                user = User(user_id=user_id, favorability=new_value)
                session.add(user)
            else:
                old_value = user.favorability
                user.favorability = max(-100, min(100, new_value))
                log(f"重置用户 {user_id} 好感度: {old_value:.1f} -> {user.favorability:.1f}")

            session.commit()
            return True
        except Exception as e:
            log(f"重置好感度失败: {str(e)}", "ERROR")
            session.rollback()
            return False
        finally:
            session.close()

    @staticmethod
    def is_favorability_query(content: str) -> bool:
        """判断是否为好感度查询请求"""
        query_patterns = [
            "查询好感度", "查询亲密度", "好感度查询",
            "好感度查看", "查看好感度", "我的好感度",
            "亲密度查看", "查看亲密度", "我的亲密度"
        ]
        return any(pattern in content for pattern in query_patterns)

    @staticmethod
    def get_favorability_query_reply(user_id: str) -> str:
        """生成好感度查询回复"""
        try:
            info = FavorabilityManager.get_favorability_info(user_id)

            # 生成随机提示语
            tips = [
                "要继续和我做好朋友哦~",
                "我们的亲密度还有提升空间呢",
                "一起创造更多美好回忆吧！",
                "每天的交流都在加深我们的羁绊呢",
                "悄悄告诉你：多找我聊天好感度会涨更快哦",
                "你在我心中很特别呢~",
                "希望我们的友谊能够长长久久",
                "感谢你一直陪伴着我"
            ]

            # 根据好感度等级选择不同的提示语
            if info["favorability"] >= 80:
                tips.extend([
                    "你已经是我最重要的朋友了！",
                    "我们的羁绊已经很深了呢~",
                    "和你在一起的时光总是那么美好"
                ])
            elif info["favorability"] >= 60:
                tips.extend([
                    "我们的关系越来越好了呢",
                    "你是个很棒的朋友！",
                    "希望能和你分享更多快乐时光"
                ])
            elif info["favorability"] < 20:
                tips = [
                    "让我们慢慢了解彼此吧",
                    "相信我们会成为好朋友的",
                    "每一次交流都是新的开始"
                ]

            # 构造回复消息
            reply = f"""【{user_id}的好感度】
当前等级：{info['level']}
具体数值：{info['favorability']:.1f}/100
交互次数：{info['interaction_count']}次
{random.choice(tips)}"""

            return reply

        except Exception as e:
            log(f"生成好感度查询回复失败: {str(e)}", "ERROR")
            return "好感度查询失败，请稍后再试"

    @staticmethod
    def get_favorability_stats() -> dict:
        """获取好感度系统统计信息"""
        session = Session()
        try:
            total_users = session.query(User).count()
            high_favor_users = session.query(User).filter(User.favorability >= 80).count()
            medium_favor_users = session.query(User).filter(
                User.favorability >= 40, User.favorability < 80
            ).count()
            low_favor_users = session.query(User).filter(User.favorability < 40).count()

            avg_favorability = session.query(User).with_entities(
                session.query(User.favorability).subquery().c.favorability
            ).scalar() or 0

            return {
                "total_users": total_users,
                "high_favor_users": high_favor_users,
                "medium_favor_users": medium_favor_users,
                "low_favor_users": low_favor_users,
                "average_favorability": avg_favorability
            }
        except Exception as e:
            log(f"获取好感度统计失败: {str(e)}", "ERROR")
            return {
                "total_users": 0,
                "high_favor_users": 0,
                "medium_favor_users": 0,
                "low_favor_users": 0,
                "average_favorability": 0
            }
        finally:
            session.close()
#!/usr/bin/env python3
"""
微信重连配置向导
帮助用户快速配置微信重连功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.wechat_reconnect.email_sender import get_smtp_config


class WeChatReconnectConfigWizard:
    """微信重连配置向导"""
    
    def __init__(self):
        self.config_path = project_root / "src" / "config" / "config.json"
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            sys.exit(1)
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            print("配置已保存")
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_input(self, prompt, default=None, required=True):
        """获取用户输入"""
        if default:
            prompt += f" (默认: {default})"
        prompt += ": "
        
        while True:
            value = input(prompt).strip()
            
            if value:
                return value
            elif default:
                return default
            elif not required:
                return ""
            else:
                print("此项为必填项，请输入有效值")
    
    def get_bool_input(self, prompt, default=True):
        """获取布尔值输入"""
        default_str = "Y/n" if default else "y/N"
        prompt += f" ({default_str}): "
        
        while True:
            value = input(prompt).strip().lower()
            
            if value in ['y', 'yes', '是', '1']:
                return True
            elif value in ['n', 'no', '否', '0']:
                return False
            elif value == "":
                return default
            else:
                print("请输入 y/yes/是 或 n/no/否")
    
    def get_number_input(self, prompt, default=None, min_val=None, max_val=None):
        """获取数字输入"""
        if default:
            prompt += f" (默认: {default})"
        prompt += ": "
        
        while True:
            value = input(prompt).strip()
            
            if value == "" and default is not None:
                return default
            
            try:
                num = int(value)
                if min_val is not None and num < min_val:
                    print(f"值不能小于 {min_val}")
                    continue
                if max_val is not None and num > max_val:
                    print(f"值不能大于 {max_val}")
                    continue
                return num
            except ValueError:
                print("请输入有效的数字")
    
    def configure_basic_settings(self):
        """配置基本设置"""
        print("\n=== 基本设置 ===")
        
        reconnect_config = self.config["categories"]["wechat_reconnect"]["settings"]
        
        # 启用自动重连
        enable = self.get_bool_input(
            "启用微信自动重连功能",
            reconnect_config["enable_auto_reconnect"]["value"]
        )
        reconnect_config["enable_auto_reconnect"]["value"] = enable
        
        if not enable:
            print("自动重连功能已禁用，跳过其他配置")
            return
        
        # 检查间隔
        interval = self.get_number_input(
            "微信状态检查间隔（秒）",
            reconnect_config["check_interval"]["value"],
            min_val=10,
            max_val=600
        )
        reconnect_config["check_interval"]["value"] = interval
        
        # 最大重试次数
        max_retry = self.get_number_input(
            "最大重连尝试次数",
            reconnect_config["max_retry_attempts"]["value"],
            min_val=1,
            max_val=10
        )
        reconnect_config["max_retry_attempts"]["value"] = max_retry
        
        # 二维码重试间隔
        qr_interval = self.get_number_input(
            "二维码重新发送间隔（秒）",
            reconnect_config["qrcode_retry_interval"]["value"],
            min_val=60,
            max_val=3600
        )
        reconnect_config["qrcode_retry_interval"]["value"] = qr_interval
    
    def configure_email_settings(self):
        """配置邮件设置"""
        print("\n=== 邮件设置 ===")
        
        reconnect_config = self.config["categories"]["wechat_reconnect"]["settings"]
        
        # 启用邮件功能
        enable_email = self.get_bool_input(
            "启用邮件发送二维码功能",
            reconnect_config["email_enabled"]["value"]
        )
        reconnect_config["email_enabled"]["value"] = enable_email
        
        if not enable_email:
            print("邮件功能已禁用")
            return
        
        print("\n邮件配置说明：")
        print("- QQ邮箱：需要开启SMTP服务，使用授权码而非密码")
        print("- Gmail：需要开启两步验证，使用应用专用密码")
        print("- 163/126邮箱：需要开启SMTP服务")
        print()
        
        # 发送方邮箱
        sender_email = self.get_input(
            "发送方邮箱地址",
            reconnect_config["sender_email"]["value"]
        )
        reconnect_config["sender_email"]["value"] = sender_email
        
        # 自动配置SMTP
        smtp_server, smtp_port = get_smtp_config(sender_email)
        print(f"自动检测SMTP配置: {smtp_server}:{smtp_port}")
        
        # 询问是否使用自动配置
        use_auto = self.get_bool_input("使用自动检测的SMTP配置", True)
        
        if use_auto:
            reconnect_config["smtp_server"]["value"] = smtp_server
            reconnect_config["smtp_port"]["value"] = smtp_port
        else:
            # 手动配置SMTP
            smtp_server = self.get_input(
                "SMTP服务器地址",
                reconnect_config["smtp_server"]["value"]
            )
            reconnect_config["smtp_server"]["value"] = smtp_server
            
            smtp_port = self.get_number_input(
                "SMTP端口",
                reconnect_config["smtp_port"]["value"],
                min_val=1,
                max_val=65535
            )
            reconnect_config["smtp_port"]["value"] = smtp_port
        
        # 发送方密码/授权码
        print("\n注意：")
        print("- QQ邮箱请使用授权码，不是登录密码")
        print("- Gmail请使用应用专用密码")
        print("- 其他邮箱请根据服务商要求配置")
        
        sender_password = self.get_input(
            "发送方邮箱密码/授权码",
            "***" if reconnect_config["sender_password"]["value"] else None
        )
        if sender_password != "***":
            reconnect_config["sender_password"]["value"] = sender_password
        
        # 接收方邮箱
        recipient_email = self.get_input(
            "接收方邮箱地址（接收二维码的邮箱）",
            reconnect_config["recipient_email"]["value"]
        )
        reconnect_config["recipient_email"]["value"] = recipient_email
    
    def test_configuration(self):
        """测试配置"""
        print("\n=== 测试配置 ===")
        
        test_email = self.get_bool_input("是否测试邮件配置", True)
        
        if test_email:
            print("正在测试邮件配置...")
            
            # 保存配置
            if not self.save_config():
                print("保存配置失败，无法测试")
                return
            
            # 测试邮件
            try:
                from src.wechat_reconnect.standalone_monitor import StandaloneWeChatMonitor
                monitor = StandaloneWeChatMonitor()
                success = monitor.test_email()
                
                if success:
                    print("✅ 邮件配置测试成功！")
                else:
                    print("❌ 邮件配置测试失败，请检查配置")
                    
            except Exception as e:
                print(f"❌ 测试邮件配置时发生错误: {e}")
    
    def run_wizard(self):
        """运行配置向导"""
        print("=" * 50)
        print("        微信重连功能配置向导")
        print("=" * 50)
        print()
        print("此向导将帮助您配置微信自动重连功能")
        print("包括状态检查、自动重连、二维码邮件发送等功能")
        print()
        
        try:
            # 配置基本设置
            self.configure_basic_settings()
            
            # 配置邮件设置
            self.configure_email_settings()
            
            # 保存配置
            print("\n=== 保存配置 ===")
            if self.save_config():
                print("✅ 配置已保存")
                
                # 测试配置
                self.test_configuration()
                
                print("\n=== 配置完成 ===")
                print("您现在可以使用以下命令启动微信重连监控：")
                print("  Windows: start_wechat_monitor.bat")
                print("  Linux/Mac: ./start_wechat_monitor.sh")
                print()
                print("或者直接运行：")
                print("  python src/wechat_reconnect/standalone_monitor.py")
                
            else:
                print("❌ 保存配置失败")
                
        except KeyboardInterrupt:
            print("\n\n配置向导已取消")
        except Exception as e:
            print(f"\n配置向导发生错误: {e}")


def main():
    """主函数"""
    wizard = WeChatReconnectConfigWizard()
    wizard.run_wizard()


if __name__ == "__main__":
    main()

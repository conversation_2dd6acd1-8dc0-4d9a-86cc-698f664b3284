{"categories": {"user_settings": {"title": "用户设置", "settings": {"listen_list": {"value": ["0"], "type": "array", "description": "要监听的用户列表（请使用微信昵称，不要使用备注名）"}, "group_chat_config": {"value": [], "type": "array", "description": "群聊配置列表（为不同群聊配置专用人设和触发词）"}}}, "llm_settings": {"title": "大语言模型配置", "settings": {"api_key": {"value": "THIS_IS_MYGO"}, "base_url": {"value": "Https://avemujica.site/v1"}, "model": {"value": "Gemini2.5-pro"}, "max_tokens": {"value": 3000, "type": "number", "description": "回复最大token数量"}, "temperature": {"value": 0.2, "type": "number", "description": "AI回复的温度值", "min": 0.0, "max": 1.7}}}, "media_settings": {"title": "媒体设置", "settings": {"image_recognition": {"use_multimodal_llm": {"value": true, "type": "boolean", "description": "使用多模态LLM进行图像识别（新识图）"}, "api_key": {"value": "THIS_IS_MYGO", "type": "string", "description": "图像识别API密钥", "is_secret": true}, "base_url": {"value": "https://avemujica.site/v1", "type": "string", "description": "图像识别API基础URL"}, "temperature": {"value": 0.6, "type": "number", "description": "图像识别温度参数", "min": 0, "max": 1}, "model": {"value": "gemini2.5-pro", "type": "string", "description": "图像识别 AI 模型"}}, "image_generation": {"model": {"value": "deepseek-ai/Janus-Pro-7B", "type": "string", "description": "图像生成模型"}, "temp_dir": {"value": "data/images/temp", "type": "string", "description": "临时图片存储目录"}}, "text_to_speech": {"tts_api_key": {"value": "", "type": "string", "description": "TTS服务API"}, "voice_dir": {"value": "data/voices", "type": "string", "description": "语音文件存储目录"}}, "openai_tts": {"api_key": {"value": "01d14a08659c43e59670485325688248", "type": "string", "description": "OpenAI兼容TTS API密钥", "is_secret": true}, "base_url": {"value": "https://avemujica.site/v1", "type": "string", "description": "OpenAI兼容TTS API基础URL"}, "model": {"value": "s1", "type": "string", "description": "TTS模型名称或ID"}, "voice": {"value": "aebaa2305aa2452fbdc8f41eec852a79", "type": "string", "description": "语音音色名称或ID"}, "temperature": {"value": 1, "type": "number", "description": "TTS 温度参数", "min": 0, "max": 1, "step": 0.1}, "top_p": {"value": 0.8, "type": "number", "description": "TTS Top-P参数", "min": 0, "max": 1, "step": 0.1}, "speed": {"value": 1, "type": "number", "description": "TTS 语速", "min": 0.5, "max": 2.0, "step": 0.1}}, "voice_call": {"anti_echo_enabled": {"value": true, "type": "boolean", "description": "启用防回音功能（使用双虚拟音频设备）"}, "asr_device_keyword": {"value": "Voicemeeter Out B1", "type": "string", "description": "ASR音频输入设备关键词（用于语音识别）"}, "tts_device_keyword": {"value": "CABLE Input", "type": "string", "description": "TTS音频输出设备关键词（用于语音播放）"}, "asr_timeout_seconds": {"value": 5.0, "type": "number", "description": "ASR发送给LLM的等待时间（秒）", "min": 1.0, "max": 10.0}}, "fish_api": {"api_key": {"value": "01d14a08659c43e59670485325688248"}, "model_id": {"value": "aebaa2305aa2452fbdc8f41eec852a79"}, "temperature": {"value": 1.0}, "top_p": {"value": 1.0}, "speed": {"value": 1.5}}}}, "behavior_settings": {"title": "行为设置", "settings": {"auto_message": {"content": {"value": "请你模拟系统设置的角色，根据之前的聊天内容在微信上找对方发消息想知道对方在做什么，并跟对方报备自己在做什么、什么心情，语气自然，与之前的不要重复", "type": "string", "description": "自动消息内容"}, "countdown": {"min_hours": {"value": 1, "type": "number", "description": "最小倒计时时间（小时）"}, "max_hours": {"value": 3, "type": "number", "description": "最大倒计时时间（小时）"}}}, "message_queue": {"timeout": {"value": 8, "type": "number", "description": "消息队列等待时间（秒）", "min": 0, "max": 20}}, "quiet_time": {"start": {"value": "23:00", "type": "string", "description": "安静时间开始"}, "end": {"value": "09:00", "type": "string", "description": "安静时间结束"}}, "context": {"max_groups": {"value": 15, "type": "number", "description": "最大上下文轮数"}, "avatar_dir": {"value": "data/avatars/lin", "type": "string", "description": "人设目录（自动包含 avatar.md 和 emojis 目录）"}, "character_reset": {"enabled": {"value": true, "type": "boolean", "description": "启用定期重置人设功能"}, "interval": {"value": 5, "type": "number", "description": "人设重置间隔（对话轮数）", "min": 1, "max": 20}}, "brackets_filtering": {"enabled": {"value": false, "type": "boolean", "description": "启用括号内容过滤功能"}, "filter_round": {"value": true, "type": "boolean", "description": "过滤圆括号 () 内容"}, "filter_square": {"value": true, "type": "boolean", "description": "过滤方括号 [] 内容"}, "filter_curly": {"value": true, "type": "boolean", "description": "过滤花括号 {} 内容"}, "filter_mode": {"value": "remove", "type": "string", "description": "过滤方式（直接删除括号内容）"}}}, "brackets_filtering": {"enabled": {"value": true}, "filter_round": {"value": true}, "filter_square": {"value": false}, "filter_curly": {"value": false}}}}, "auth_settings": {"title": "认证设置", "settings": {"admin_password": {"value": "6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b", "type": "string", "description": "管理员密码", "is_secret": true}}}, "schedule_settings": {"title": "定时任务配置", "settings": {"tasks": {"value": [], "type": "array", "description": "定时任务列表"}}}, "network_search_settings": {"title": "网络搜索设置", "settings": {"search_enabled": {"value": false, "type": "boolean"}, "weblens_enabled": {"value": true, "type": "boolean"}, "api_key": {"value": "sk-3CS66EEGVx90pERIxLF2MfE7mqBHDIzeauuZxdhHj9SGfMwG"}, "base_url": {"value": "https://api.kourichat.com/v1"}, "intelligent_search_enabled": {"value": true, "type": "boolean"}}}, "weather_settings": {"settings": {"default_location": {"value": "深圳龙华"}}}, "memory_and_story_settings": {"title": "记忆和剧情", "settings": {"memory_system_type": {"value": "database", "type": "string", "description": "记忆系统类型", "options": ["old", "database"], "option_labels": {"old": "原版记忆系统（JSON文件）", "database": "数据库记忆系统（SQLite）"}}, "enable_story_database": {"value": true, "type": "boolean", "description": "启用剧情数据库功能（独立于记忆系统，可单独启用）"}, "auto_memory_summary": {"value": true, "type": "boolean", "description": "启用自动记忆总结（每10轮对话自动更新核心记忆）"}, "use_improved_memory_prompt": {"value": true, "type": "boolean", "description": "使用改进的记忆提示词（防止瞎编和角色关系混淆）"}, "memory_archive_days": {"value": 7, "type": "number", "description": "记忆归档天数（短期记忆保留天数）", "min": 1, "max": 30}, "story_auto_classify": {"value": true, "type": "boolean", "description": "启用剧情自动分类（AI自动分类剧情内容）"}, "story_query_enabled": {"value": true, "type": "boolean", "description": "启用剧情智能查询（对话时自动查询相关剧情）"}, "max_story_results": {"value": 7, "type": "number", "description": "最大剧情查询结果数", "min": 1, "max": 20}, "auto_archive_memory": {"value": true}}}, "wechat_reconnect": {"title": "微信重连配置", "settings": {"enable_auto_reconnect": {"value": false, "type": "boolean", "description": "启用微信自动重连功能"}, "check_interval": {"value": 10, "type": "number", "description": "微信状态检查间隔（秒）"}, "max_retry_attempts": {"value": 3, "type": "number", "description": "最大重连尝试次数"}, "qrcode_retry_interval": {"value": 300, "type": "number", "description": "二维码重新发送间隔（秒）"}, "email_enabled": {"value": false, "type": "boolean", "description": "启用邮件发送二维码功能"}, "smtp_server": {"value": "smtp.qq.com", "type": "string", "description": "SMTP服务器地址"}, "smtp_port": {"value": 587, "type": "number", "description": "SMTP端口"}, "sender_email": {"value": "<EMAIL>", "type": "string", "description": "发送方邮箱地址"}, "sender_password": {"value": "qguegffznsdddcgg", "type": "string", "description": "发送方邮箱密码/授权码", "is_secret": true}, "recipient_email": {"value": "<EMAIL>", "type": "string", "description": "接收方邮箱地址"}}}, "memory_recall_settings": {"title": "智能记忆回忆", "settings": {"api_key": {"value": "sk-3CS66EEGVx90pERIxLF2MfE7mqBHDIzeauuZxdhHj9SGfMwG", "type": "string", "description": "记忆回忆专用API密钥（留空则不启用智能回忆功能）", "is_secret": true}, "base_url": {"value": "https://api.kourichat.com/v1", "type": "string", "description": "记忆回忆API地址"}, "model": {"value": "qwen-turbo-latest", "type": "string", "description": "记忆回忆模型名称"}, "max_tokens": {"value": "1001", "type": "number", "description": "记忆回忆最大token数", "min": 100, "max": 4000}, "temperature": {"value": "0.1", "type": "number", "description": "记忆回忆温度参数", "min": 0.0, "max": 2.0}, "memory_format": {"value": "summary", "type": "string", "description": "记忆格式类型", "options": ["summary", "perspective"], "option_labels": {"summary": "摘要格式（=== 相关记忆 ===）", "perspective": "角色视角（=== 我对用户的了解 ===）"}}}}}}
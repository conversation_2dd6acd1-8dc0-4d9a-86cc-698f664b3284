#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签到定时任务管理器
处理签到提醒、统计和踢人的定时任务
"""

import threading
import time
import schedule
from datetime import datetime
from typing import Optional
from utils.logger import log
from utils.checkin_manager import CheckinManager


class CheckinScheduler:
    """签到定时任务管理器"""
    
    def __init__(self, checkin_manager: CheckinManager, wx_instance=None):
        """
        初始化定时任务管理器
        
        Args:
            checkin_manager: 签到管理器实例
            wx_instance: 微信实例
        """
        self.checkin_manager = checkin_manager
        self.wx = wx_instance
        self.scheduler_thread = None
        self.is_running = False
        
    def set_wx_instance(self, wx_instance):
        """设置微信实例"""
        self.wx = wx_instance
        
    def start_scheduler(self):
        """启动定时任务"""
        if self.is_running:
            log("签到定时任务已在运行中")
            return
            
        try:
            # 清除之前的任务
            schedule.clear()
            
            # 设置定时任务
            self.setup_daily_tasks()
            
            # 启动调度器线程
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            log("签到定时任务已启动")
            
        except Exception as e:
            log(f"启动签到定时任务失败: {str(e)}", "ERROR")
            self.is_running = False
    
    def stop_scheduler(self):
        """停止定时任务"""
        try:
            self.is_running = False
            schedule.clear()
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            log("签到定时任务已停止")
            
        except Exception as e:
            log(f"停止签到定时任务失败: {str(e)}", "ERROR")
    
    def setup_daily_tasks(self):
        """设置每日定时任务"""
        try:
            log("开始设置签到定时任务")

            # 从配置文件读取时间设置
            schedule_config = self.get_schedule_config()

            reminder_time = schedule_config.get('reminder_time', '10:00')
            stats_time = schedule_config.get('stats_time', '23:00')
            kick_time = schedule_config.get('kick_time', '23:30')
            cleanup_time = schedule_config.get('cleanup_time', '23:50')

            log(f"使用配置时间: 提醒={reminder_time}, 统计={stats_time}, 踢人={kick_time}, 清理={cleanup_time}")

            # 设置签到提醒任务
            job1 = schedule.every().day.at(reminder_time).do(self.send_daily_reminder)
            log(f"设置签到提醒任务: {reminder_time} - 下次执行 {job1.next_run}")

            # 设置签到统计任务
            job2 = schedule.every().day.at(stats_time).do(self.send_daily_stats)
            log(f"设置签到统计任务: {stats_time} - 下次执行 {job2.next_run}")

            # 设置踢人任务
            job3 = schedule.every().day.at(kick_time).do(self.kick_absent_users)
            log(f"设置踢人任务: {kick_time} - 下次执行 {job3.next_run}")

            # 设置数据清理任务
            job4 = schedule.every().day.at(cleanup_time).do(self.cleanup_invalid_records)
            log(f"设置清理任务: {cleanup_time} - 下次执行 {job4.next_run}")

            total_jobs = len(schedule.jobs)
            log(f"签到定时任务设置完成，共设置 {total_jobs} 个任务")

        except Exception as e:
            log(f"设置签到定时任务失败: {str(e)}", "ERROR")

    def get_schedule_config(self):
        """获取定时任务配置"""
        try:
            import config

            # 优先使用全局定时任务配置
            global_schedule = getattr(config, 'GLOBAL_CHECKIN_SCHEDULE', {})
            if global_schedule:
                log(f"使用全局定时任务配置: {global_schedule}")
                return global_schedule

            # 如果没有全局配置，使用默认配置
            default_config = getattr(config, 'DEFAULT_CHECKIN_CONFIG', {})
            log(f"使用默认签到配置: {default_config}")
            return default_config

        except Exception as e:
            log(f"获取定时任务配置失败: {str(e)}", "ERROR")
            # 返回硬编码的默认值
            return {
                'reminder_time': '10:00',
                'stats_time': '23:00',
                'kick_time': '23:30',
                'cleanup_time': '23:50'
            }
    
    def _run_scheduler(self):
        """运行调度器"""
        log("签到定时任务调度器开始运行")
        while self.is_running:
            try:
                # 检查是否有待执行的任务
                pending_jobs = schedule.jobs
                if pending_jobs:
                    current_time = datetime.now().strftime('%H:%M')
                    # 每小时记录一次状态（避免日志过多）
                    if datetime.now().minute == 0:
                        log(f"定时任务运行中，当前时间: {current_time}，待执行任务数: {len(pending_jobs)}")

                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                log(f"定时任务执行异常: {str(e)}", "ERROR")
                time.sleep(60)

        log("签到定时任务调度器已停止")

    def reload_schedule_config(self):
        """重新加载定时任务配置"""
        try:
            log("开始重新加载定时任务配置")

            if not self.is_running:
                log("定时任务调度器未运行，无法重新加载配置", "WARNING")
                return False

            # 清除现有任务
            schedule.clear()
            log("已清除现有定时任务")

            # 重新设置任务
            self.setup_daily_tasks()

            log("定时任务配置重新加载完成")
            return True

        except Exception as e:
            log(f"重新加载定时任务配置失败: {str(e)}", "ERROR")
            return False
    
    def send_daily_reminder(self):
        """发送每日签到提醒"""
        try:
            log("开始执行每日签到提醒任务")

            if not self.wx:
                log("微信实例未设置，无法发送签到提醒", "ERROR")
                return

            enabled_groups = self.checkin_manager.get_enabled_groups()

            if not enabled_groups:
                log("没有启用签到功能的群聊")
                return

            sent_count = 0

            for group_name in enabled_groups:
                try:
                    message = self.generate_reminder_message()

                    # @所有人发送提醒
                    if hasattr(self.checkin_manager, 'group_manager') and self.checkin_manager.group_manager:
                        success = self.checkin_manager.group_manager.at_all_members(group_name, message)
                        if success:
                            sent_count += 1
                            log(f"已向群聊 {group_name} 发送签到提醒")
                        else:
                            log(f"向群聊 {group_name} 发送签到提醒失败", "ERROR")
                    else:
                        log("群聊管理器未设置，无法发送提醒", "ERROR")

                except Exception as e:
                    log(f"向群聊 {group_name} 发送签到提醒异常: {str(e)}", "ERROR")

            log(f"每日签到提醒任务完成，成功发送到 {sent_count} 个群聊")

        except Exception as e:
            log(f"发送每日签到提醒失败: {str(e)}", "ERROR")
    
    def send_daily_stats(self):
        """发送每日签到统计"""
        try:
            log("开始执行每日签到统计任务")

            if not self.wx:
                log("微信实例未设置，无法发送签到统计", "ERROR")
                return

            enabled_groups = self.checkin_manager.get_enabled_groups()

            if not enabled_groups:
                log("没有启用签到功能的群聊")
                return

            sent_count = 0

            for group_name in enabled_groups:
                try:
                    stats = self.checkin_manager.get_daily_checkin_stats(group_name)
                    if stats:
                        message = self.generate_stats_message(stats)

                        # 发送统计消息到群聊
                        self.wx.ChatWith(group_name)
                        chat = self.wx.GetSubWindow(group_name)
                        if chat:
                            chat.SendMsg(message)
                            sent_count += 1
                            log(f"已向群聊 {group_name} 发送签到统计")
                        else:
                            log(f"无法获取群聊 {group_name} 子窗口", "ERROR")
                    else:
                        log(f"无法获取群聊 {group_name} 的签到统计数据", "WARNING")

                except Exception as e:
                    log(f"向群聊 {group_name} 发送签到统计异常: {str(e)}", "ERROR")

            log(f"每日签到统计任务完成，成功发送到 {sent_count} 个群聊")

        except Exception as e:
            log(f"发送每日签到统计失败: {str(e)}", "ERROR")
    
    def kick_absent_users(self):
        """踢出连续缺勤的用户"""
        try:
            log("开始执行踢出缺勤用户任务")

            enabled_groups = self.checkin_manager.get_enabled_groups()

            if not enabled_groups:
                log("没有启用签到功能的群聊")
                return

            total_kicked = 0

            for group_name in enabled_groups:
                try:
                    # 获取群聊配置
                    config = self.checkin_manager.get_group_checkin_config(group_name)
                    if not config:
                        log(f"无法获取群聊 {group_name} 的配置", "WARNING")
                        continue

                    max_absent_days = config.get('max_absent_days', 3)
                    kicked_users = self.checkin_manager.kick_absent_users(group_name, max_absent_days)

                    if kicked_users:
                        total_kicked += len(kicked_users)

                        # 发送踢人通知
                        message = self.generate_kick_message(kicked_users, max_absent_days)

                        self.wx.ChatWith(group_name)
                        chat = self.wx.GetSubWindow(group_name)
                        if chat:
                            chat.SendMsg(message)
                            log(f"已向群聊 {group_name} 发送踢人通知，踢出 {len(kicked_users)} 人")
                        else:
                            log(f"无法获取群聊 {group_name} 子窗口，踢人通知发送失败", "ERROR")
                    else:
                        log(f"群聊 {group_name} 无需踢人")

                except Exception as e:
                    log(f"群聊 {group_name} 踢人操作异常: {str(e)}", "ERROR")

            log(f"踢出缺勤用户任务完成，总共踢出 {total_kicked} 人")

        except Exception as e:
            log(f"踢出缺勤用户失败: {str(e)}", "ERROR")
    
    def check_custom_tasks(self):
        """检查自定义时间任务（暂时禁用，使用固定时间任务）"""
        try:
            # 这个方法暂时不执行任何操作
            # 所有任务都通过固定时间的 schedule 任务执行
            pass

        except Exception as e:
            log(f"检查自定义任务失败: {str(e)}", "ERROR")

    def cleanup_invalid_records(self):
        """清理无效的签到记录"""
        try:
            log("开始执行签到记录清理任务")

            # 执行清理
            cleanup_stats = self.checkin_manager.cleanup_invalid_checkin_records()

            if not cleanup_stats:
                log("签到记录清理完成，无数据需要处理")
                return

            # 生成清理报告
            cleanup_message = self.checkin_manager.get_cleanup_stats_message(cleanup_stats)

            # 发送清理报告到所有启用签到的群聊
            enabled_groups = self.checkin_manager.get_enabled_groups()

            for group_name in enabled_groups:
                try:
                    # 只有清理了记录的群聊才发送通知
                    if cleanup_stats.get(group_name, 0) > 0:
                        self.wx.ChatWith(group_name)
                        chat = self.wx.GetSubWindow(group_name)
                        if chat:
                            chat.SendMsg(cleanup_message)
                            log(f"已向群聊 {group_name} 发送清理报告")
                        else:
                            log(f"无法获取群聊 {group_name} 子窗口", "ERROR")

                except Exception as e:
                    log(f"向群聊 {group_name} 发送清理报告异常: {str(e)}", "ERROR")

            # 记录清理统计
            total_cleaned = sum(count for count in cleanup_stats.values() if count > 0)
            if total_cleaned > 0:
                log(f"签到记录清理任务完成，总共清理了 {total_cleaned} 条无效记录")
            else:
                log("签到记录清理任务完成，无需清理任何记录")

        except Exception as e:
            log(f"清理签到记录任务失败: {str(e)}", "ERROR")
    
    def send_group_reminder(self, group_name: str):
        """向指定群聊发送签到提醒"""
        try:
            message = self.generate_reminder_message()
            
            if hasattr(self.checkin_manager, 'group_manager') and self.checkin_manager.group_manager:
                success = self.checkin_manager.group_manager.at_all_members(group_name, message)
                if success:
                    log(f"已向群聊 {group_name} 发送自定义时间签到提醒")
                    
        except Exception as e:
            log(f"向群聊 {group_name} 发送自定义签到提醒失败: {str(e)}", "ERROR")
    
    def send_group_stats(self, group_name: str):
        """向指定群聊发送签到统计"""
        try:
            stats = self.checkin_manager.get_daily_checkin_stats(group_name)
            if stats:
                message = self.generate_stats_message(stats)
                
                self.wx.ChatWith(group_name)
                chat = self.wx.GetSubWindow(group_name)
                if chat:
                    chat.SendMsg(message)
                    log(f"已向群聊 {group_name} 发送自定义时间签到统计")
                    
        except Exception as e:
            log(f"向群聊 {group_name} 发送自定义签到统计失败: {str(e)}", "ERROR")
    
    def kick_group_absent_users(self, group_name: str):
        """踢出指定群聊的缺勤用户"""
        try:
            config = self.checkin_manager.get_group_checkin_config(group_name)
            if not config:
                return
            
            max_absent_days = config.get('max_absent_days', 3)
            kicked_users = self.checkin_manager.kick_absent_users(group_name, max_absent_days)
            
            if kicked_users:
                message = self.generate_kick_message(kicked_users, max_absent_days)
                
                self.wx.ChatWith(group_name)
                chat = self.wx.GetSubWindow(group_name)
                if chat:
                    chat.SendMsg(message)
                    log(f"已向群聊 {group_name} 发送自定义时间踢人通知")
                    
        except Exception as e:
            log(f"群聊 {group_name} 自定义时间踢人操作失败: {str(e)}", "ERROR")
    
    def generate_reminder_message(self) -> str:
        """生成签到提醒消息"""
        return """📢 签到提醒 📢

🌅 新的一天开始了！请大家及时签到打卡！

💡 签到方式：@我 + "签到" 即可完成签到
⏰ 签到时间：全天24小时
⚠️ 注意：每天只能签到一次，连续3天未签到将被移出群聊

让我们一起坚持打卡，共同进步！💪"""
    
    def generate_stats_message(self, stats: dict) -> str:
        """生成签到统计消息"""
        try:
            date = stats.get('date', '')
            total_members = stats.get('total_members', 0)
            checkin_count = stats.get('checkin_count', 0)
            not_checkin_count = stats.get('not_checkin_count', 0)
            checkin_rate = stats.get('checkin_rate', 0)
            
            message = f"""📊 {date} 签到统计报告 📊

👥 群聊总人数：{total_members}
✅ 已签到人数：{checkin_count}
❌ 未签到人数：{not_checkin_count}
📈 签到率：{checkin_rate:.1f}%

"""
            
            # 添加未签到用户提醒
            not_checkin_users = stats.get('not_checkin_users', [])
            if not_checkin_users and len(not_checkin_users) <= 10:  # 只显示前10个
                message += "⚠️ 未签到用户：\n"
                for user in not_checkin_users[:10]:
                    message += f"• {user}\n"
                
                if len(not_checkin_users) > 10:
                    message += f"... 还有 {len(not_checkin_users) - 10} 人未签到\n"
            
            message += "\n💡 提醒：连续3天未签到将被移出群聊，请及时签到！"
            
            return message
            
        except Exception as e:
            log(f"生成签到统计消息失败: {str(e)}", "ERROR")
            return "签到统计生成失败"
    
    def generate_kick_message(self, kicked_users: list, max_absent_days: int) -> str:
        """生成踢人通知消息"""
        try:
            if not kicked_users:
                return ""
            
            message = f"""🚫 群聊管理通知 🚫

由于连续{max_absent_days}天未签到，以下用户已被移出群聊：

"""
            
            for user in kicked_users:
                message += f"• {user}\n"
            
            message += f"""
📋 群规提醒：
• 每天需要@机器人签到一次
• 连续{max_absent_days}天未签到将被自动移出群聊
• 如有特殊情况请提前说明

请大家按时签到，维护良好的群聊秩序！"""
            
            return message
            
        except Exception as e:
            log(f"生成踢人通知消息失败: {str(e)}", "ERROR")
            return "踢人通知生成失败"
    
    def manual_reminder(self, group_name: str) -> bool:
        """手动发送签到提醒"""
        try:
            if not self.checkin_manager.is_checkin_enabled(group_name):
                return False
            
            message = self.generate_reminder_message()
            
            if hasattr(self.checkin_manager, 'group_manager') and self.checkin_manager.group_manager:
                success = self.checkin_manager.group_manager.at_all_members(group_name, message)
                if success:
                    log(f"手动向群聊 {group_name} 发送签到提醒成功")
                    return True
            
            return False
            
        except Exception as e:
            log(f"手动发送签到提醒失败: {str(e)}", "ERROR")
            return False
    
    def manual_stats(self, group_name: str) -> bool:
        """手动发送签到统计"""
        try:
            if not self.checkin_manager.is_checkin_enabled(group_name):
                return False
            
            stats = self.checkin_manager.get_daily_checkin_stats(group_name)
            if not stats:
                return False
            
            message = self.generate_stats_message(stats)
            
            self.wx.ChatWith(group_name)
            chat = self.wx.GetSubWindow(group_name)
            if chat:
                chat.SendMsg(message)
                log(f"手动向群聊 {group_name} 发送签到统计成功")
                return True
            
            return False
            
        except Exception as e:
            log(f"手动发送签到统计失败: {str(e)}", "ERROR")
            return False

import os
import logging
from typing import List, Dict, Optional
from openai import OpenAI
from src.config import config

logger = logging.getLogger(__name__)

class MemoryRecallService:
    """
    智能记忆回忆服务
    负责从历史对话中检索相关记忆并格式化返回
    """
    
    def __init__(self):
        self.config = config.memory_recall
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            if self.is_enabled():
                self.client = OpenAI(
                    api_key=self.config.api_key,
                    base_url=self.config.base_url
                )
                logger.info(f"记忆回忆服务已初始化 - 模型: {self.config.model}")
            else:
                logger.info("记忆回忆服务未启用（API密钥或模型未配置）")
        except Exception as e:
            logger.error(f"初始化记忆回忆服务失败: {e}")
            self.client = None
    
    def is_enabled(self) -> bool:
        """检查记忆回忆功能是否启用"""
        return bool(self.config.api_key and self.config.model)
    
    def recall_memories(self, current_message: str, history_conversations: List[Dict], core_memory: str) -> str:
        """
        回忆相关记忆
        
        Args:
            current_message: 当前用户消息
            history_conversations: 历史对话记录
            core_memory: 核心记忆内容
            
        Returns:
            str: 格式化的记忆内容，失败时返回空字符串
        """
        if not self.is_enabled() or not self.client:
            logger.debug("记忆回忆服务未启用，跳过记忆检索")
            return ""
        
        try:
            # 选择提示词模板
            prompt_template = self._load_prompt_template()
            if not prompt_template:
                logger.error("无法加载记忆回忆提示词模板")
                return ""
            
            # 格式化历史对话
            formatted_history = self._format_history_conversations(history_conversations)
            
            # 构建完整提示词
            prompt = prompt_template.format(
                current_message=current_message,
                history_conversations=formatted_history,
                core_memory=core_memory or "无核心记忆"
            )
            
            # 调用LLM进行记忆回忆
            response = self.client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": prompt}
                ],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            
            recalled_memory = response.choices[0].message.content.strip()
            
            # 验证返回内容
            if self._is_valid_memory_response(recalled_memory):
                logger.debug(f"成功回忆记忆，长度: {len(recalled_memory)}")
                return recalled_memory
            else:
                logger.debug("回忆结果为空或无效")
                return ""
                
        except Exception as e:
            logger.error(f"记忆回忆失败: {e}")
            return ""
    
    def _load_prompt_template(self) -> str:
        """加载提示词模板"""
        try:
            # 根据配置选择模板文件
            if self.config.memory_format == "perspective":
                template_file = "data/base/memory_recall_perspective.md"
            else:
                template_file = "data/base/memory_recall_summary.md"
            
            if os.path.exists(template_file):
                with open(template_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                logger.error(f"提示词模板文件不存在: {template_file}")
                return ""
        except Exception as e:
            logger.error(f"加载提示词模板失败: {e}")
            return ""
    
    def _format_history_conversations(self, conversations: List[Dict]) -> str:
        """格式化历史对话记录"""
        if not conversations:
            return "无历史对话记录"
        
        formatted_lines = []
        for conv in conversations[-20:]:  # 只取最近20条对话
            if isinstance(conv, dict):
                user_msg = conv.get('user', '')
                bot_msg = conv.get('bot', '')
                if user_msg and bot_msg:
                    formatted_lines.append(f"用户: {user_msg}")
                    formatted_lines.append(f"助手: {bot_msg}")
                    formatted_lines.append("")  # 空行分隔
        
        return "\n".join(formatted_lines) if formatted_lines else "无有效历史对话"
    
    def _is_valid_memory_response(self, response: str) -> bool:
        """验证记忆回忆响应是否有效"""
        if not response or len(response.strip()) < 10:
            return False
        
        # 检查是否包含记忆标记
        if self.config.memory_format == "perspective":
            return "=== 我对用户的了解 ===" in response and "=== 了解结束 ===" in response
        else:
            return "=== 相关记忆 ===" in response and "=== 记忆结束 ===" in response
    
    def get_service_status(self) -> Dict:
        """获取服务状态信息"""
        return {
            "enabled": self.is_enabled(),
            "model": self.config.model if self.is_enabled() else "未配置",
            "format": self.config.memory_format,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature
        }

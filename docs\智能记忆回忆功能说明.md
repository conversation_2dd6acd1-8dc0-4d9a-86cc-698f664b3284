# 智能记忆回忆功能说明

## 功能概述

智能记忆回忆功能让AI能够主动回忆和使用历史对话中的相关信息，而不是被动等待用户提到关键词。这大大提升了对话的连续性和个性化体验。

## 核心特性

### 1. 主动记忆回忆
- AI在每次对话前会主动分析当前消息
- 自动检索相关的历史对话记录
- 将相关记忆自然地融入回复中

### 2. 智能上下文管理
- 避免短期记忆与LLM内置上下文的重复
- 只在程序重启时使用短期记忆恢复上下文
- 运行时依靠智能记忆回忆提供历史信息

### 3. 多种记忆格式
- **摘要格式**: 保持原始对话格式
- **角色视角**: 以AI第一人称描述对用户的了解

### 4. 错误处理机制
- 记忆回忆失败时自动降级到普通对话
- 不会影响正常的对话功能

## 配置说明

### 在配置中心设置

1. 打开配置中心
2. 找到"智能记忆回忆"部分
3. 配置以下参数：

#### 必需配置
- **API密钥**: 记忆回忆专用的API密钥（留空则不启用功能）
- **API地址**: 记忆回忆服务的API地址
- **模型名称**: 用于记忆回忆的AI模型

#### 可选配置
- **最大Token数**: 记忆回忆的最大token限制（默认1000）
- **温度参数**: 控制回忆的创造性（默认0.3，较低保证准确性）
- **记忆格式**: 选择摘要格式或角色视角格式

### 配置示例

```json
{
  "memory_recall_settings": {
    "settings": {
      "api_key": {"value": "your-api-key"},
      "base_url": {"value": "https://api.example.com/v1"},
      "model": {"value": "gpt-4"},
      "max_tokens": {"value": 1000},
      "temperature": {"value": 0.3},
      "memory_format": {"value": "summary"}
    }
  }
}
```

## 记忆格式说明

### 摘要格式 (summary)
保持原始对话的格式，适合需要精确回忆对话内容的场景：

```
=== 相关记忆 ===
用户消息: 昨天下雨了
我回复: 是的，昨天确实下了一场大雨
用户消息: 我喜欢晴天
我回复: 晴天确实让人心情愉悦
=== 记忆结束 ===
```

### 角色视角 (perspective)
以AI的第一人称视角描述对用户的了解，更自然：

```
=== 我对用户的了解 ===
我记得用户昨天提到下雨了，当时我们聊了天气话题。
我了解到用户比较喜欢晴天，不太喜欢下雨天。
我们之前还聊过用户的心情会受天气影响。
=== 了解结束 ===
```

## 工作原理

### 1. 记忆检索流程
```
用户发送消息 → 分析消息内容 → 检索相关历史记忆 → 格式化记忆 → 整合到系统提示词 → 生成回复
```

### 2. 上下文优化
- **程序运行时**: 使用LLM内置的15轮上下文 + 智能回忆的相关记忆
- **程序重启时**: 使用短期记忆恢复上下文，然后切换到运行时模式
- **避免重复**: 智能回忆不会与当前上下文重叠

### 3. 错误处理
```
记忆回忆成功 → 整合记忆 + 正常回复
记忆回忆失败 → 直接使用主LLM回复（无记忆）
```

## 使用效果

### 启用前
- AI只能记住当前对话的15轮内容
- 无法主动联想历史信息
- 对话缺乏连续性

### 启用后
- AI能主动回忆相关的历史对话
- 自然提及之前讨论过的话题
- 体现出对用户的深度了解
- 对话更有个性化和连续性

## 示例对话

### 用户: "今天天气真好"

**启用前的回复:**
"是的，今天确实是个好天气呢！"

**启用后的回复:**
"是的，今天确实是个好天气！我记得你之前说过喜欢晴天，昨天下雨的时候你还有点不开心呢。这样的好天气应该让你心情不错吧？"

## 注意事项

1. **性能影响**: 每次对话会额外调用一次记忆回忆API
2. **Token消耗**: 会增加一定的token使用量
3. **准确性**: 依赖于记忆回忆模型的质量
4. **隐私**: 历史对话会被发送给记忆回忆服务

## 故障排除

### 功能未生效
1. 检查API密钥和模型是否正确配置
2. 查看日志中是否有错误信息
3. 确认记忆回忆服务是否可访问

### 回忆内容不准确
1. 调整温度参数（降低温度提高准确性）
2. 检查历史记忆数据是否正确
3. 考虑更换更强的记忆回忆模型

### 性能问题
1. 减少max_tokens参数
2. 优化记忆回忆的调用频率
3. 考虑使用更快的模型

## 技术实现

- **记忆回忆服务**: `modules/memory/memory_recall_service.py`
- **记忆管理器**: `modules/memory_manager.py`
- **配置管理**: `src/config/__init__.py`
- **提示词模板**: `data/base/memory_recall_*.md`

这个功能让AI从"健忘症患者"变成了"有完整记忆的朋友"，能够在对话中自然地体现出对历史交互的记忆和理解。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
签到管理模块
实现群聊签到功能，包括签到记录、自动提醒、统计和踢人功能
"""

from sqlalchemy import func
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from utils.logger import log
from database import CheckinRecord, GroupCheckinConfig, KickRecord


class CheckinManager:
    """签到管理器"""

    def __init__(self, session: Session):
        """
        初始化签到管理器

        Args:
            session: 数据库会话
        """
        self.session = session
        self.group_manager = None
        
    def set_group_manager(self, group_manager):
        """设置群聊管理器"""
        self.group_manager = group_manager

    def auto_enable_groups_from_config(self):
        """根据配置文件自动启用群聊签到功能"""
        try:
            import config

            if not getattr(config, 'ENABLE_CHECKIN_SYSTEM', True):
                log("签到系统已在配置中禁用")
                return

            auto_enable_groups = getattr(config, 'AUTO_ENABLE_CHECKIN_GROUPS', [])
            default_config = getattr(config, 'DEFAULT_CHECKIN_CONFIG', {})

            if not auto_enable_groups:
                log("配置中没有设置自动启用签到的群聊")
                return

            enabled_count = 0

            for group_config in auto_enable_groups:
                try:
                    if isinstance(group_config, str):
                        # 简单的群聊名称，使用默认配置
                        group_name = group_config
                        config_params = default_config.copy()
                    elif isinstance(group_config, dict):
                        # 详细配置
                        group_name = group_config.get('group_name')
                        if not group_name:
                            log("群聊配置中缺少 group_name", "ERROR")
                            continue

                        config_params = default_config.copy()
                        config_params.update({
                            k: v for k, v in group_config.items()
                            if k != 'group_name'
                        })
                    else:
                        log(f"无效的群聊配置格式: {group_config}", "ERROR")
                        continue

                    # 检查群聊是否已经启用
                    if self.is_checkin_enabled(group_name):
                        log(f"群聊 {group_name} 签到功能已启用，跳过")
                        continue

                    # 启用签到功能
                    success = self.enable_group_checkin(group_name, **config_params)
                    if success:
                        enabled_count += 1
                        log(f"自动启用群聊 {group_name} 签到功能成功")
                    else:
                        log(f"自动启用群聊 {group_name} 签到功能失败", "ERROR")

                except Exception as e:
                    log(f"处理群聊配置异常: {str(e)}", "ERROR")

            if enabled_count > 0:
                log(f"自动启用了 {enabled_count} 个群聊的签到功能")
            else:
                log("没有新的群聊需要启用签到功能")

        except Exception as e:
            log(f"自动启用群聊签到功能异常: {str(e)}", "ERROR")
        

    
    def is_group_admin(self, group_name: str) -> bool:
        """
        检查机器人是否为群管理员
        
        Args:
            group_name: 群聊名称
            
        Returns:
            bool: 是否为管理员
        """
        try:
            # 这里需要实际检查机器人在群聊中的权限
            # 暂时返回 True，实际使用时需要根据 wxautox API 实现
            # 可以通过获取群聊信息或尝试执行管理员操作来判断
            return True
        except Exception as e:
            log(f"检查群管理员权限失败: {str(e)}", "ERROR")
            return False
    
    def enable_group_checkin(self, group_name: str, **kwargs) -> bool:
        """
        启用群聊签到功能

        Args:
            group_name: 群聊名称
            **kwargs: 配置参数
                - reminder_time: 提醒时间，默认 '10:00'
                - stats_time: 统计时间，默认 '23:00'
                - kick_time: 踢人时间，默认 '23:30'
                - max_absent_days: 最大缺勤天数，默认 3

        Returns:
            bool: 是否启用成功
        """
        try:
            # 检查是否为群管理员
            if not self.is_group_admin(group_name):
                log(f"机器人不是群聊 {group_name} 的管理员，无法启用签到功能", "WARNING")
                return False

            config = self.session.query(GroupCheckinConfig).filter_by(group_name=group_name).first()
            if config:
                config.is_enabled = True
                config.reminder_time = kwargs.get('reminder_time', '10:00')
                config.stats_time = kwargs.get('stats_time', '23:00')
                config.kick_time = kwargs.get('kick_time', '23:30')
                config.max_absent_days = kwargs.get('max_absent_days', 3)
            else:
                config = GroupCheckinConfig(
                    group_name=group_name,
                    is_enabled=True,
                    reminder_time=kwargs.get('reminder_time', '10:00'),
                    stats_time=kwargs.get('stats_time', '23:00'),
                    kick_time=kwargs.get('kick_time', '23:30'),
                    max_absent_days=kwargs.get('max_absent_days', 3)
                )
                self.session.add(config)

            self.session.commit()
            log(f"群聊 {group_name} 签到功能已启用")
            return True

        except Exception as e:
            self.session.rollback()
            log(f"启用群聊签到功能失败: {str(e)}", "ERROR")
            return False
    
    def disable_group_checkin(self, group_name: str) -> bool:
        """
        禁用群聊签到功能

        Args:
            group_name: 群聊名称

        Returns:
            bool: 是否禁用成功
        """
        try:
            config = self.session.query(GroupCheckinConfig).filter_by(group_name=group_name).first()
            if config:
                config.is_enabled = False
                self.session.commit()
                log(f"群聊 {group_name} 签到功能已禁用")
                return True
            else:
                log(f"群聊 {group_name} 未找到配置，无需禁用", "WARNING")
                return False

        except Exception as e:
            self.session.rollback()
            log(f"禁用群聊签到功能失败: {str(e)}", "ERROR")
            return False
    
    def is_checkin_enabled(self, group_name: str) -> bool:
        """
        检查群聊是否启用签到功能

        Args:
            group_name: 群聊名称

        Returns:
            bool: 是否启用签到功能
        """
        try:
            config = self.session.query(GroupCheckinConfig).filter_by(group_name=group_name).first()
            return config.is_enabled if config else False

        except Exception as e:
            log(f"检查签到功能状态失败: {str(e)}", "ERROR")
            return False
    
    def user_checkin(self, group_name: str, user_name: str) -> Tuple[bool, str]:
        """
        用户签到

        Args:
            group_name: 群聊名称
            user_name: 用户名称

        Returns:
            Tuple[bool, str]: (是否签到成功, 消息)
        """
        try:
            # 检查群聊是否启用签到功能
            if not self.is_checkin_enabled(group_name):
                return False, "该群聊未启用签到功能"

            today = datetime.now().strftime('%Y-%m-%d')

            # 检查今天是否已经签到
            existing_record = self.session.query(CheckinRecord).filter_by(
                group_name=group_name, user_name=user_name, checkin_date=today).first()

            if existing_record:
                return False, "您今天已经签到过了，请明天再来签到！"

            # 插入签到记录
            new_record = CheckinRecord(
                group_name=group_name,
                user_name=user_name,
                checkin_date=today,
                checkin_time=datetime.now().strftime('%H:%M:%S')
            )
            self.session.add(new_record)
            self.session.commit()

            # 获取连续签到天数
            consecutive_days = self.get_consecutive_checkin_days(group_name, user_name)

            log(f"用户 {user_name} 在群聊 {group_name} 签到成功")
            return True, f"签到成功！今天是您连续签到的第 {consecutive_days} 天 ✅"

        except Exception as e:
            self.session.rollback()
            log(f"用户签到失败: {str(e)}", "ERROR")
            return False, "签到失败，请稍后再试"
    
    def get_consecutive_checkin_days(self, group_name: str, user_name: str) -> int:
        """
        获取用户连续签到天数

        Args:
            group_name: 群聊名称
            user_name: 用户名称

        Returns:
            int: 连续签到天数
        """
        try:
            records = self.session.query(CheckinRecord.checkin_date).filter_by(
                group_name=group_name, user_name=user_name
            ).order_by(CheckinRecord.checkin_date.desc()).limit(30).all()

            if not records:
                return 0

            consecutive_days = 0
            current_date = datetime.now().date()

            for record in records:
                record_date = datetime.strptime(record[0], '%Y-%m-%d').date()
                expected_date = current_date - timedelta(days=consecutive_days)

                if record_date == expected_date:
                    consecutive_days += 1
                else:
                    break

            return consecutive_days

        except Exception as e:
            log(f"获取连续签到天数失败: {str(e)}", "ERROR")
            return 0
    
    def get_daily_checkin_stats(self, group_name: str, date: str = None) -> Dict:
        """
        获取每日签到统计

        Args:
            group_name: 群聊名称
            date: 日期，默认为今天

        Returns:
            Dict: 签到统计信息
        """
        try:
            if date is None:
                date = datetime.now().strftime('%Y-%m-%d')

            checkin_users = self.session.query(CheckinRecord.user_name, CheckinRecord.checkin_time).filter_by(
                group_name=group_name, checkin_date=date
            ).order_by(CheckinRecord.checkin_time).all()

            # 获取群聊所有成员（需要群聊管理器支持）
            all_members = []
            if self.group_manager:
                all_members = self.group_manager.get_cached_group_members(group_name)

            checkin_user_names = [user[0] for user in checkin_users]
            not_checkin_users = [member for member in all_members if member not in checkin_user_names]

            return {
                'date': date,
                'group_name': group_name,
                'total_members': len(all_members),
                'checkin_count': len(checkin_users),
                'not_checkin_count': len(not_checkin_users),
                'checkin_users': checkin_users,
                'not_checkin_users': not_checkin_users,
                'checkin_rate': len(checkin_users) / len(all_members) * 100 if all_members else 0
            }

        except Exception as e:
            log(f"获取签到统计失败: {str(e)}", "ERROR")
            return {}
    
    def get_absent_users(self, group_name: str, max_absent_days: int = 3) -> List[Tuple[str, int]]:
        """
        获取连续缺勤的用户
        
        Args:
            group_name: 群聊名称
            max_absent_days: 最大缺勤天数
            
        Returns:
            List[Tuple[str, int]]: [(用户名, 缺勤天数), ...]
        """
        try:
            if not self.group_manager:
                return []
            
            all_members = self.group_manager.get_cached_group_members(group_name)
            absent_users = []
            
            for member in all_members:
                absent_days = self.get_user_absent_days(group_name, member)
                if absent_days >= max_absent_days:
                    absent_users.append((member, absent_days))
            
            return absent_users
            
        except Exception as e:
            log(f"获取缺勤用户失败: {str(e)}", "ERROR")
            return []
    
    def get_user_absent_days(self, group_name: str, user_name: str) -> int:
        """
        获取用户连续缺勤天数

        Args:
            group_name: 群聊名称
            user_name: 用户名称

        Returns:
            int: 连续缺勤天数
        """
        try:
            last_checkin_date = self.session.query(func.max(CheckinRecord.checkin_date)).filter_by(
                group_name=group_name, user_name=user_name).scalar()

            if not last_checkin_date:
                # 从未签到，返回一个较大的数值
                return 999

            last_date = datetime.strptime(last_checkin_date, '%Y-%m-%d').date()
            today = datetime.now().date()
            absent_days = (today - last_date).days - 1  # 减1是因为当天还没结束

            return max(0, absent_days)

        except Exception as e:
            log(f"获取用户缺勤天数失败: {str(e)}", "ERROR")
            return 0
    
    def kick_absent_users(self, group_name: str, max_absent_days: int = 3) -> List[str]:
        """
        踢出连续缺勤的用户
        
        Args:
            group_name: 群聊名称
            max_absent_days: 最大缺勤天数
            
        Returns:
            List[str]: 被踢出的用户列表
        """
        try:
            if not self.group_manager:
                log("群聊管理器未设置，无法踢出用户", "ERROR")
                return []
            
            absent_users = self.get_absent_users(group_name, max_absent_days)
            kicked_users = []
            
            for user_name, absent_days in absent_users:
                try:
                    # 踢出用户
                    success = self.group_manager.remove_group_members(group_name, user_name)
                    if success:
                        kicked_users.append(user_name)
                        
                        # 记录踢人记录
                        self.record_kick(group_name, user_name, f"连续{absent_days}天未签到", absent_days)
                        log(f"已踢出用户 {user_name}（连续{absent_days}天未签到）")
                    else:
                        log(f"踢出用户 {user_name} 失败", "ERROR")
                        
                except Exception as e:
                    log(f"踢出用户 {user_name} 异常: {str(e)}", "ERROR")
            
            return kicked_users
            
        except Exception as e:
            log(f"踢出缺勤用户失败: {str(e)}", "ERROR")
            return []
    
    def record_kick(self, group_name: str, user_name: str, reason: str, absent_days: int):
        """
        记录踢人操作

        Args:
            group_name: 群聊名称
            user_name: 用户名称
            reason: 踢人原因
            absent_days: 缺勤天数
        """
        try:
            new_record = KickRecord(
                group_name=group_name,
                user_name=user_name,
                kick_reason=reason,
                kick_date=datetime.now().strftime('%Y-%m-%d'),
                absent_days=absent_days
            )
            self.session.add(new_record)
            self.session.commit()

        except Exception as e:
            self.session.rollback()
            log(f"记录踢人操作失败: {str(e)}", "ERROR")
    
    def get_group_checkin_config(self, group_name: str) -> Optional[Dict]:
        """
        获取群聊签到配置

        Args:
            group_name: 群聊名称

        Returns:
            Optional[Dict]: 配置信息
        """
        try:
            config = self.session.query(GroupCheckinConfig).filter_by(group_name=group_name).first()
            if config:
                return {
                    'group_name': config.group_name,
                    'is_enabled': config.is_enabled,
                    'reminder_time': config.reminder_time,
                    'stats_time': config.stats_time,
                    'kick_time': config.kick_time,
                    'max_absent_days': config.max_absent_days
                }
            return None

        except Exception as e:
            log(f"获取群聊签到配置失败: {str(e)}", "ERROR")
            return None
    
    def get_enabled_groups(self) -> List[str]:
        """
        获取启用签到功能的群聊列表

        Returns:
            List[str]: 群聊名称列表
        """
        try:
            groups = self.session.query(GroupCheckinConfig.group_name).filter_by(is_enabled=True).all()
            return [group[0] for group in groups]

        except Exception as e:
            log(f"获取启用签到的群聊列表失败: {str(e)}", "ERROR")
            return []

    def cleanup_invalid_checkin_records(self) -> Dict[str, int]:
        """
        清理无效的签到记录（用户已不在群聊中）

        Returns:
            Dict[str, int]: 每个群聊清理的记录数量
        """
        try:
            if not self.group_manager:
                log("群聊管理器未设置，无法清理签到记录", "ERROR")
                return {}

            cleanup_stats = {}
            enabled_groups = self.get_enabled_groups()

            for group_name in enabled_groups:
                try:
                    # 获取群聊当前成员列表
                    current_members = self.group_manager.get_cached_group_members(group_name)
                    if not current_members:
                        log(f"无法获取群聊 {group_name} 的成员列表，跳过清理", "WARNING")
                        continue

                    # 获取数据库中该群聊的所有签到用户
                    db_users = self.get_all_checkin_users(group_name)

                    # 找出不在群聊中的用户
                    invalid_users = [user for user in db_users if user not in current_members]

                    if invalid_users:
                        # 删除这些用户的签到记录
                        deleted_count = self.delete_user_checkin_records(group_name, invalid_users)
                        cleanup_stats[group_name] = deleted_count

                        log(f"群聊 {group_name} 清理了 {deleted_count} 条无效签到记录，涉及用户: {', '.join(invalid_users)}")
                    else:
                        log(f"群聊 {group_name} 无需清理签到记录")
                        cleanup_stats[group_name] = 0

                except Exception as e:
                    log(f"清理群聊 {group_name} 签到记录异常: {str(e)}", "ERROR")
                    cleanup_stats[group_name] = -1

            total_cleaned = sum(count for count in cleanup_stats.values() if count > 0)
            if total_cleaned > 0:
                log(f"签到记录清理完成，总共清理了 {total_cleaned} 条记录")
            else:
                log("签到记录清理完成，无需清理任何记录")

            return cleanup_stats

        except Exception as e:
            log(f"清理签到记录异常: {str(e)}", "ERROR")
            return {}

    def get_all_checkin_users(self, group_name: str) -> List[str]:
        """
        获取数据库中某个群聊的所有签到用户

        Args:
            group_name: 群聊名称

        Returns:
            List[str]: 用户名列表
        """
        try:
            users = self.session.query(CheckinRecord.user_name).filter_by(group_name=group_name).distinct().all()
            return [user[0] for user in users]

        except Exception as e:
            log(f"获取群聊 {group_name} 签到用户列表失败: {str(e)}", "ERROR")
            return []

    def delete_user_checkin_records(self, group_name: str, user_names: List[str]) -> int:
        """
        删除指定用户的所有签到记录

        Args:
            group_name: 群聊名称
            user_names: 要删除的用户名列表

        Returns:
            int: 删除的记录数量
        """
        try:
            if not user_names:
                return 0

            deleted_count = self.session.query(CheckinRecord).filter(
                CheckinRecord.group_name == group_name,
                CheckinRecord.user_name.in_(user_names)
            ).delete(synchronize_session=False)

            self.session.commit()

            return deleted_count

        except Exception as e:
            self.session.rollback()
            log(f"删除用户签到记录失败: {str(e)}", "ERROR")
            return 0

    def get_cleanup_stats_message(self, cleanup_stats: Dict[str, int]) -> str:
        """
        生成清理统计消息

        Args:
            cleanup_stats: 清理统计数据

        Returns:
            str: 统计消息
        """
        try:
            if not cleanup_stats:
                return "📋 签到记录清理完成\n\n暂无需要清理的记录"

            message = "📋 签到记录清理报告\n\n"

            total_cleaned = 0
            cleaned_groups = []
            error_groups = []

            for group_name, count in cleanup_stats.items():
                if count > 0:
                    total_cleaned += count
                    cleaned_groups.append(f"• {group_name}: 清理 {count} 条记录")
                elif count == -1:
                    error_groups.append(f"• {group_name}: 清理失败")
                else:
                    # count == 0，无需清理
                    pass

            if total_cleaned > 0:
                message += f"🧹 总共清理了 {total_cleaned} 条无效记录\n\n"
                message += "清理详情：\n"
                message += "\n".join(cleaned_groups)
            else:
                message += "✅ 所有签到记录都是有效的，无需清理"

            if error_groups:
                message += "\n\n⚠️ 清理失败的群聊：\n"
                message += "\n".join(error_groups)

            message += f"\n\n🕐 清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            return message

        except Exception as e:
            log(f"生成清理统计消息失败: {str(e)}", "ERROR")
            return f"清理统计消息生成失败: {str(e)}"

from __future__ import annotations

"""outdoor_reminder.py
检测用户消息中是否存在外出/出门相关意图，若有则查询天气并在恶劣天气时提醒。
"""
import re
import logging
from typing import Optional

from src.services.weather.weather_service import WeatherService
from src.config import config

logger = logging.getLogger(__name__)


class OutdoorWeatherReminder:
    """外出天气提醒模块。"""

    _motivation_patterns = [
        r"\b出门\b", r"\b出去\b", r"上班", r"上学", r"逛街", r"见面", r"跑步",
        r"户外", r"外出", r"散步", r"买菜", r"购物", r"去公司", r"去学校",
    ]

    _bad_weather_keywords = [
        "雨", "阵雨", "雷雨", "暴雨", "大雨", "雪", "雷阵雨", "大风", "雾", "沙尘", "霾",
    ]

    def __init__(self, weather_service: WeatherService, location: Optional[str] = None):
        self.weather_service = weather_service
        self.location = location or config.weather.default_location
        self._motivation_regex = re.compile("|".join(self._motivation_patterns))

    def check_message(self, content: str) -> Optional[tuple[str, str]]:
        m = self._motivation_regex.search(content)
        if not m:
            return None
        motivation = m.group(0)
        summary = self.weather_service.get_weather(self.location)
        if any(kw in summary for kw in self._bad_weather_keywords):
            return motivation, summary
        return None 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天气查询模块
参考My-Dream-Moments-WeChat-wxauto项目的天气功能
使用高德地图API获取天气信息
"""

import re
import time
import requests
from datetime import datetime, timedelta
from typing import Optional, Dict, List
import config
from utils.logger import log


class WeatherManager:
    """天气管理类"""

    def __init__(self):
        self.weather_cache = {}  # 天气缓存
        self.api_key = config.GAODE_API_KEY
        self.weather_url = config.GAODE_WEATHER_URL
        self.geocode_url = config.GAODE_GEOCODE_URL
        self.cache_hours = config.WEATHER_CACHE_HOURS

    def get_city_adcode(self, city_name: str) -> Optional[str]:
        """获取城市的行政区划代码"""
        try:
            params = {
                "key": self.api_key,
                "address": f"{city_name}市",  # 补充"市"提高精度
                "citylimit": "true"
            }
            response = requests.get(self.geocode_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data["status"] == "1" and data["geocodes"]:
                # 优先返回精确匹配结果
                for geo in data["geocodes"]:
                    if city_name in geo["formatted_address"]:
                        return geo["adcode"]
                # 如果没有精确匹配，返回第一个结果
                return data["geocodes"][0]["adcode"]

            log(f"未找到城市 {city_name} 的地理编码", "WARNING")
            return None
        except Exception as e:
            log(f"获取城市编码失败: {str(e)}", "ERROR")
            return None

    def get_weather_data(self, city: str) -> Optional[Dict]:
        """获取城市天气数据"""
        try:
            # 获取城市编码
            adcode = self.get_city_adcode(city)
            if not adcode:
                return None

            # 获取天气数据
            params = {
                "key": self.api_key,
                "city": adcode,
                "extensions": "base"  # base:实时 all:预报
            }
            response = requests.get(self.weather_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data["status"] != "1" or not data.get("lives"):
                log(f"天气API返回错误: {data.get('info', '未知错误')}", "ERROR")
                return None

            return data["lives"][0]
        except Exception as e:
            log(f"获取天气数据失败: {str(e)}", "ERROR")
            return None

    def format_weather_info(self, weather_data: Dict) -> str:
        """格式化天气信息"""
        try:
            return (
                f"🌆 {weather_data['province']}{weather_data['city']}天气\n"
                f"🌡️ 实时温度：{weather_data['temperature']}℃\n"
                f"🌦️ 天气状况：{weather_data['weather']}\n"
                f"💨 风向风力：{weather_data['winddirection']}风{weather_data['windpower']}级\n"
                f"💧 空气湿度：{weather_data['humidity']}%\n"
                f"🕒 发布时间：{weather_data['reporttime']}"
            )
        except Exception as e:
            log(f"格式化天气信息失败: {str(e)}", "ERROR")
            return "天气信息格式化失败"

    def get_weather(self, city: str) -> str:
        """获取城市天气信息（带缓存）"""
        try:
            # 检查缓存
            if city in self.weather_cache:
                cache_data = self.weather_cache[city]
                time_diff = datetime.now() - cache_data['timestamp']
                if time_diff.total_seconds() < self.cache_hours * 3600:
                    log(f"使用缓存的{city}天气信息")
                    return self.format_weather_info(cache_data['data'])

            # 获取新的天气数据
            weather_data = self.get_weather_data(city)
            if not weather_data:
                return f"暂不支持{city}天气查询哦~"

            # 缓存数据
            self.weather_cache[city] = {
                'data': weather_data,
                'timestamp': datetime.now()
            }

            log(f"成功获取{city}天气信息")
            return self.format_weather_info(weather_data)

        except Exception as e:
            log(f"天气查询失败: {str(e)}", "ERROR")
            return "天气查询失败，请稍后再试"

    def get_multiple_weather(self, cities: List[str]) -> List[str]:
        """获取多个城市的天气信息"""
        weather_reports = []

        for city in cities:
            try:
                report = self.get_weather(city)
                weather_reports.append(report)
                time.sleep(1)  # 防止API频率限制
            except Exception as e:
                log(f"{city}天气获取失败: {str(e)}", "ERROR")
                weather_reports.append(f"【{city}】天气获取失败")

        return weather_reports

    def is_weather_request(self, text: str) -> bool:
        """判断是否为天气查询请求"""
        patterns = [
            r".*天气.*",
            r".*温度.*",
            r".*多少度.*",
            r".*冷不冷.*",
            r".*热不热.*"
        ]
        return any(re.search(pattern, text, re.IGNORECASE) for pattern in patterns)

    def extract_city(self, text: str) -> str:
        """从文本中提取城市名称"""
        patterns = [
            r"(.+?)(天气|温度|几度|多少度)$",  # 匹配结尾模式："徐州天气"
            r"现在?(.+?)(的天气)?(怎么样|如何)",  # 匹配："现在徐州天气如何"
            r"(今天|现在)?(.+?)温度",  # 匹配："今天徐州温度"
            r"(.*?)(市|县|区)(的天气)?"  # 匹配行政区划
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                # 提取第一个有效分组
                city = next((g for g in match.groups() if g and g not in ["天气", "温度", "今天", "现在"]), "")
                if city and len(city) >= 2:
                    return re.sub(r"(现在|今天|的|天气)", "", city)

        # 保底逻辑：取前2-4个字符
        return text[:4] if len(text) >= 2 else ""

    def get_cached_weather_for_prompt(self, city: str) -> str:
        """获取用于AI提示的简化天气信息"""
        try:
            if city in self.weather_cache:
                cache_data = self.weather_cache[city]
                time_diff = datetime.now() - cache_data['timestamp']
                if time_diff.total_seconds() < self.cache_hours * 3600:
                    weather_data = cache_data['data']
                    return f"[当前{city}天气]: {weather_data.get('weather')} {weather_data.get('temperature')}°C"
            return ""
        except Exception as e:
            log(f"获取提示天气信息失败: {str(e)}", "ERROR")
            return ""

    def update_weather_cache(self, cities: List[str]):
        """更新指定城市的天气缓存"""
        for city in cities:
            try:
                weather_data = self.get_weather_data(city)
                if weather_data:
                    self.weather_cache[city] = {
                        'data': weather_data,
                        'timestamp': datetime.now()
                    }
                    log(f"成功更新{city}天气缓存")
                time.sleep(1)  # 防止API频率限制
            except Exception as e:
                log(f"更新{city}天气缓存失败: {str(e)}", "ERROR")
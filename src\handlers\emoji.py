"""
表情包处理模块
负责处理表情包相关功能，包括:
- 表情标签识别
- 表情包选择
- 文件管理
"""

import os
import random
import logging
from typing import Optional
from datetime import datetime
import pyautogui
import time
from wxauto import WeChat
from src.config import config

logger = logging.getLogger('main')

class EmojiHandler:
    def __init__(self, root_dir):
        self.root_dir = root_dir
        # 修改表情包目录路径为avatar目录下的emojis
        self.emoji_dir = os.path.join(root_dir, config.behavior.context.avatar_dir, "emojis")

        # 支持的表情类型
        self.emotion_types = [
    'happy', 'sad', 'angry', 'neutral', 'love', 'funny', 'cute', 'bored', 'shy',
    'embarrassed', 'sleepy', 'lonely', 'hungry', 'comfort', 'surprise', 'confused',
    'playful', 'excited', 'tease', 'hot', 'speechless', 'scared', 'emo_1',
    'emo_2', 'emo_3', 'emo_4', 'emo_5', 'afraid', 'amused', 'anxious',
    'confident', 'cold', 'suspicious', 'loving', 'curious', 'envious',
    'jealous', 'miserable', 'stupid', 'sick', 'ashamed', 'withdrawn',
    'indifferent', 'sorry', 'determined', 'crazy', 'bashful', 'depressed',
    'enraged', 'frightened', 'interested', 'hopeful', 'regretful', 'stubborn',
    'thirsty', 'guilty', 'nervous', 'disgusted', 'proud', 'ecstatic',
    'frustrated', 'hurt', 'tired', 'smug', 'thoughtful', 'pained', 'optimistic',
    'relieved', 'puzzled', 'shocked', 'joyful', 'skeptical', 'bad', 'worried']


        self.screenshot_dir = os.path.join(root_dir, 'screenshot')

        # 缓存微信窗口以提高性能
        self._cached_wechat_window = None
        self._window_cache_time = 0
        self._window_cache_timeout = 30  # 30秒后重新查找窗口

    def extract_emotion_tags(self, text: str) -> list:
        """从文本中提取表情标签"""
        tags = []
        start = 0
        while True:
            start = text.find('[', start)
            if start == -1:
                break
            end = text.find(']', start)
            if end == -1:
                break
            tag = text[start+1:end].lower()
            if tag in self.emotion_types:
                tags.append(tag)
                logger.info(f"检测到表情标签: {tag}")
            start = end + 1
        return tags

    def _get_wechat_window(self):
        """获取微信窗口（带缓存）"""
        import time
        current_time = time.time()

        # 检查缓存是否有效
        if (self._cached_wechat_window and
            current_time - self._window_cache_time < self._window_cache_timeout):
            # 验证窗口是否仍然存在
            try:
                if (hasattr(self._cached_wechat_window, 'title') and
                    self._cached_wechat_window.title == '微信'):
                    return self._cached_wechat_window
            except:
                # 窗口可能已经关闭，清除缓存
                self._cached_wechat_window = None

        # 重新查找微信窗口
        all_windows = pyautogui.getAllWindows()
        for window in all_windows:
            if window.title == '微信' and window.width > 300 and window.height > 300:
                self._cached_wechat_window = window
                self._window_cache_time = current_time
                logger.debug(f'缓存微信窗口: {window.title} ({window.width}x{window.height})')
                return window

        return None

    def get_emoji_for_emotion(self, emotion_type: str) -> Optional[str]:
        """根据情感类型获取对应表情包"""
        try:
            target_dir = os.path.join(self.emoji_dir, emotion_type)
            logger.info(f"查找表情包目录: {target_dir}")

            if not os.path.exists(target_dir):
                logger.warning(f"情感目录不存在: {target_dir}")
                return None

            emoji_files = [f for f in os.listdir(target_dir)
                          if f.lower().endswith(('.gif', '.jpg', '.png', '.jpeg'))]

            if not emoji_files:
                logger.warning(f"目录中未找到表情包: {target_dir}")
                return None

            selected = random.choice(emoji_files)
            emoji_path = os.path.join(target_dir, selected)
            logger.info(f"已选择 {emotion_type} 表情包: {emoji_path}")
            return emoji_path

        except Exception as e:
            logger.error(f"获取表情包失败: {str(e)}")
            return None

    def capture_and_save_screenshot(self, who: str) -> str:
        """捕获并保存聊天窗口截图（优化版本）"""
        try:
            # 确保截图目录存在
            os.makedirs(self.screenshot_dir, exist_ok=True)

            screenshot_path = os.path.join(
                self.screenshot_dir,
                f'{who}_{datetime.now().strftime("%Y%m%d%H%M%S")}.png'
            )

            try:
                # 使用缓存的微信窗口（性能优化）
                chat_window = self._get_wechat_window()

                if not chat_window:
                    logger.error(f'无法找到微信主窗口')
                    return None

                logger.info(f'使用微信主窗口进行截图: {chat_window.title}')

                # 快速激活窗口（如果需要）
                if chat_window.isMinimized:
                    chat_window.restore()
                if not chat_window.isActive:
                    chat_window.activate()

                # 获取窗口的坐标和大小
                x, y, width, height = chat_window.left, chat_window.top, chat_window.width, chat_window.height

                # 减少等待时间
                time.sleep(0.3)  # 从1秒减少到0.3秒

                # 截取指定窗口区域的屏幕
                screenshot = pyautogui.screenshot(region=(x, y, width, height))
                screenshot.save(screenshot_path)
                logger.info(f'已保存截图: {screenshot_path}')
                return screenshot_path

            except Exception as e:
                logger.error(f'截取或保存截图失败: {str(e)}')
                return None

        except Exception as e:
            logger.error(f'创建截图目录失败: {str(e)}')
            return None

    def cleanup_screenshot_dir(self):
        """清理截图目录"""
        try:
            if os.path.exists(self.screenshot_dir):
                for file in os.listdir(self.screenshot_dir):
                    file_path = os.path.join(self.screenshot_dir, file)
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        logger.error(f"删除截图失败 {file_path}: {str(e)}")
        except Exception as e:
            logger.error(f"清理截图目录失败: {str(e)}")

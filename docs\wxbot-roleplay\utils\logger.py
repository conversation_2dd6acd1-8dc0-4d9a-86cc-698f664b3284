#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志模块
参考SiverWxBotWebV2.2.3项目的日志风格
"""

import logging
import os
from datetime import datetime
import config


def setup_logger():
    """设置日志配置"""
    if not config.ENABLE_LOGGING:
        return

    # 创建logs目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 设置日志级别
    level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)

    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(level)

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器
    log_file = os.path.join(log_dir, f"bot_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)


def log(message, level="INFO"):
    """简单的日志函数，兼容SiverWxBotWebV2.2.3风格"""
    logger = logging.getLogger(__name__)

    if level.upper() == "DEBUG":
        logger.debug(message)
    elif level.upper() == "INFO":
        logger.info(message)
    elif level.upper() == "WARNING":
        logger.warning(message)
    elif level.upper() == "ERROR":
        logger.error(message)
    else:
        logger.info(message)
"""
微信重连管理器
负责检测微信状态、自动重连、二维码处理等功能
"""

import time
import threading
import os
import logging
import psutil
from typing import Optional, Callable
from datetime import datetime, timedelta

from .email_sender import EmailSender, get_smtp_config

logger = logging.getLogger(__name__)


class WeChatReconnectManager:
    """微信重连管理器"""
    
    def __init__(self, wx_instance, config, on_reconnect_success: Optional[Callable] = None):
        """
        初始化重连管理器
        
        Args:
            wx_instance: 微信实例
            config: 配置对象
            on_reconnect_success: 重连成功回调函数
        """
        self.wx = wx_instance
        self.config = config
        self.on_reconnect_success = on_reconnect_success
        
        # 重连配置
        self.reconnect_config = config.wechat_reconnect
        self.enabled = self.reconnect_config.enable_auto_reconnect
        self.check_interval = self.reconnect_config.check_interval
        self.max_retry_attempts = self.reconnect_config.max_retry_attempts
        self.qrcode_retry_interval = self.reconnect_config.qrcode_retry_interval
        
        # 邮件配置
        self.email_enabled = self.reconnect_config.email_enabled
        self.email_sender = None
        if self.email_enabled and self._validate_email_config():
            self.email_sender = self._create_email_sender()
        
        # 状态变量
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_qrcode_time = None
        self.qrcode_retry_count = 0
        self.is_reconnecting = False
        
        logger.info(f"微信重连管理器初始化完成 - 启用状态: {self.enabled}")
    
    def _validate_email_config(self) -> bool:
        """验证邮件配置是否完整"""
        required_fields = ['sender_email', 'sender_password', 'recipient_email']
        for field in required_fields:
            if not getattr(self.reconnect_config, field):
                logger.warning(f"邮件配置不完整，缺少: {field}")
                return False
        return True
    
    def _create_email_sender(self) -> EmailSender:
        """创建邮件发送器"""
        # 自动获取SMTP配置
        smtp_server, smtp_port = get_smtp_config(self.reconnect_config.sender_email)
        
        # 如果配置中指定了SMTP服务器，则使用配置的
        if self.reconnect_config.smtp_server:
            smtp_server = self.reconnect_config.smtp_server
        if self.reconnect_config.smtp_port:
            smtp_port = self.reconnect_config.smtp_port
        
        return EmailSender(
            smtp_server=smtp_server,
            smtp_port=smtp_port,
            sender_email=self.reconnect_config.sender_email,
            sender_password=self.reconnect_config.sender_password,
            recipient_email=self.reconnect_config.recipient_email
        )
    
    def start_monitoring(self):
        """开始监控微信状态"""
        if not self.enabled:
            logger.info("微信自动重连功能已禁用")
            return
        
        if self.is_monitoring:
            logger.warning("微信状态监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="WeChatReconnectMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("微信状态监控已启动")
    
    def stop_monitoring(self):
        """停止监控微信状态"""
        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        logger.info("微信状态监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        logger.info(f"开始监控微信状态，检查间隔: {self.check_interval}秒")
        
        while self.is_monitoring:
            try:
                # 检查微信状态
                if not self._check_wechat_status():
                    logger.warning("检测到微信连接断开，开始重连流程")
                    self._handle_disconnection()
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"监控循环中发生错误: {e}")
                time.sleep(self.check_interval)
    
    def _check_wechat_status(self) -> bool:
        """检查微信状态"""
        try:
            # 使用IsOnline方法检查微信状态
            if hasattr(self.wx, 'IsOnline'):
                return self.wx.IsOnline()
            else:
                # 如果没有IsOnline方法，使用进程检测
                return self._check_wechat_process()
        except Exception as e:
            logger.error(f"检查微信状态时发生错误: {e}")
            return False

    def _check_wechat_process(self) -> bool:
        """检查微信进程是否存在"""
        try:
            # 检查微信进程是否运行
            wechat_processes = []
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                        wechat_processes.append(proc.info['name'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if not wechat_processes:
                logger.debug("未找到微信进程，微信可能已关闭")
                return False

            # 进程存在，再尝试通过wxauto检查功能是否正常
            try:
                # 尝试获取会话列表
                sessions = self.wx.GetSessionList()
                if sessions is None:
                    logger.debug("微信进程存在但GetSessionList返回None")
                    return False

                # 如果能获取到会话列表，认为微信正常
                logger.debug(f"微信状态正常，找到{len(wechat_processes)}个微信进程，会话列表长度: {len(sessions) if sessions else 0}")
                return True

            except Exception as e:
                logger.debug(f"微信进程存在但功能异常: {e}")
                return False

        except Exception as e:
            logger.error(f"检查微信进程时发生错误: {e}")
            return False
    
    def _handle_disconnection(self):
        """处理微信断开连接"""
        if self.is_reconnecting:
            logger.info("重连流程已在进行中，跳过")
            return
        
        self.is_reconnecting = True
        
        try:
            # 尝试自动重连
            if self._attempt_auto_reconnect():
                logger.info("微信自动重连成功")
                if self.on_reconnect_success:
                    self.on_reconnect_success()
                return
            
            # 自动重连失败，尝试截图发送邮件
            logger.warning("自动重连失败，尝试发送微信截图到邮箱")
            self._handle_qrcode_login()
            
        finally:
            self.is_reconnecting = False

    def _attempt_auto_reconnect(self) -> bool:
        """尝试自动重连"""
        logger.info("开始尝试自动重连...")

        for attempt in range(self.max_retry_attempts):
            try:
                logger.info(f"自动重连尝试 {attempt + 1}/{self.max_retry_attempts}")

                # 导入微信登录点击器
                from src.Wechat_Login_Clicker.Wechat_Login_Clicker import click_wechat_buttons

                # 执行点击操作
                result = click_wechat_buttons()

                if result:
                    # 等待一段时间让微信完成登录
                    time.sleep(5)

                    # 检查是否重连成功
                    if self._check_wechat_status():
                        logger.info(f"自动重连成功 (尝试 {attempt + 1})")
                        return True
                    else:
                        logger.warning(f"自动重连尝试 {attempt + 1} 失败，微信仍未连接")
                else:
                    logger.warning(f"自动重连尝试 {attempt + 1} 失败，找不到微信登录窗口")

                # 等待后重试
                if attempt < self.max_retry_attempts - 1:
                    time.sleep(10)

            except Exception as e:
                logger.error(f"自动重连尝试 {attempt + 1} 时发生错误: {e}")
                if attempt < self.max_retry_attempts - 1:
                    time.sleep(10)

        logger.error("所有自动重连尝试均失败")
        return False

    def _handle_qrcode_login(self):
        """处理微信截图登录"""
        try:
            # 检查是否需要重新发送截图
            current_time = datetime.now()
            if (self.last_qrcode_time and
                current_time - self.last_qrcode_time < timedelta(seconds=self.qrcode_retry_interval)):
                logger.info("距离上次发送微信截图时间过短，跳过")
                return

            # 获取微信窗口截图
            screenshot_path = self._get_qrcode()
            if not screenshot_path:
                logger.error("获取微信窗口截图失败")
                return

            # 发送邮件
            if self.email_enabled and self.email_sender:
                success = self.email_sender.send_qrcode(screenshot_path, self.qrcode_retry_count)
                if success:
                    self.last_qrcode_time = current_time
                    self.qrcode_retry_count += 1
                    logger.info(f"微信截图邮件发送成功 (第{self.qrcode_retry_count}次)")

                    # 启动登录监控
                    self._start_qrcode_monitoring(screenshot_path)
                else:
                    logger.error("微信截图邮件发送失败")
            else:
                logger.warning("邮件功能未启用或配置不完整，微信截图已保存到: " + screenshot_path)
                # 即使不发邮件，也启动监控
                self._start_qrcode_monitoring(screenshot_path)

        except Exception as e:
            logger.error(f"处理微信截图登录时发生错误: {e}")

    def _get_qrcode(self) -> Optional[str]:
        """获取微信登录界面（通过截图方式）"""
        try:
            # 生成截图文件路径
            timestamp = int(time.time())
            screenshot_filename = f"wechat_screenshot_{timestamp}.png"
            screenshot_path = os.path.join("data", "cache", screenshot_filename)

            # 确保目录存在
            os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)

            # 检查是否有get_qrcode方法（保留兼容性）
            if hasattr(self.wx, 'get_qrcode'):
                logger.info("使用微信实例的get_qrcode方法获取二维码")
                try:
                    result = self.wx.get_qrcode(path=screenshot_path)
                    if result and os.path.exists(screenshot_path):
                        logger.info(f"二维码已保存到: {screenshot_path}")
                        return screenshot_path
                    else:
                        logger.warning("get_qrcode方法执行失败，转为截图方式")
                except Exception as e:
                    logger.warning(f"get_qrcode方法出错: {e}，转为截图方式")

            # 使用截图方式获取微信窗口
            logger.info("使用截图方式获取微信登录界面")
            screenshot_result = self._capture_wechat_window(screenshot_path)

            if screenshot_result and os.path.exists(screenshot_path):
                logger.info(f"微信窗口截图已保存到: {screenshot_path}")
                return screenshot_path
            else:
                logger.error("截图方式获取微信窗口失败")
                return None

        except Exception as e:
            logger.error(f"获取微信登录界面时发生错误: {e}")
            return None

    def _capture_wechat_window(self, save_path: str) -> bool:
        """截图微信窗口"""
        try:
            import win32gui
            import win32ui
            import win32con
            from PIL import Image

            # 查找微信窗口
            hwnd = win32gui.FindWindow(None, "微信")
            if hwnd == 0:
                logger.error("找不到微信窗口")
                return False

            # 获取窗口位置和大小
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top

            # 确保窗口可见
            if win32gui.IsIconic(hwnd):
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.5)

            # 将窗口置于前台
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.5)

            # 获取窗口设备上下文
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            # 创建位图对象
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            # 截图
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)

            # 保存为文件
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)

            # 转换为PIL图像并保存
            img = Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1
            )
            img.save(save_path, 'PNG')

            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)

            logger.info(f"微信窗口截图成功: {save_path}")
            return True

        except ImportError as e:
            logger.error(f"缺少必要的库: {e}")
            logger.error("请安装: pip install pillow pywin32")
            return False
        except Exception as e:
            logger.error(f"截图微信窗口时发生错误: {e}")
            return False

    def _start_qrcode_monitoring(self, screenshot_path: str):
        """启动登录监控"""
        def monitor_login():
            """监控微信登录状态"""
            logger.info("开始监控微信登录状态...")

            # 微信二维码通常2分钟过期，我们监控150秒
            monitor_duration = 150
            check_interval = 5
            checks = monitor_duration // check_interval

            for i in range(checks):
                try:
                    # 检查微信是否已经登录
                    if self._check_wechat_status():
                        logger.info("检测到微信已登录，扫码登录成功")

                        # 清理截图文件
                        try:
                            if os.path.exists(screenshot_path):
                                os.remove(screenshot_path)
                                logger.info("已清理微信截图文件")
                        except:
                            pass

                        # 重置重试计数
                        self.qrcode_retry_count = 0

                        # 调用成功回调
                        if self.on_reconnect_success:
                            self.on_reconnect_success()

                        return

                    time.sleep(check_interval)

                except Exception as e:
                    logger.error(f"监控微信登录时发生错误: {e}")
                    time.sleep(check_interval)

            # 监控超时，二维码可能已过期
            logger.warning("登录监控超时，二维码可能已过期")

            # 清理过期的截图文件
            try:
                if os.path.exists(screenshot_path):
                    os.remove(screenshot_path)
                    logger.info("已清理过期的微信截图文件")
            except:
                pass

            # 如果启用了邮件，考虑重新发送
            if (self.email_enabled and self.email_sender and
                self.qrcode_retry_count < 5):  # 最多重试5次
                logger.info("准备重新发送微信截图...")
                time.sleep(30)  # 等待30秒后重新发送
                if self.is_monitoring and not self._check_wechat_status():
                    self._handle_qrcode_login()

        # 在后台线程中监控
        monitor_thread = threading.Thread(
            target=monitor_login,
            name="WeChatLoginMonitor",
            daemon=True
        )
        monitor_thread.start()

    def force_reconnect(self) -> bool:
        """强制重连（手动触发）"""
        logger.info("手动触发微信重连...")

        if self.is_reconnecting:
            logger.warning("重连流程已在进行中")
            return False

        # 先检查当前状态
        if self._check_wechat_status():
            logger.info("微信当前状态正常，无需重连")
            return True

        # 执行重连流程
        self.is_reconnecting = True
        try:
            # 尝试自动重连
            if self._attempt_auto_reconnect():
                logger.info("强制重连成功")
                return True

            # 自动重连失败，尝试发送微信截图
            logger.warning("自动重连失败，尝试发送微信截图到邮箱")
            self._handle_qrcode_login()
            return False  # 需要用户手动扫描二维码

        finally:
            self.is_reconnecting = False

    def test_email_config(self) -> bool:
        """测试邮件配置"""
        if not self.email_enabled:
            logger.warning("邮件功能未启用")
            return False

        if not self.email_sender:
            logger.error("邮件发送器未初始化")
            return False

        return self.email_sender.test_connection()

    def get_status(self) -> dict:
        """获取重连管理器状态"""
        return {
            'enabled': self.enabled,
            'monitoring': self.is_monitoring,
            'reconnecting': self.is_reconnecting,
            'email_enabled': self.email_enabled,
            'wechat_online': self._check_wechat_status(),
            'qrcode_retry_count': self.qrcode_retry_count,
            'last_qrcode_time': self.last_qrcode_time.isoformat() if self.last_qrcode_time else None
        }

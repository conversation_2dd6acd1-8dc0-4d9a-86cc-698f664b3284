"""
调试命令处理模块
提供调试命令的解析和执行功能
"""

import os
import logging
import json
import threading
from datetime import datetime
from typing import List, Dict, Tuple, Any, Optional, Callable
from modules.memory.content_generator import ContentGenerator  # 导入内容生成服务

logger = logging.getLogger('main')

class DebugCommandHandler:
    """调试命令处理器类，处理各种调试命令"""

    def __init__(self, root_dir: str, memory_service=None, llm_service=None, content_generator=None):
        """
        初始化调试命令处理器

        Args:
            root_dir: 项目根目录
            memory_service: 记忆服务实例
            llm_service: LLM服务实例
            content_generator: 内容生成服务实例
        """
        self.root_dir = root_dir
        self.memory_service = memory_service
        self.llm_service = llm_service
        self.content_generator = content_generator
        self.DEBUG_PREFIX = "/"
        self.AVATAR_PREFIX = "#"

        # 如果没有提供内容生成服务，尝试初始化
        if not self.content_generator:
            try:
                from src.config import config
                self.content_generator = ContentGenerator(
                    root_dir=self.root_dir,
                    api_key=config.OPENAI_API_KEY,
                    base_url=config.OPENAI_API_BASE,
                    model=config.OPENAI_API_MODEL,
                    max_token=config.OPENAI_MAX_TOKENS,
                    temperature=config.OPENAI_TEMPERATURE,
                    memory_manager=self.memory_service
                )
                logger.info("内容生成服务初始化成功")
            except Exception as e:
                logger.error(f"初始化内容生成服务失败: {str(e)}")
                self.content_generator = None

    def is_debug_command(self, message: str) -> bool:
        """
        判断消息是否为调试命令

        Args:
            message: 用户消息

        Returns:
            bool: 是否为调试命令
        """
        return message.strip().startswith(self.DEBUG_PREFIX)

    def is_avatar_command(self, message: str) -> bool:
        """
        判断消息是否为人设切换命令

        Args:
            message: 用户消息

        Returns:
            bool: 是否为人设切换命令
        """
        return message.strip().startswith(self.AVATAR_PREFIX) and len(message.strip()) > 1

    def process_command(self, command: str, current_avatar: str, user_id: str, chat_id: str = None, callback: Callable = None) -> Tuple[bool, str]:
        """
        处理调试命令

        Args:
            command: 调试命令（包含/前缀）
            current_avatar: 当前角色名
            user_id: 用户ID
            chat_id: 聊天ID，用于异步回调
            callback: 回调函数，用于异步处理生成的内容

        Returns:
            Tuple[bool, str]: (是否需要拦截普通消息处理, 响应消息)
        """
        # 去除前缀并转为小写
        cmd = command.strip()[1:].lower()

        # 帮助命令
        if cmd == "help":
            return True, self._get_help_message()

        # 显示当前角色记忆
        elif cmd == "mem":
            return True, self._show_memory(current_avatar, user_id)
        
        # 强制刷新并显示当前角色记忆
        elif cmd == "mem refresh":
            return True, self._show_memory(current_avatar, user_id, force_refresh=True)

        # 重置当前角色的最近记忆
        elif cmd == "reset":
            return True, self._reset_short_memory(current_avatar, user_id)

        # 清空当前角色的核心记忆
        elif cmd == "clear":
            return True, self._clear_core_memory(current_avatar, user_id)

        # 清空当前角色的对话上下文
        elif cmd == "context":
            return True, self._clear_context(user_id)
        
        # 手动生成核心记忆
        elif cmd == "gen_core_mem":
            return True, self._gen_core_mem(current_avatar, user_id)

        # 内容生成命令，如果提供了回调函数，则使用异步方式
        elif cmd in ["diary", "state", "letter", "list", "pyq", "gift", "shopping"]:
            if callback and chat_id:
                # 使用异步方式生成内容
                return True, self._generate_content_async(cmd, current_avatar, user_id, chat_id, callback)
            else:
                # 使用同步方式生成内容
                return True, self._generate_content(cmd, current_avatar, user_id)

        # 记忆修正命令
        elif cmd.startswith("change "):
            return True, self._handle_memory_change(command, current_avatar, user_id)

        # 查看所有人设
        elif cmd == "avatar":
            return True, self._show_all_avatars()

        # 查看当前人设
        elif cmd == "now":
            return True, self._show_current_avatar(current_avatar)

        # 切换人设命令
        elif cmd.startswith("switch "):
            avatar_name = cmd[7:].strip()  # 去除"switch "前缀
            if not avatar_name:
                return True, "请指定要切换的人设名称，格式: /switch 人设名"
            return True, self._switch_avatar(avatar_name)

        # 退出调试模式
        elif cmd == "exit":
            return True, "已退出调试模式"

        # 无效命令
        else:
            return True, f"未知命令: {cmd}\n使用 /help 查看可用命令"

    def process_avatar_command(self, command: str, current_avatar: str, user_id: str) -> Tuple[bool, str]:
        """
        处理人设切换命令

        Args:
            command: 人设切换命令（包含#前缀）
            current_avatar: 当前角色名
            user_id: 用户ID

        Returns:
            Tuple[bool, str]: (是否需要拦截普通消息处理, 响应消息)
        """
        # 去除前缀，获取人设文件名
        avatar_file = command.strip()[1:].strip()
        
        if not avatar_file:
            return True, "请指定要切换的人设文件名，格式: #人设文件名"
        
        return True, self._switch_to_avatar_file(avatar_file, current_avatar)

    def _switch_to_avatar_file(self, avatar_file: str, current_avatar: str) -> str:
        """
        切换到指定的人设文件

        Args:
            avatar_file: 人设文件名（不带.md后缀）
            current_avatar: 当前角色名

        Returns:
            str: 操作结果
        """
        try:
            # 检查人设文件是否存在
            avatar_path = os.path.join(self.root_dir, "data", "avatars", current_avatar, f"{avatar_file}.md")
            
            if not os.path.exists(avatar_path):
                # 查找当前角色目录下有哪些可用的人设文件
                available_files = self._get_available_avatar_files(current_avatar)
                if available_files:
                    file_list = "\n".join([f"- {file}" for file in available_files])
                    return f"人设文件不存在: {avatar_file}.md\n\n当前角色可用人设文件:\n{file_list}"
                else:
                    return f"人设文件不存在: {avatar_file}.md\n当前角色目录下没有找到其他人设文件"
            
            # 读取新的人设文件内容
            with open(avatar_path, 'r', encoding='utf-8') as f:
                new_avatar_content = f.read()
            
            # 更新全局配置中的当前人设文件路径
            from src.config import config
            config.behavior.context.avatar_dir = os.path.join("data", "avatars", current_avatar)
            config.behavior.context.avatar_file = f"{avatar_file}.md"
            
            # 导入全局人设切换函数
            from src.main import update_avatar_content
            update_avatar_content(new_avatar_content, current_avatar)
            
            logger.info(f"已切换人设文件: {current_avatar}/{avatar_file}.md")
            return f"✅ 已成功切换到人设: {avatar_file}"
            
        except Exception as e:
            logger.error(f"切换人设文件失败: {str(e)}")
            return f"❌ 切换人设文件失败: {str(e)}"

    def _get_available_avatar_files(self, avatar_name: str) -> List[str]:
        """
        获取指定角色目录下所有可用的人设文件

        Args:
            avatar_name: 角色名

        Returns:
            List[str]: 人设文件名列表（不包含.md后缀）
        """
        try:
            avatar_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name)
            
            if not os.path.exists(avatar_dir):
                return []
            
            available_files = []
            for file in os.listdir(avatar_dir):
                if file.endswith(".md") and file != "avatar.md":
                    # 移除.md后缀
                    available_files.append(file[:-3])
            
            available_files.sort()
            return available_files
            
        except Exception as e:
            logger.error(f"获取人设文件列表失败: {str(e)}")
            return []

    def _get_help_message(self) -> str:
        """获取帮助信息"""
        return """调试模式命令:
- /help: 显示此帮助信息
- /mem: 显示当前角色的记忆
- /mem refresh: 强制刷新缓存并显示当前角色的记忆
- /reset: 重置当前角色的最近记忆
- /clear: 清空当前角色的核心记忆
- /context: 清空当前角色的对话上下文
- /change <错误记忆> <正确记忆>: 修正核心记忆中的错误信息
- /diary: 生成角色小日记
- /state: 查看角色状态
- /letter: 角色给你写的信
- /list: 角色的备忘录
- /pyq: 角色的朋友圈
- /gift: 角色想送的礼物
- /shopping: 角色的购物清单
- /avatar: 查看所有可用人设
- /now: 查看当前人设
- /switch 人设名: 切换到指定人设
- /exit: 退出调试模式"""

    def _gen_core_mem(self, avatar_name: str, user_id: str) -> str:
        if not self.memory_service:
            return f"错误: 记忆服务未初始化"

        context = self.memory_service.get_recent_context(avatar_name, user_id)
        if self.memory_service.update_core_memory(avatar_name, user_id, context):
            return f"成功更新核心记忆"
        else:
            return f"未能成功更新核心记忆"

    def _show_memory(self, avatar_name: str, user_id: str, force_refresh: bool = False) -> str:
        """
        显示当前角色的记忆

        Args:
            avatar_name: 角色名
            user_id: 用户ID
            force_refresh: 是否强制刷新缓存

        Returns:
            str: 记忆内容
        """
        if not self.memory_service:
            return "错误: 记忆服务未初始化"

        try:
            # 如果需要强制刷新，清除数据库缓存
            if force_refresh and hasattr(self.memory_service, '_invalidate_cache'):
                self.memory_service._invalidate_cache(avatar_name, user_id)
                logger.info(f"已强制刷新用户 {user_id} 的数据库缓存")

            # 获取核心记忆
            core_memory = self.memory_service.get_core_memory(avatar_name, user_id)
            if not core_memory:
                core_memory_str = "当前角色没有核心记忆"
            else:
                core_memory_str = core_memory

            # 获取短期记忆（最近对话上下文）
            recent_context = self.memory_service.get_recent_context(avatar_name, user_id, 5)
            if not recent_context:
                short_memory_str = "当前角色没有短期记忆"
            else:
                # 格式化短期记忆
                short_memory_items = []
                for i in range(0, len(recent_context), 2):
                    if i + 1 < len(recent_context):
                        user_msg = recent_context[i].get('content', '')
                        bot_msg = recent_context[i + 1].get('content', '')
                        short_memory_items.append(f"用户: {user_msg}\n回复: {bot_msg}")

                short_memory_str = "\n\n".join(short_memory_items)

            # 如果是强制刷新模式，添加提示
            refresh_hint = "\n\n🔄 (已强制刷新缓存)" if force_refresh else ""
            
            return f"核心记忆:\n{core_memory_str}\n\n短期记忆:\n{short_memory_str}{refresh_hint}"

        except Exception as e:
            logger.error(f"获取记忆失败: {str(e)}")
            return f"获取记忆失败: {str(e)}"

    def _reset_short_memory(self, avatar_name: str, user_id: str) -> str:
        """
        重置当前角色的最近记忆

        Args:
            avatar_name: 角色名
            user_id: 用户ID

        Returns:
            str: 操作结果
        """
        if not self.memory_service:
            return "错误: 记忆服务未初始化"

        try:
            # 使用记忆管理器的统一接口重置短期记忆
            if self.memory_service.reset_short_memory(avatar_name, user_id):
                # 同时清除LLM服务中的对话上下文，避免重新加载旧记忆
                if self.llm_service:
                    self.llm_service.clear_history(user_id)
                    logger.info(f"已清除用户 {user_id} 的LLM对话上下文")
                
                # 同时清除数据库服务的缓存，确保短期记忆立即消失
                if hasattr(self.memory_service, '_invalidate_cache'):
                    self.memory_service._invalidate_cache(avatar_name, user_id)
                    logger.info(f"已清除用户 {user_id} 的数据库缓存")
                
                return f"已重置 {avatar_name} 的最近记忆"
            else:
                return f"重置 {avatar_name} 的最近记忆失败"
        except Exception as e:
            logger.error(f"重置最近记忆失败: {str(e)}")
            return f"重置最近记忆失败: {str(e)}"

    def _clear_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        清空当前角色的核心记忆

        Args:
            avatar_name: 角色名
            user_id: 用户ID

        Returns:
            str: 操作结果
        """
        if not self.memory_service:
            return "错误: 记忆服务未初始化"

        try:
            # 使用记忆管理器的统一接口清空核心记忆
            if self.memory_service.clear_core_memory(avatar_name, user_id):
                # 同时清除LLM服务中的对话上下文，避免重新加载旧记忆
                if self.llm_service:
                    self.llm_service.clear_history(user_id)
                    logger.info(f"已清除用户 {user_id} 的LLM对话上下文")
                return f"已清空 {avatar_name} 的核心记忆"
            else:
                return f"清空 {avatar_name} 的核心记忆失败"
        except Exception as e:
            logger.error(f"清空核心记忆失败: {str(e)}")
            return f"清空核心记忆失败: {str(e)}"

    def _clear_context(self, user_id: str) -> str:
        """
        清空当前角色的对话上下文

        Args:
            user_id: 用户ID

        Returns:
            str: 操作结果
        """
        if not self.llm_service:
            return "错误: LLM服务未初始化"

        try:
            self.llm_service.clear_history(user_id)
            
            # 同时清除数据库服务的缓存，确保记忆状态立即更新
            if self.memory_service and hasattr(self.memory_service, '_invalidate_cache'):
                # 获取当前角色名（如果需要的话）
                # 由于这个方法只接收user_id，我们需要从其他地方获取avatar_name
                # 这里我们可以清除所有相关的缓存
                if hasattr(self.memory_service, 'cache'):
                    # 清除所有与该用户相关的缓存
                    keys_to_remove = []
                    for key in self.memory_service.cache.keys():
                        if user_id in key:
                            keys_to_remove.append(key)
                    
                    for key in keys_to_remove:
                        del self.memory_service.cache[key]
                    
                    if keys_to_remove:
                        logger.info(f"已清除用户 {user_id} 的数据库缓存（{len(keys_to_remove)}个条目）")
            
            return "已清空对话上下文"
        except Exception as e:
            logger.error(f"清空对话上下文失败: {str(e)}")
            return f"清空对话上下文失败: {str(e)}"

    def _generate_content(self, content_type: str, avatar_name: str, user_id: str) -> str:
        """
        通用内容生成方法

        Args:
            content_type: 内容类型，如 'diary', 'state', 'letter'
            avatar_name: 角色名
            user_id: 用户ID

        Returns:
            str: 生成的内容
        """
        if not self.content_generator:
            return "错误: 内容生成服务未初始化"

        try:
            # 根据内容类型调用相应的方法
            content_type_methods = {
                'diary': self.content_generator.generate_diary,
                'state': self.content_generator.generate_state,
                'letter': self.content_generator.generate_letter,
                'list': self.content_generator.generate_list,
                'pyq': self.content_generator.generate_pyq,
                'gift': self.content_generator.generate_gift,
                'shopping': self.content_generator.generate_shopping
            }

            # 获取并使用相应的生成方法，或使用默认方法
            generate_method = content_type_methods.get(content_type)
            if not generate_method:
                return f"不支持的内容类型: {content_type}"

            content = generate_method(avatar_name, user_id)

            if not content or content.startswith("无法"):
                return content

            logger.info(f"已生成{avatar_name}的{content_type} 用户: {user_id}")
            return content

        except Exception as e:
            logger.error(f"生成{content_type}失败: {str(e)}")
            return f"{content_type}生成失败: {str(e)}"

    def _generate_content_async(self, content_type: str, avatar_name: str, user_id: str, chat_id: str, callback: Callable[[str, str, str], None]) -> str:
        """
        异步生成内容

        Args:
            content_type: 内容类型，如 'diary', 'state', 'letter'
            avatar_name: 角色名
            user_id: 用户ID
            chat_id: 聊天ID，用于回调发送消息
            callback: 回调函数，用于处理生成的内容

        Returns:
            str: 初始响应消息
        """
        if not self.content_generator:
            return "错误: 内容生成服务未初始化"

        # 创建异步线程执行内容生成
        def generate_thread():
            try:
                # 根据内容类型调用相应的方法
                content_type_methods = {
                    'diary': self.content_generator.generate_diary,
                    'state': self.content_generator.generate_state,
                    'letter': self.content_generator.generate_letter,
                    'list': self.content_generator.generate_list,
                    'pyq': self.content_generator.generate_pyq,
                    'gift': self.content_generator.generate_gift,
                    'shopping': self.content_generator.generate_shopping
                }

                # 获取并使用相应的生成方法，或使用默认方法
                generate_method = content_type_methods.get(content_type)
                if not generate_method:
                    result = f"不支持的内容类型: {content_type}"
                    callback(command=f"/{content_type}", reply=result, chat_id=chat_id)
                    return

                # 生成内容
                content = generate_method(avatar_name, user_id)

                if not content or content.startswith("无法"):
                    callback(command=f"/{content_type}", reply=content, chat_id=chat_id)
                    return

                logger.info(f"已生成{avatar_name}的{content_type} 用户: {user_id}")
                # 调用回调函数处理生成的内容
                callback(command=f"/{content_type}", reply=content, chat_id=chat_id)

            except Exception as e:
                error_msg = f"{content_type}生成失败: {str(e)}"
                logger.error(error_msg)
                callback(command=f"/{content_type}", reply=error_msg, chat_id=chat_id)

        # 启动异步线程
        thread = threading.Thread(target=generate_thread)
        thread.daemon = True  # 设置为守护线程，不会阻止程序退出
        thread.start()

        # 静默生成，不返回任何初始响应
        return ""

    def _generate_diary(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的日记"""
        return self._generate_content('diary', avatar_name, user_id)

    def _generate_state(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的状态信息"""
        return self._generate_content('state', avatar_name, user_id)

    def _generate_letter(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色给用户写的信"""
        return self._generate_content('letter', avatar_name, user_id)

    def _generate_list(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的备忘录"""
        return self._generate_content('list', avatar_name, user_id)

    def _generate_pyq(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的朋友圈"""
        return self._generate_content('pyq', avatar_name, user_id)

    def _generate_gift(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色想送的礼物"""
        return self._generate_content('gift', avatar_name, user_id)

    def _generate_shopping(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的购物清单"""
        return self._generate_content('shopping', avatar_name, user_id)

    def _show_all_avatars(self) -> str:
        """显示所有可用的人设"""
        try:
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")

            if not os.path.exists(avatars_dir):
                return "人设目录不存在"

            avatars = []
            for item in os.listdir(avatars_dir):
                item_path = os.path.join(avatars_dir, item)
                # 检查是否为目录且包含avatar.md文件
                if os.path.isdir(item_path):
                    avatar_file = os.path.join(item_path, "avatar.md")
                    if os.path.exists(avatar_file):
                        avatars.append(item)

            if not avatars:
                return "未找到任何可用的人设"

            avatars.sort()  # 按字母顺序排序
            avatar_list = "\n".join([f"- {avatar}" for avatar in avatars])
            return f"可用的人设列表:\n{avatar_list}\n\n使用 /switch 人设名 来切换人设"

        except Exception as e:
            logger.error(f"获取人设列表失败: {str(e)}")
            return f"获取人设列表失败: {str(e)}"

    def _show_current_avatar(self, current_avatar: str) -> str:
        """显示当前人设信息"""
        try:
            avatar_path = os.path.join(self.root_dir, "data", "avatars", current_avatar, "avatar.md")

            if not os.path.exists(avatar_path):
                return f"当前人设文件不存在: {current_avatar}"

            # 读取人设文件的基本信息
            with open(avatar_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取角色名称（从"# 角色"部分）
            role_info = ""
            lines = content.split('\n')
            in_role_section = False

            for line in lines:
                if line.strip().startswith('# 角色'):
                    in_role_section = True
                    continue
                elif line.strip().startswith('# ') and in_role_section:
                    break
                elif in_role_section and line.strip():
                    role_info += line.strip() + " "

            if role_info:
                role_info = role_info.strip()[:100] + "..." if len(role_info) > 100 else role_info.strip()
            else:
                role_info = "无角色描述"

            return f"当前人设: {current_avatar}\n角色信息: {role_info}"

        except Exception as e:
            logger.error(f"获取当前人设信息失败: {str(e)}")
            return f"获取当前人设信息失败: {str(e)}"

    def _switch_avatar(self, avatar_name: str) -> str:
        """切换到指定人设"""
        try:
            # 检查人设是否存在
            avatar_path = os.path.join(self.root_dir, "data", "avatars", avatar_name, "avatar.md")

            if not os.path.exists(avatar_path):
                return f"人设不存在: {avatar_name}\n使用 /avatar 查看可用人设"

            # 导入必要的模块和函数
            from src.main import switch_avatar

            # 执行人设切换
            switch_avatar(avatar_name)

            logger.info(f"通过调试命令切换人设到: {avatar_name}")
            return f"已成功切换到人设: {avatar_name}"

        except Exception as e:
            logger.error(f"切换人设失败: {str(e)}")
            return f"切换人设失败: {str(e)}"

    def _handle_memory_change(self, command: str, avatar_name: str, user_id: str) -> str:
        """处理记忆修正命令"""
        try:
            # 解析命令参数
            parts = command.split(' ', 2)
            if len(parts) < 3:
                return "用法：/change <错误记忆> <正确记忆>\n例如：/change \"用户喜欢叫我宝宝\" \"我喜欢叫LLM宝宝\""
            
            wrong_memory = parts[1].strip('"\'')
            correct_memory = parts[2].strip('"\'')
            
            if not wrong_memory or not correct_memory:
                return "错误记忆和正确记忆都不能为空"
            
            # 记录修正操作
            logger.info(f"用户 {user_id} 请求修正记忆：'{wrong_memory}' -> '{correct_memory}'")
            
            # 导入记忆管理器
            from modules.memory_manager import MemoryManager
            
            # 创建记忆管理器实例
            memory_manager = MemoryManager(
                root_dir=self.root_dir,
                api_key="dummy_key",  # 这里不会用到，只是创建实例
                base_url="dummy_url",
                model="dummy_model",
                max_token=1000,
                temperature=0.7
            )
            
            # 使用LLM智能修正记忆
            success = self._correct_memory_with_llm(
                memory_manager, avatar_name, user_id, wrong_memory, correct_memory
            )
            
            if success:
                return f"✅ 记忆修正成功！\n已将：{wrong_memory}\n修正为：{correct_memory}"
            else:
                return f"❌ 记忆修正失败，未找到匹配的记忆记录"
                
        except Exception as e:
            logger.error(f"处理 /change 命令失败: {str(e)}")
            return f"❌ 处理命令时发生错误：{str(e)}"

    def _correct_memory_with_llm(self, memory_manager, avatar_name: str, user_id: str, 
                               wrong_memory: str, correct_memory: str) -> bool:
        """
        使用LLM智能修正核心记忆
        
        Args:
            memory_manager: 记忆管理器实例
            avatar_name: 人设名称
            user_id: 用户ID
            wrong_memory: 错误的记忆描述
            correct_memory: 正确的记忆描述
            
        Returns:
            bool: 修正是否成功
        """
        try:
            # 导入LLM服务
            import sys
            import os
            # 确保项目根目录在Python路径中
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            if root_dir not in sys.path:
                sys.path.insert(0, root_dir)
            
            from src.services.ai.cached_llm_service import get_cached_llm_service
            llm_service = get_cached_llm_service()
            
            # 获取当前核心记忆
            current_memory = memory_manager.get_core_memory(avatar_name, user_id)
            if not current_memory:
                logger.warning(f"未找到用户 {user_id} 的核心记忆")
                return False
            
            # 备份现有记忆文件
            backup_success = self._backup_memory_files(memory_manager, avatar_name, user_id)
            if not backup_success:
                logger.warning(f"备份记忆文件失败，继续执行修正操作")
            
            # 读取记忆修正系统提示词
            system_prompt = self._load_memory_correction_system_prompt()
            
            # 构建记忆修正提示词
            prompt = self._build_memory_correction_prompt(
                avatar_name, user_id, current_memory, wrong_memory, correct_memory
            )
            
            # 调用LLM进行智能修正
            response = llm_service.get_response(
                message=prompt,
                system_prompt=system_prompt,
                user_id=f"memory_correction_{user_id}"
            )
            
            # 解析LLM响应并直接更新记忆
            return self._apply_llm_memory_correction(memory_manager, response, avatar_name, user_id)
            
        except Exception as e:
            logger.error(f"LLM记忆修正失败: {str(e)}")
            return False

    def _backup_memory_files(self, memory_manager, avatar_name: str, user_id: str) -> bool:
        """
        备份记忆文件到archived_memory文件夹
        
        Args:
            memory_manager: 记忆管理器实例
            avatar_name: 人设名称
            user_id: 用户ID
            
        Returns:
            bool: 备份是否成功
        """
        try:
            import os
            import shutil
            from datetime import datetime
            
            # 创建archived_memory文件夹
            archive_dir = os.path.join(self.root_dir, "archived_memory")
            os.makedirs(archive_dir, exist_ok=True)
            
            # 创建时间戳文件夹
            timestamp = datetime.now().strftime("%m%d_%H%M")
            backup_dir = os.path.join(archive_dir, f"{avatar_name}_{timestamp}")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份旧记忆系统文件
            old_memory_path = os.path.join(self.root_dir, "oldmemory", avatar_name, f"{user_id}.json")
            if os.path.exists(old_memory_path):
                shutil.copy2(old_memory_path, backup_dir)
                logger.info(f"已备份旧记忆文件: {old_memory_path}")
            
            # 备份新记忆系统数据库文件
            new_memory_db_path = os.path.join(self.root_dir, "memory", avatar_name, f"core_memory_{user_id}.db")
            if os.path.exists(new_memory_db_path):
                shutil.copy2(new_memory_db_path, backup_dir)
                logger.info(f"已备份数据库文件: {new_memory_db_path}")
            
            # 备份MD文件
            md_file_path = os.path.join(self.root_dir, "memory", avatar_name, f"{user_id}.md")
            if os.path.exists(md_file_path):
                shutil.copy2(md_file_path, backup_dir)
                logger.info(f"已备份MD文件: {md_file_path}")
            
            logger.info(f"记忆文件备份成功: {backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"备份记忆文件失败: {str(e)}")
            return False
    
    def _load_memory_correction_system_prompt(self) -> str:
        """
        加载记忆修正的系统提示词
        
        Returns:
            str: 系统提示词内容
        """
        try:
            prompt_path = os.path.join(self.root_dir, "data", "base", "memory_correction_prompt.md")
            if os.path.exists(prompt_path):
                with open(prompt_path, "r", encoding="utf-8") as f:
                    return f.read()
            else:
                # 如果文件不存在，使用默认提示词
                return """你是一个专业的记忆管理助手，负责修正和优化AI角色的核心记忆。
请根据用户的修正意图，智能地识别和修改核心记忆中的相关内容。
使用语义理解而不是精确字符串匹配来找到需要修正的记忆片段。
修正后的记忆要保持第一人称视角，使用"我"指代AI角色，"用户"指代对话用户。
不同记忆条目用分号"；"分隔，保持简洁精炼。"""
        except Exception as e:
            logger.error(f"加载记忆修正系统提示词失败: {str(e)}")
            return """你是一个记忆管理助手，负责修正和优化核心记忆。"""

    def _build_memory_correction_prompt(self, avatar_name: str, user_id: str, 
                                      current_memory: str, wrong_memory: str, correct_memory: str) -> str:
        """
        构建记忆修正的提示词
        
        Args:
            avatar_name: 人设名称
            user_id: 用户ID
            current_memory: 当前核心记忆内容
            wrong_memory: 错误的记忆描述
            correct_memory: 正确的记忆描述
            
        Returns:
            str: 完整的提示词
        """
        prompt = f"""请根据以下指示修正核心记忆：

## 当前核心记忆
{current_memory}

## 记忆修正任务
- 用户想要修正：{wrong_memory}
- 修正为：{correct_memory}

## 人设信息
- 当前人设：{avatar_name}
- 用户ID：{user_id}

## 修正要求
1. 使用语义理解，不要做精确字符串匹配
2. 智能识别用户想要修正的记忆片段
3. 只修正相关的部分，保持其他记忆不变
4. 确保修正后的记忆逻辑一致
5. 保持第一人称视角（"我"指AI角色，"用户"指对话用户）
6. 不同记忆条目用分号"；"分隔

## 输出格式
请以JSON格式输出修正结果：
{{
  "success": true,
  "corrected_memory": "修正后的完整核心记忆内容",
  "changes_made": [
    {{
      "original": "原始记忆片段",
      "corrected": "修正后的记忆片段",
      "reason": "修正原因"
    }}
  ],
  "confidence": 0.9,
  "explanation": "修正说明和理由"
}}

如果无法找到相关记忆，请输出：
{{
  "success": false,
  "error": "无法找到匹配的记忆片段",
  "suggestions": ["可能的相似记忆片段"],
  "confidence": 0.1
}}

请开始分析并修正记忆："""
        
        return prompt

    def _apply_llm_memory_correction(self, memory_manager, llm_response: str, avatar_name: str, user_id: str) -> bool:
        """
        应用LLM的记忆修正结果
        
        Args:
            memory_manager: 记忆管理器实例
            llm_response: LLM的响应
            avatar_name: 人设名称
            user_id: 用户ID
            
        Returns:
            bool: 应用是否成功
        """
        try:
            # 解析JSON响应
            import json
            result = json.loads(llm_response)
            
            # 检查是否成功
            if not result.get("success", False):
                error_msg = result.get("error", "未知错误")
                logger.warning(f"LLM记忆修正失败: {error_msg}")
                return False
            
            # 获取修正后的记忆
            corrected_memory = result.get("corrected_memory", "")
            confidence = result.get("confidence", 0.0)
            explanation = result.get("explanation", "")
            changes_made = result.get("changes_made", [])
            
            # 检查置信度
            if confidence < 0.5:
                logger.warning(f"LLM记忆修正置信度过低: {confidence}")
                return False
            
            # 记录修正详情
            logger.info(f"LLM记忆修正结果:")
            logger.info(f"置信度: {confidence}")
            logger.info(f"修正说明: {explanation}")
            for change in changes_made:
                logger.info(f"修正: {change.get('original', '')} -> {change.get('corrected', '')}")
            
            # 直接更新核心记忆
            success = memory_manager.update_core_memory_content(avatar_name, user_id, corrected_memory)
            
            if success:
                logger.info(f"LLM记忆修正成功完成")
                return True
            else:
                logger.error(f"更新核心记忆失败")
                return False
                
        except json.JSONDecodeError as e:
            logger.error(f"解析LLM响应JSON失败: {str(e)}")
            logger.error(f"LLM响应: {llm_response}")
            return False
        except Exception as e:
            logger.error(f"应用LLM记忆修正失败: {str(e)}")
            return False

    def _apply_memory_correction(self, memory_manager, llm_response: str, avatar_name: str, user_id: str, wrong_memory: str, correct_memory: str) -> bool:
        """
        应用LLM的记忆修正结果
        
        Args:
            memory_manager: 记忆管理器实例
            llm_response: LLM的响应
            avatar_name: 人设名称
            user_id: 用户ID
            wrong_memory: 错误的记忆描述
            correct_memory: 正确的记忆描述
            
        Returns:
            bool: 应用是否成功
        """
        try:
            # 尝试解析JSON响应
            import json
            result = json.loads(llm_response)
            
            corrected_memory = result.get("corrected_memory", "")
            confidence = result.get("confidence", 0.0)
            explanation = result.get("explanation", "")
            
            if confidence < 0.5:
                logger.warning(f"LLM记忆修正置信度过低: {confidence}")
                return False
            
            # 记录LLM的分析结果
            logger.info(f"用户 {user_id} 的记忆分析结果:")
            logger.info(f"LLM修正内容: {corrected_memory}")
            logger.info(f"置信度: {confidence}")
            logger.info(f"修正说明: {explanation}")
            
            # 使用记忆管理器的简单修正方法
            success = memory_manager.correct_memory(avatar_name, user_id, wrong_memory, correct_memory)
            
            if success:
                logger.info(f"记忆修正成功: {wrong_memory} -> {correct_memory}")
                return True
            else:
                logger.error(f"记忆管理器修正失败")
                return False
            
        except json.JSONDecodeError:
            logger.error(f"LLM响应格式错误，无法解析JSON: {llm_response}")
            return False
        except Exception as e:
            logger.error(f"应用记忆修正失败: {str(e)}")
            return False

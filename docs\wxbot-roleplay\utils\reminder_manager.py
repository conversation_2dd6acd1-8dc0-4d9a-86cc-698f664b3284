#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时提醒管理器
实现用户定时提醒功能，支持私聊和群聊
"""

import re
import os
import threading
from datetime import datetime, timedelta
from typing import Optional, Tuple, List
from sqlalchemy.orm import sessionmaker
from database import Session, UserReminder
from utils.logger import log
import config


class ReminderManager:
    """定时提醒管理器"""
    
    def __init__(self, bot=None):
        self.bot = bot
        self.scheduler = None
        
    def set_scheduler(self, scheduler):
        """设置调度器"""
        self.scheduler = scheduler
        
    def is_reminder_request(self, content: str) -> bool:
        """检查消息是否为定时提醒请求"""
        if not content:
            return False
            
        # 提醒关键词
        reminder_keywords = [
            '提醒', '叫我', '喊我', '叫醒我', '通知', '闹钟', '记得',
            '分钟后', '小时后', '点叫我', '点提醒', '点喊我', '点叫醒我',
            '催我', '点催我'
        ]

        # 时间关键词
        time_keywords = [
            '分钟', '小时', '点', '半', '刻', '明天', '后天', '今天'
        ]
        
        # 检查是否包含提醒关键词和时间信息
        has_reminder_keyword = any(keyword in content for keyword in reminder_keywords)
        has_time_info = any(keyword in content for keyword in time_keywords) or any(char.isdigit() for char in content)
        
        return has_reminder_keyword and has_time_info
    
    def parse_time_from_message(self, content: str, current_time: Optional[datetime] = None) -> Optional[datetime]:
        """从消息中解析时间"""
        try:
            now = current_time if current_time else datetime.now()
            
            # 匹配相对时间：X分钟后、X小时后
            relative_patterns = [
                r'(\d+)\s*分钟后',
                r'(\d+)\s*小时后',
                r'(\d+)\s*分钟后叫我',
                r'(\d+)\s*小时后叫我',
                r'(\d+)\s*分钟后提醒',
                r'(\d+)\s*小时后提醒',
            ]
            
            for pattern in relative_patterns:
                match = re.search(pattern, content)
                if match:
                    number = int(match.group(1))
                    if '小时' in pattern:
                        return now + timedelta(hours=number)
                    else:  # 分钟
                        return now + timedelta(minutes=number)
            
            # 匹配绝对时间：X点、X点X分、X:X
            # 注意：更具体的模式要放在前面，避免被通用模式先匹配
            absolute_patterns = [
                (r'(\d{1,2})\s*点\s*(\d{1,2})\s*分', 'hour_minute'),  # X点X分
                (r'(\d{1,2})\s*点(\d{1,2})', 'hour_minute_no_space'),  # X点X（无空格无"分"字）
                (r'(\d{1,2})\s*点半', 'hour_half'),  # X点半
                (r'(\d{1,2}):(\d{1,2})', 'hour_colon_minute'),  # X:X
                (r'(\d{1,2})\s*点', 'hour_only'),  # X点（最后匹配）
            ]

            # 检查是否包含明天、后天等日期词汇
            day_offset = 0
            if '明天' in content:
                day_offset = 1
            elif '后天' in content:
                day_offset = 2

            for pattern, pattern_type in absolute_patterns:
                match = re.search(pattern, content)
                if match:
                    if pattern_type == 'hour_minute':  # X点X分
                        hour = int(match.group(1))
                        minute = int(match.group(2))
                    elif pattern_type == 'hour_minute_no_space':  # X点X（如7点05）
                        hour = int(match.group(1))
                        minute = int(match.group(2))
                    elif pattern_type == 'hour_half':  # X点半
                        hour = int(match.group(1))
                        minute = 30
                    elif pattern_type == 'hour_colon_minute':  # X:X
                        hour = int(match.group(1))
                        minute = int(match.group(2))
                    elif pattern_type == 'hour_only':  # X点
                        hour = int(match.group(1))
                        minute = 0

                    # 智能判断时间：如果没有明确指定日期，需要判断是今天还是明天
                    if day_offset == 0:
                        # 构建今天的目标时间
                        target_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)

                        # 智能判断逻辑
                        current_hour = now.hour
                        current_minute = now.minute

                        # 如果用户输入的小时数小于12，可能是12小时制
                        if hour <= 12:
                            # 情况1：用户在晚上早期(18-21点)说"9点"等，很可能指当天晚上
                            if 18 <= current_hour <= 21 and hour <= 11:
                                # 检查是否还有时间（考虑分钟）
                                evening_hour = hour + 12
                                if evening_hour > current_hour or (evening_hour == current_hour and minute > current_minute):
                                    # 转换为24小时制的晚上时间
                                    target_time = target_time.replace(hour=evening_hour)
                                # 如果晚上时间已过，保持原hour（明天早上）
                            # 情况2：用户在很晚(22-23点)说时间，需要特殊判断
                            elif current_hour >= 22 and hour <= 11:
                                # 检查是否是当天晚上还能实现的时间
                                evening_hour = hour + 12
                                if evening_hour > current_hour or (evening_hour == current_hour and minute > current_minute):
                                    # 如果晚上时间还没过，就是当天晚上
                                    target_time = target_time.replace(hour=evening_hour)
                                else:
                                    # 如果晚上时间已过，就是明天早上
                                    pass  # 保持原来的hour
                            # 情况3：用户在上午说"9点"，可能指当天上午
                            elif current_hour < 12 and hour > current_hour:
                                # 如果指定时间比当前时间晚，就是今天上午
                                pass  # 保持原来的hour
                            # 情况4：用户在下午早期(12-17点)说"9点"，可能指晚上
                            elif 12 <= current_hour < 18 and hour <= 11:
                                target_time = target_time.replace(hour=hour + 12)

                        # 如果计算出的时间已经过了，设置为明天
                        if target_time <= now:
                            target_time += timedelta(days=1)
                    else:
                        # 有明确日期偏移，直接构建目标时间
                        target_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                        target_time += timedelta(days=day_offset)

                    return target_time

            # 处理只有日期没有具体时间的情况（如"明天叫我"）
            if day_offset > 0:
                # 默认设置为上午9点
                target_time = now.replace(hour=9, minute=0, second=0, microsecond=0)
                target_time += timedelta(days=day_offset)
                return target_time
            
            return None
            
        except Exception as e:
            log(f"解析时间失败: {str(e)}", "ERROR")
            return None
    
    def create_reminder(self, user_id: str, chat_id: str, is_group: bool, 
                       original_message: str, event_time: datetime) -> Optional[int]:
        """创建定时提醒"""
        try:
            session = Session()
            
            # 创建提醒记录
            reminder = UserReminder(
                user_id=user_id,
                chat_id=chat_id,
                is_group=is_group,
                original_message=original_message,
                event_time=event_time,
                reminder_time=event_time,  # 直接在指定时间提醒
                is_triggered=False
            )
            
            session.add(reminder)
            session.commit()
            
            reminder_id = reminder.id
            session.close()
            
            # 安排定时任务
            if self.scheduler:
                self.scheduler.add_job(
                    func=self.trigger_reminder,
                    trigger='date',
                    run_date=event_time,
                    args=[reminder_id],
                    id=f"reminder_{reminder_id}",
                    name=f"用户提醒_{user_id}_{event_time.strftime('%H:%M')}",
                    replace_existing=True
                )
                
                log(f"定时提醒已创建: 用户{user_id} 时间{event_time.strftime('%Y-%m-%d %H:%M:%S')}")
                return reminder_id
            else:
                log("调度器未设置，无法创建定时任务", "ERROR")
                return None
                
        except Exception as e:
            log(f"创建定时提醒失败: {str(e)}", "ERROR")
            return None
    
    def trigger_reminder(self, reminder_id: int):
        """触发定时提醒"""
        try:
            session = Session()
            reminder = session.query(UserReminder).get(reminder_id)

            if not reminder or reminder.is_triggered:
                session.close()
                return

            # 创建一个独立的reminder对象，避免会话问题
            reminder_data = {
                'id': reminder.id,
                'user_id': reminder.user_id,
                'chat_id': reminder.chat_id,
                'is_group': reminder.is_group,
                'original_message': reminder.original_message
            }

            # 生成AI提醒消息
            ai_message = self.generate_reminder_message(reminder)

            # 标记为已触发
            reminder.is_triggered = True
            session.commit()
            session.close()

            # 创建临时reminder对象用于发送消息
            temp_reminder = type('TempReminder', (), reminder_data)()

            # 发送提醒消息
            self.send_reminder_message(temp_reminder, ai_message)

            log(f"定时提醒已触发: 用户{reminder_data['user_id']}")

        except Exception as e:
            log(f"触发定时提醒失败: {str(e)}", "ERROR")
    
    def generate_reminder_message(self, reminder: UserReminder) -> str:
        """生成AI提醒消息"""
        try:
            if not self.bot or not self.bot.api_client:
                return f"⏰ 时间到了！{reminder.original_message}"

            # 获取用户名（去掉可能的群聊后缀）
            sender_name = reminder.user_id
            if sender_name.endswith("@chatroom"):
                sender_name = sender_name.replace("@chatroom", "")

            # 获取当前时间
            now = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")

            # 获取天气信息（如果启用）
            weather_prompt = ""
            if hasattr(self.bot, 'weather_manager'):
                try:
                    # 使用配置的默认城市获取天气
                    default_city = getattr(config, 'ONLY_WEATHER_CITY', '徐州')
                    weather_info = self.bot.weather_manager.get_cached_weather_for_prompt(default_city)
                    if weather_info:
                        weather_prompt = f"\n{weather_info}"
                except Exception as e:
                    log(f"获取天气信息失败: {str(e)}", "DEBUG")

            # 获取日程信息（如果启用）
            schedule_prompt = ""
            if hasattr(self.bot, 'schedule_manager') and getattr(config, 'ENABLE_DAILY_SCHEDULE', False):
                try:
                    schedule_info = self.bot.schedule_manager.get_today_schedule()
                    if schedule_info:
                        schedule_prompt = f"\n[今日日程: {schedule_info}]"
                except:
                    pass

            # 构建完整的AI提示（使用与正常对话相同的格式）
            formatted_prompt = f"[本次对话的用户叫: {sender_name}]\n[当前系统时间: {now}]{weather_prompt}{schedule_prompt}[本次对话的用户{sender_name}之前对你说：{reminder.original_message}]\n\n现在是提醒时间，请生成一个温馨的提醒消息。要求：\n1. 语气要亲切自然，符合角色设定\n2. 不要包含[+好感+数字]等标签\n3. 简洁明了，可以分多句\n4. 可以适当加入表情符号"

            # 调用AI生成回复
            response = self.bot.api_client.chat.completions.create(
                model=config.MODEL,
                messages=[
                    {"role": "system", "content": self.bot.prompt_content},
                    {"role": "user", "content": formatted_prompt}
                ],
                temperature=config.TEMPERATURE,
                max_tokens=200
            )

            if response.choices and len(response.choices) > 0:
                ai_message = response.choices[0].message.content.strip()
                return ai_message
            else:
                return f"⏰ 时间到了！{reminder.original_message}"

        except Exception as e:
            log(f"生成AI提醒消息失败: {str(e)}", "ERROR")
            return f"⏰ 时间到了！{reminder.original_message}"
    
    def send_reminder_message(self, reminder: UserReminder, ai_message: str):
        """发送提醒消息"""
        try:
            if not self.bot or not self.bot.wx:
                log("机器人或微信客户端未初始化", "ERROR")
                return

            # 构建最终消息
            final_message = f"⏰ {ai_message}"

            # 使用机器人的消息处理逻辑来分割句子发送
            self._send_message_with_processing(final_message, reminder)

        except Exception as e:
            log(f"发送提醒消息失败: {str(e)}", "ERROR")

    def _send_message_with_processing(self, message: str, reminder: UserReminder):
        """使用机器人的消息处理逻辑发送消息"""
        try:
            # 导入文本处理函数
            from utils.text_processor import (
                remove_brackets_content,
                format_response,
                split_sentences,
                extract_image_tags,
                has_think_tags,
                remove_think_tags,
                apply_message_delay
            )

            # 处理思考标签
            if has_think_tags(message):
                message = remove_think_tags(message)

            # 检查是否包含图片标签
            clean_text, images = extract_image_tags(message)

            # 如果有图片，先发送图片
            if images:
                for image_path in images:
                    if os.path.exists(image_path):
                        try:
                            # 这里需要创建chat对象，暂时跳过图片发送
                            log(f"提醒消息包含图片，但暂不支持: {image_path}")
                        except Exception as e:
                            log(f"提醒图片发送失败: {str(e)}", "ERROR")

            # 处理文本回复
            if clean_text:
                # 移除括号内容
                cleaned_reply = remove_brackets_content(clean_text)

                # 格式化回复（添加分隔符）
                formatted_reply = format_response(cleaned_reply)

                # 分割句子
                sentences = split_sentences(formatted_reply)

                # 发送每个句子
                for i, sentence in enumerate(sentences):
                    if sentence.strip():
                        # 使用统一的延迟函数
                        apply_message_delay(i, len(sentences))

                        # 根据是否为群聊选择发送方式
                        if reminder.is_group:
                            # 群聊中为第一条消息@用户
                            if i == 0:
                                result = self.bot.wx.SendMsg(msg=sentence, who=reminder.chat_id, at=reminder.user_id)
                            else:
                                result = self.bot.wx.SendMsg(msg=sentence, who=reminder.chat_id)
                        else:
                            # 私聊直接发送
                            result = self.bot.wx.SendMsg(msg=sentence, who=reminder.chat_id)

                        if result:
                            log(f"提醒句子发送成功: {sentence[:30]}...")
                        else:
                            log(f"提醒句子发送失败: {sentence[:30]}...", "ERROR")

                # 记录发送完成
                if reminder.is_group:
                    log(f"群聊提醒已发送完成: {reminder.chat_id} @{reminder.user_id}")
                else:
                    log(f"私聊提醒已发送完成: {reminder.chat_id}")

        except Exception as e:
            log(f"处理提醒消息发送失败: {str(e)}", "ERROR")
            # 发送原始消息作为备用
            try:
                # 移除括号内容
                from utils.text_processor import remove_brackets_content
                cleaned_backup_message = remove_brackets_content(message)
                if reminder.is_group:
                    self.bot.wx.SendMsg(msg=cleaned_backup_message, who=reminder.chat_id, at=reminder.user_id)
                else:
                    self.bot.wx.SendMsg(msg=cleaned_backup_message, who=reminder.chat_id)
            except Exception as backup_e:
                log(f"备用提醒发送也失败: {str(backup_e)}", "ERROR")
    
    def handle_reminder_request(self, content: str, user_id: str, chat_id: str,
                               is_group: bool, chat=None) -> Optional[str]:
        """处理定时提醒请求"""
        try:
            # 解析时间
            event_time = self.parse_time_from_message(content)
            if not event_time:
                return "抱歉，我没有理解你想要设置的时间，可以说得更具体一些吗？比如'15分钟后叫我'或'下午3点提醒我'"
            
            # 检查时间是否已过
            if event_time <= datetime.now():
                return "这个时间已经过了呢，请设置一个未来的时间～"
            
            # 创建提醒
            reminder_id = self.create_reminder(user_id, chat_id, is_group, content, event_time)
            if not reminder_id:
                return "提醒设置失败，请稍后再试"
            
            # 生成确认消息
            time_str = event_time.strftime('%H:%M')
            if event_time.date() > datetime.now().date():
                time_str = event_time.strftime('%m月%d日 %H:%M')
            
            return f"好的！我会在{time_str}提醒你的～"
            
        except Exception as e:
            log(f"处理定时提醒请求失败: {str(e)}", "ERROR")
            return "提醒设置失败，请稍后再试"
    
    def get_user_reminders(self, user_id: str, chat_id: str = None) -> List[UserReminder]:
        """获取用户的提醒列表"""
        try:
            session = Session()
            query = session.query(UserReminder).filter(
                UserReminder.user_id == user_id,
                UserReminder.is_triggered == False,
                UserReminder.reminder_time > datetime.now()
            )
            
            if chat_id:
                query = query.filter(UserReminder.chat_id == chat_id)
            
            reminders = query.order_by(UserReminder.reminder_time).all()
            session.close()
            return reminders
            
        except Exception as e:
            log(f"获取用户提醒列表失败: {str(e)}", "ERROR")
            return []
    
    def cancel_reminder(self, reminder_id: int) -> bool:
        """取消定时提醒"""
        try:
            session = Session()
            reminder = session.query(UserReminder).get(reminder_id)
            
            if reminder and not reminder.is_triggered:
                # 从调度器中移除任务
                if self.scheduler:
                    try:
                        self.scheduler.remove_job(f"reminder_{reminder_id}")
                    except:
                        pass  # 任务可能已经不存在
                
                # 标记为已触发（实际上是取消）
                reminder.is_triggered = True
                session.commit()
                session.close()
                
                log(f"定时提醒已取消: {reminder_id}")
                return True
            
            session.close()
            return False
            
        except Exception as e:
            log(f"取消定时提醒失败: {str(e)}", "ERROR")
            return False
    
    def cleanup_old_reminders(self, days: int = 7):
        """清理旧的提醒记录"""
        try:
            session = Session()
            cutoff_time = datetime.now() - timedelta(days=days)

            # 删除旧的已触发提醒
            deleted_count = session.query(UserReminder).filter(
                UserReminder.is_triggered == True,
                UserReminder.created_at < cutoff_time
            ).delete()

            session.commit()
            session.close()

            if deleted_count > 0:
                log(f"已清理 {deleted_count} 条旧提醒记录")

        except Exception as e:
            log(f"清理旧提醒记录失败: {str(e)}", "ERROR")

    def recover_reminders_on_startup(self):
        """程序启动时恢复未触发的提醒任务"""
        try:
            if not self.scheduler:
                log("调度器未设置，无法恢复提醒任务", "ERROR")
                return 0

            session = Session()
            now = datetime.now()

            # 查找所有未触发且时间未过的提醒
            active_reminders = session.query(UserReminder).filter(
                UserReminder.is_triggered == False,
                UserReminder.reminder_time > now
            ).all()

            recovered_count = 0
            expired_count = 0

            for reminder in active_reminders:
                try:
                    # 重新安排定时任务
                    self.scheduler.add_job(
                        func=self.trigger_reminder,
                        trigger='date',
                        run_date=reminder.reminder_time,
                        args=[reminder.id],
                        id=f"reminder_{reminder.id}",
                        name=f"恢复提醒_{reminder.user_id}_{reminder.reminder_time.strftime('%H:%M')}",
                        replace_existing=True
                    )
                    recovered_count += 1
                    log(f"恢复提醒任务: ID{reminder.id} 用户{reminder.user_id} 时间{reminder.reminder_time.strftime('%Y-%m-%d %H:%M')}")

                except Exception as e:
                    log(f"恢复提醒任务失败 ID{reminder.id}: {str(e)}", "ERROR")

            # 标记已过期的提醒为已触发
            expired_reminders = session.query(UserReminder).filter(
                UserReminder.is_triggered == False,
                UserReminder.reminder_time <= now
            )

            expired_count = expired_reminders.count()
            if expired_count > 0:
                expired_reminders.update({UserReminder.is_triggered: True})
                session.commit()
                log(f"标记 {expired_count} 个过期提醒为已触发")

            session.close()

            log(f"提醒恢复完成: 恢复 {recovered_count} 个任务, 过期 {expired_count} 个任务")
            return recovered_count

        except Exception as e:
            log(f"恢复提醒任务失败: {str(e)}", "ERROR")
            return 0
    
    def get_reminder_stats(self) -> dict:
        """获取提醒统计信息"""
        try:
            session = Session()
            
            # 统计各种状态的提醒
            total_reminders = session.query(UserReminder).count()
            active_reminders = session.query(UserReminder).filter(
                UserReminder.is_triggered == False,
                UserReminder.reminder_time > datetime.now()
            ).count()
            triggered_reminders = session.query(UserReminder).filter(
                UserReminder.is_triggered == True
            ).count()
            
            session.close()
            
            return {
                'total_reminders': total_reminders,
                'active_reminders': active_reminders,
                'triggered_reminders': triggered_reminders
            }
            
        except Exception as e:
            log(f"获取提醒统计失败: {str(e)}", "ERROR")
            return {
                'total_reminders': 0,
                'active_reminders': 0,
                'triggered_reminders': 0
            }

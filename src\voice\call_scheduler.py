"""call_scheduler.py
调用 apscheduler，在检测到用户具有"提醒打电话/叫醒"关键词时，按规则定时使用 wxauto 进行微信语音通话。
"""
import re
import logging
from datetime import datetime, timedelta, time as dtime
from typing import Optional
import threading
from importlib import import_module
import sys, os
import subprocess

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.base import JobLookupError

logger = logging.getLogger('main')

# 统一的后台调度器实例（避免重复启动多个调度线程）
_scheduler: Optional[BackgroundScheduler] = None

def _get_scheduler() -> BackgroundScheduler:
    global _scheduler
    if _scheduler is None:
        _scheduler = BackgroundScheduler()
        _scheduler.start()
        logger.info("CallScheduler 调度器已启动")
    return _scheduler

class CallScheduler:
    """负责从用户消息中解析呼叫请求并按时拨打微信语音电话。"""

    # 关键词正则
    _regex_relative_sec = re.compile(r"(\d+)\s*秒(?:钟)?后.*?(?:给我)?打.*?电话")
    _regex_relative_min = re.compile(r"(\d+)\s*分(?:钟)?后.*?(?:给我)?打.*?电话")

    # 含模糊时间段的关键词
    _patterns = [
        (re.compile(r"明天早上|明早"), "tomorrow_morning"),
        (re.compile(r"明天晚上|明晚"), "tomorrow_evening"),
        (re.compile(r"今晚|今天晚上"), "today_evening"),
        (re.compile(r"今天下午"), "today_afternoon"),
    ]

    def __init__(self, wx_instance):
        self.scheduler = _get_scheduler()
        self.wx = wx_instance

    # ----------------- 公共接口 -----------------
    def handle_message(self, content: str, contact_name: str) -> bool:
        """检测消息，若匹配到呼叫请求则创建调度任务并返回 True。否则返回 False。"""
        content = content.strip()
        # 先检查相对秒
        m = self._regex_relative_sec.search(content)
        if m:
            secs = int(m.group(1))
            run_time = datetime.now() + timedelta(seconds=secs)
            self._schedule_call(run_time, contact_name)
            logger.info(f"已为 {contact_name} 创建延时 {secs} 秒的语音呼叫任务 → {run_time}")
            return True

        # 再检查相对分钟
        m = self._regex_relative_min.search(content)
        if m:
            mins = int(m.group(1))
            run_time = datetime.now() + timedelta(minutes=mins)
            self._schedule_call(run_time, contact_name)
            logger.info(f"已为 {contact_name} 创建延时 {mins} 分钟的语音呼叫任务 → {run_time}")
            return True

        # 检查模糊时间段
        for pattern, tag in self._patterns:
            if pattern.search(content):
                run_time = self._resolve_time(tag)
                if run_time:
                    self._schedule_call(run_time, contact_name)
                    logger.info(f"关键词 '{tag}' 命中，为 {contact_name} 创建呼叫任务 → {run_time}")
                    return True
        return False

    # ----------------- 内部工具 -----------------
    def _schedule_call(self, run_time: datetime, contact_name: str):
        """向 apscheduler 添加一次性任务。job_id 使用时间+名称确保唯一。"""
        job_id = f"call_{contact_name}_{run_time.timestamp()}"
        try:
            self.scheduler.add_job(
                func=self._make_call,
                trigger="date",
                run_date=run_time,
                args=[contact_name],
                id=job_id,
                misfire_grace_time=60,  # 1 分钟容错
            )
        except Exception:
            # 若已存在同名任务，先删除再添加
            try:
                self.scheduler.remove_job(job_id)
            except JobLookupError:
                pass
            self.scheduler.add_job(
                func=self._make_call,
                trigger="date",
                run_date=run_time,
                args=[contact_name],
                id=job_id,
                misfire_grace_time=60,
            )

    def _make_call(self, contact_name: str):
        """在调度触发时真正拨打微信语音电话。"""
        logger.info(f"正在尝试给 {contact_name} 拨打微信语音电话……")
        try:
            # 防御性检查：确保VoiceCall方法存在
            if not hasattr(self.wx, 'VoiceCall'):
                logger.error(f"WeChat实例缺少VoiceCall方法，尝试多种恢复策略...")

                # 策略1: 重新导入wxauto模块
                try:
                    import importlib
                    import wxauto
                    importlib.reload(wxauto)
                    from wxauto import WeChat
                    self.wx = WeChat()
                    logger.info("策略1成功: 重新加载wxauto模块并创建实例")
                except Exception as reload_err:
                    logger.error(f"策略1失败: {reload_err}")

                    # 策略2: 强制重新导入
                    try:
                        import sys
                        if 'wxauto' in sys.modules:
                            del sys.modules['wxauto']
                        if 'wxauto.wxauto' in sys.modules:
                            del sys.modules['wxauto.wxauto']

                        from wxauto import WeChat
                        self.wx = WeChat()
                        logger.info("策略2成功: 强制重新导入wxauto模块")
                    except Exception as force_err:
                        logger.error(f"策略2失败: {force_err}")

                        # 策略3: 使用全局WX实例
                        try:
                            from main import WX
                            if WX and hasattr(WX, 'VoiceCall'):
                                self.wx = WX
                                logger.info("策略3成功: 使用全局WX实例")
                            else:
                                logger.error("策略3失败: 全局WX实例也缺少VoiceCall方法")
                                return
                        except Exception as global_err:
                            logger.error(f"策略3失败: {global_err}")
                            return

            # 再次检查VoiceCall方法
            if not hasattr(self.wx, 'VoiceCall'):
                logger.error(f"重新初始化后仍然缺少VoiceCall方法，无法拨打电话")
                # 详细诊断信息
                import wxauto
                logger.error(f"wxauto模块路径: {wxauto.__file__}")
                logger.error(f"wxauto版本: {getattr(wxauto, '__version__', '未知')}")
                logger.error(f"WeChat类: {wxauto.WeChat}")
                logger.error(f"WeChat类中的VoiceCall: {hasattr(wxauto.WeChat, 'VoiceCall')}")

                # 检查类的所有方法
                methods = [m for m in dir(wxauto.WeChat) if not m.startswith('_')]
                voice_methods = [m for m in methods if 'voice' in m.lower() or 'call' in m.lower()]
                logger.error(f"WeChat类中包含voice/call的方法: {voice_methods}")

                # 尝试直接从类创建实例
                try:
                    test_wx = wxauto.WeChat()
                    logger.error(f"直接创建的实例VoiceCall存在: {hasattr(test_wx, 'VoiceCall')}")
                    if hasattr(test_wx, 'VoiceCall'):
                        logger.error(f"VoiceCall方法: {test_wx.VoiceCall}")
                except Exception as test_err:
                    logger.error(f"直接创建WeChat实例失败: {test_err}")

                return

            # 检查当前聊天对象
            try:
                current_chat = self.wx.CurrentChat()
                if current_chat != contact_name:
                    logger.info(f"切换聊天对象从 '{current_chat}' 到 '{contact_name}'")
                    self.wx.ChatWith(contact_name)
            except Exception as chat_err:
                logger.warning(f"检查/切换聊天对象时出错: {chat_err}")
                # 继续尝试拨打电话

            # 在发起通话前设置基线窗口
            try:
                from .call_session import CallSession  # 延迟导入，避免循环依赖
                call_session = CallSession()
                # 设置基线微信窗口（在发起通话前）
                call_session._window_detector.start_call_detection()
                logger.info("已设置通话检测基线")
            except Exception as e:
                logger.error(f"创建 CallSession 失败: {e}")
                return

            # 执行语音通话
            ok = self.wx.VoiceCall(contact_name)
            if ok:
                logger.info(f"已向 {contact_name} 发起语音通话")

                # 启动通话窗口检测和会话管理
                try:
                    # 启动检测线程，等待新微信窗口出现
                    def start_call_session_when_ready():
                        """等待通话窗口出现后启动CallSession"""
                        import time
                        max_wait_time = 30  # 最多等待30秒
                        check_interval = 1  # 每秒检查一次

                        for i in range(max_wait_time):
                            # 检测新的微信窗口
                            new_window = call_session._window_detector.detect_new_wechat_window()
                            if new_window:
                                hwnd, title = new_window
                                logger.info(f"检测到通话窗口，启动CallSession: {title} (ID: {hwnd})")
                                # 启动ASR、LLM、TTS会话
                                call_session.start()
                                return
                            time.sleep(check_interval)

                        logger.warning("等待通话窗口超时，未启动CallSession")
                        call_session._window_detector.stop_call_detection()

                    # 在后台线程中等待通话窗口出现
                    threading.Thread(
                        target=start_call_session_when_ready,
                        name="CallWindowDetector",
                        daemon=True
                    ).start()

                    logger.info("通话窗口检测线程已启动")

                except Exception as e:
                    logger.error(f"启动通话检测失败: {e}")
                    call_session._window_detector.stop_call_detection()
            else:
                logger.warning(f"未成功向 {contact_name} 发起语音通话")
                call_session._window_detector.stop_call_detection()
        except Exception as err:
            logger.error(f"拨打 {contact_name} 语音电话时出错: {err}")
            # 添加详细的调试信息
            logger.error(f"WeChat实例类型: {type(self.wx)}")
            logger.error(f"WeChat实例ID: {id(self.wx)}")
            logger.error(f"VoiceCall方法存在: {hasattr(self.wx, 'VoiceCall')}")
            if hasattr(self.wx, 'VoiceCall'):
                logger.error(f"VoiceCall方法类型: {type(self.wx.VoiceCall)}")
            # 列出所有可用方法
            methods = [m for m in dir(self.wx) if not m.startswith('_') and 'call' in m.lower()]
            logger.error(f"包含'call'的方法: {methods}")

    @staticmethod
    def _resolve_time(tag: str) -> Optional[datetime]:
        """根据关键词标签计算目标时间点。"""
        now = datetime.now()
        if tag == "tomorrow_morning":
            target_date = now.date() + timedelta(days=1)
            target_time = dtime(hour=7, minute=0)
        elif tag == "tomorrow_evening":
            target_date = now.date() + timedelta(days=1)
            target_time = dtime(hour=20, minute=0)
        elif tag == "today_evening":
            target_date = now.date()
            target_time = dtime(hour=20, minute=0)
            if datetime.combine(target_date, target_time) <= now:
                # 如果已过晚上八点，则改到明晚
                target_date += timedelta(days=1)
        elif tag == "today_afternoon":
            target_date = now.date()
            target_time = dtime(hour=13, minute=30)
            if datetime.combine(target_date, target_time) <= now:
                target_date += timedelta(days=1)
        else:
            return None
        return datetime.combine(target_date, target_time) 
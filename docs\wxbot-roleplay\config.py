# -*- coding: utf-8 -*-
"""
微信角色扮演机器人配置文件
"""

# ==================== 基础配置 ====================
# 用户列表(请配置要和bot说话的账号的昵称或者群名，不要写备注！)
# 例如：LISTEN_LIST = ['用户1','用户2','群名']
LISTEN_LIST = ['用户1']

# 群组列表
GROUP_LIST = ['智械危机']

# 管理员列表
ADMIN_LIST = ['管理员']

# 机器人的微信名称
ROBOT_WX_NAME = '机器人'

# ==================== AI API 配置 ====================
API_KEY = 'key-'
BASE_URL = 'https://api.openai.com/v1/chat/completions'
MODEL = 'deepseek-chat'

# 回复最大token
MAX_TOKEN = 1000
# 温度参数 (优化后提高生动性)
TEMPERATURE = 1.1

# ==================== 角色设定 ====================
# 角色提示词文件名（存放在prompts目录下）
PROMPT_NAME = '试做型-林小葵.md'

# 好感度提示词文件名
FAVORABILITY_PROMPT_NAME = '好感度提示词.md'

# 防御提示词文件名
DEFENSE_PROMPT_NAME = '防御提示词.md'

# 特殊提示词文件名
SPECIAL_PROMPT_NAME = '林小葵-特殊.md'

# ==================== 功能开关 ====================
# 是否启用群聊功能
GROUP_SWITCH = True

# 群聊是否仅回复@消息
GROUP_REPLY_AT_ONLY = True

# ==================== 群聊智能参与配置 ====================
# 群聊参与模式: 'smart' (智能模式), 'at_only' (仅@回复), 'all' (回复所有)
GROUP_PARTICIPATION_MODE = 'at_only'

# 智能参与基础概率 (0.0-1.0)
GROUP_PARTICIPATION_BASE_PROBABILITY = 0.3

# 群聊参与最小间隔时间（秒）
GROUP_PARTICIPATION_MIN_INTERVAL = 300  # 5分钟

# 群聊参与最大间隔时间（秒）- 超过此时间会增加参与概率
GROUP_PARTICIPATION_MAX_INTERVAL = 3600  # 1小时

# 是否启用群聊参与统计日志
ENABLE_GROUP_PARTICIPATION_STATS = True

# 是否启用日志
ENABLE_LOGGING = True

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL = 'INFO'

# ==================== 消息发送配置 ====================
# AI回复消息句子间隔时间（秒）
MESSAGE_SENTENCE_DELAY = 2

# 是否启用消息间隔延迟
ENABLE_MESSAGE_DELAY = True

# 消息长度限制（字符数）- 超过此长度的消息将不会发送给AI处理
MAX_MESSAGE_LENGTH = 240

# 消息过长时的提醒文本
MESSAGE_TOO_LONG_REMINDER = "您的消息太长了，请发送不超过{max_length}字的消息哦~"

# ==================== 上下文配置 ====================
# 上下文记忆大小
CONTEXT_MEMORY_SIZE = 10

# ==================== 记忆配置 ====================
# 记忆保留天数
MEMORY_RETENTION_DAYS = 30

# 是否启用记忆摘要
ENABLE_MEMORY_SUMMARY = True

# 最大历史记录数量
MAX_HISTORY_SIZE = 50

# ==================== 天气配置 ====================
# 高德地图API配置
GAODE_API_KEY = "key-"  # 在高德开放平台获取
GAODE_WEATHER_URL = "https://restapi.amap.com/v3/weather/weatherInfo"
GAODE_GEOCODE_URL = "https://restapi.amap.com/v3/geocode/geo"

# 天气推送配置
WEATHER_CITIES = ['徐州', '北京', '上海']  # 需要推送天气的城市
MORNING_SEND_HOUR = 8  # 每天早上8点发送天气
STARTUP_WEATHER_DELAY = 300  # 启动后5分钟发送天气（秒）

# 天气缓存配置
WEATHER_CACHE_HOURS = 3  # 天气缓存有效期（小时）

# ==================== 主动发消息配置 ====================
# 是否启用主动发消息功能
ENABLE_PROACTIVE_MESSAGE = True

# 主动发消息时间间隔（分钟）
PROACTIVE_MESSAGE_MIN_INTERVAL = 30  # 最小间隔30分钟
PROACTIVE_MESSAGE_MAX_INTERVAL = 120  # 最大间隔2小时

# 静默时间设置（24小时制）
PROACTIVE_QUIET_START_HOUR = 24  # 静默开始时间（晚上11点）
PROACTIVE_QUIET_END_HOUR = 6    # 静默结束时间（早上7点）

# 主动发消息给同一目标的最小间隔（小时）
PROACTIVE_TARGET_MIN_INTERVAL = 0

# ==================== 每日日程配置 ====================
# 是否启用每日日程功能
ENABLE_DAILY_SCHEDULE = True

# 日程生成时间（每天晚上生成次日日程）
SCHEDULE_GENERATE_HOUR = 23  # 晚上11点生成次日日程
SCHEDULE_GENERATE_MINUTE = 30

# 日程存储目录
SCHEDULE_DIR = 'schedules'

# 日程提醒提前时间（分钟）
SCHEDULE_REMINDER_ADVANCE = 0  # 提前多少分钟提醒

# 是否在消息中附加日程信息
ENABLE_SCHEDULE_IN_MESSAGE = True

# 日程AI生成提示词
SCHEDULE_PROMPT = """请为{date}生成一个每日日程安排。
要求：
1. 生成3-5个时间点的活动
2. 时间分布在9:00-21:00之间
3. 活动内容应该是具体的事情或活动，不是对话语句
4. 活动要贴心、温馨，适合日常生活
5. 严格按照JSON格式返回

活动示例（正确格式）：
- "晨练" 而不是 "早安问候"
- "午餐时间" 而不是 "午餐提醒"
- "下午茶" 而不是 "下午茶时光"
- "晚餐" 而不是 "晚餐关怀"
- "睡前阅读" 而不是 "晚安问候"

返回格式：
{{
    "date": "{date}",
    "schedule": [
        {{"time": "09:00", "activity": "晨练"}},
        {{"time": "12:00", "activity": "午餐时间"}},
        {{"time": "15:00", "activity": "下午茶"}},
        {{"time": "18:00", "activity": "晚餐"}},
        {{"time": "21:00", "activity": "睡前阅读"}}
    ]
}}"""

# ==================== 定时提醒配置 ====================
# 是否启用定时提醒功能
ENABLE_REMINDER = True

# 提醒清理配置（清理多少天前的已触发提醒）
REMINDER_CLEANUP_DAYS = 7

# ==================== 群聊内容缓存配置 ====================
# 是否启用群聊内容缓存功能
ENABLE_GROUP_CONTENT_CACHE = True

# 群聊内容缓存大小（每个群聊最多缓存多少条消息）
GROUP_CONTENT_CACHE_SIZE = 20

# 群聊内容缓存有效期（小时）
GROUP_CONTENT_CACHE_HOURS = 24

# 是否在@机器人时包含群聊缓存内容
INCLUDE_GROUP_CACHE_ON_AT = True

# 群聊缓存内容在AI上下文中的最大条数
GROUP_CACHE_CONTEXT_SIZE = 20

# ==================== 群聊管理配置 ====================
# 是否启用群聊管理功能
ENABLE_GROUP_MANAGEMENT = False

# 群聊成员缓存有效期（秒）
GROUP_MEMBER_CACHE_DURATION = 3600  # 1小时

# 群聊管理权限控制
GROUP_MANAGEMENT_ADMINS = []  # 有群聊管理权限的用户列表，空列表表示所有管理员都有权限

# 群聊操作日志记录
ENABLE_GROUP_OPERATION_LOG = True

# 群聊操作安全确认（对于危险操作如移除成员）
REQUIRE_GROUP_OPERATION_CONFIRMATION = False

# ==================== 签到功能配置 ====================
# 是否启用签到功能
ENABLE_CHECKIN_SYSTEM = False

# 签到数据库文件路径
CHECKIN_DATABASE_PATH = "checkin_history.db"

# 自动启用签到功能的群聊列表
AUTO_ENABLE_CHECKIN_GROUPS = [
    # 格式：群聊名称或配置字典
    "智械危机"
]

# 全局签到定时任务配置
GLOBAL_CHECKIN_SCHEDULE = {
    'reminder_time': '10:00',    # 全局签到提醒时间
    'stats_time': '23:00',       # 全局统计发送时间
    'kick_time': '23:30',        # 全局踢人执行时间
    'cleanup_time': '23:50',     # 全局数据清理时间
}

# 默认签到配置（用于字符串格式的群聊名称）
DEFAULT_CHECKIN_CONFIG = {
    'reminder_time': '10:00',    # 签到提醒时间
    'stats_time': '23:00',       # 统计发送时间
    'kick_time': '23:30',        # 踢人执行时间
    'max_absent_days': 30         # 最大缺勤天数
}

# 签到关键词列表
CHECKIN_KEYWORDS = [
    "签到", "打卡", "报到", "check in", "checkin",
    "签个到", "打个卡", "来签到", "我签到"
]

# ==================== 图片生成配置 ====================
# 是否启用AI图片生成功能
ENABLE_IMAGE_GENERATION = True

# 图片生成API配置
# 支持的服务类型: "openai", "deepseek", "auto"
IMAGE_GENERATION_SERVICE = "auto"

# 图片生成API密钥（如果与主API不同，可以单独配置）
IMAGE_API_KEY = None  # 如果为None，则使用主API_KEY

# 图片生成API基础URL（如果与主API不同，可以单独配置）
IMAGE_BASE_URL = None  # 如果为None，则使用主BASE_URL

# 图片生成模型（如果与主模型不同，可以单独配置）
IMAGE_MODEL = None  # 如果为None，则使用主MODEL

# 图片生成超时时间（秒）
IMAGE_GENERATION_TIMEOUT = 120

# 生成图片保存目录
IMAGE_OUTPUT_DIR = "generated_images"

# 图片文件清理配置（保留天数）
IMAGE_CLEANUP_DAYS = 7

# 图片生成提示词优化
ENABLE_IMAGE_PROMPT_OPTIMIZATION = True

# 图片生成失败时的回复消息
IMAGE_GENERATION_FAILED_MESSAGE = "抱歉，图片生成失败了，请稍后再试~"

# 图片生成成功时的回复消息
IMAGE_GENERATION_SUCCESS_MESSAGE = "图片生成完成啦！"

# 是否在图片发送后自动删除本地文件
AUTO_DELETE_SENT_IMAGES = True

# 是否在生成的图片上添加水印
WATERMARK = False

# ==================== 消息队列配置 ====================
# 是否启用消息队列功能（确保消息按顺序处理）
ENABLE_MESSAGE_QUEUE = True

# 消息队列最大大小
MESSAGE_QUEUE_MAX_SIZE = 100

# 消息队列处理超时时间（秒）
MESSAGE_QUEUE_TIMEOUT = 30

# ==================== 情感状态系统配置 ====================
# 是否启用情感状态系统
ENABLE_EMOTION_STATE = True

# 情感状态持续时间配置（秒）
EMOTION_DURATION_CONFIG = {
    'base_duration': 1800,      # 基础持续时间（30分钟）
    'min_duration': 300,        # 最小持续时间（5分钟）
    'max_duration': 7200,       # 最大持续时间（2小时）
}

# 情感状态衰减配置
EMOTION_DECAY_CONFIG = {
    'decay_rate': 0.1,          # 基础衰减率
    'cleanup_interval': 300,    # 清理过期情感的间隔（秒）
    'auto_decay_enabled': True, # 是否启用自动时间衰减
}

# 情感状态触发配置
EMOTION_TRIGGER_CONFIG = {
    'sentiment_threshold': 0.3,     # 情感分析触发阈值
    'intensity_multiplier': 1.0,    # 情感强度倍数
    'random_emotion_chance': 0.05,  # 随机情感变化概率
}

# 情感状态存储配置
EMOTION_STORAGE_CONFIG = {
    'save_interval': 60,        # 自动保存间隔（秒）
    'max_history_size': 100,    # 最大历史记录数量
    'max_active_emotions': 5,   # 最大同时活跃情感数量
}

# 情感状态在回复中的影响配置
EMOTION_RESPONSE_CONFIG = {
    'include_emotion_prompt': True,     # 是否在AI提示词中包含情感状态
    'emotion_influence_strength': 0.8,  # 情感对回复的影响强度（0-1）
    'show_emotion_in_debug': True,      # 是否在调试信息中显示情感状态
}

# 时间段情感倾向配置
TIME_BASED_EMOTION_CONFIG = {
    'morning': {        # 早晨 (6-9点)
        'emotions': ['energetic', 'happy', 'curious'],
        'probability': 0.1
    },
    'noon': {           # 中午 (12-14点)
        'emotions': ['happy', 'calm', 'grateful'],
        'probability': 0.05
    },
    'evening': {        # 晚上 (18-20点)
        'emotions': ['calm', 'happy', 'grateful'],
        'probability': 0.05
    },
    'night': {          # 深夜 (22-6点)
        'emotions': ['tired', 'calm', 'lonely'],
        'probability': 0.08
    }
}

# ==================== 其他配置 ====================
# 时区设置
TIMEZONE = "Asia/Shanghai"

# ==================== 好友请求配置 ====================
# 是否启用自动接受好友请求
ENABLE_AUTO_ACCEPT_FRIEND_REQUEST = True

# 检查好友请求的时间间隔（分钟）
AUTO_ACCEPT_FRIEND_REQUEST_INTERVAL = 5


# 每天自动通过的好友请求数量上限
MAX_AUTO_ACCEPT_FRIENDS_PER_DAY = 15

# 接受好友请求后的备注
AUTO_ACCEPT_FRIEND_REQUEST_REMARK = ""

# 接受好友请求后添加的标签
AUTO_ACCEPT_FRIEND_REQUEST_TAGS = []


# ==================== 新好友自动邀请入群配置 ====================
# 是否启用自动邀请新好友入群
ENABLE_AUTO_INVITE_TO_GROUP = True

# 自动邀请加入的群聊名称（请确保机器人是该群的群主或管理员）
AUTO_INVITE_TO_GROUP_NAME = "智械危机"


# ==================== 自定义表情配置 ====================
# 是否启用发送自定义表情功能
ENABLE_CUSTOM_EMOTION = True

# 发送自定义表情的概率 (0.0-1.0)，例如0.5表示有50%的概率发送
# 根据反馈调整为更低的概率，0.1表示10%的概率发送表情
EMOTION_SEND_PROBABILITY = 0.3

# 自定义表情索引列表，从0开始计数
# 使用list(range(n))自动生成包含0到n-1的索引列表
# 例如：list(range(30)) 表示使用0到29的索引（共30个表情）
# 根据你实际的微信自定义表情数量调整range参数
CUSTOM_EMOTION_INDEXES = list(range(30))

from __future__ import annotations

"""tts_client.py
封装 TTS API → VB 虚拟麦克风播放的简易客户端。
使用OpenAI标准配置格式，支持Fish Audio、OpenAI等多种TTS服务。
供 CallSession 等业务模块调用：

    from fish.tts_client import TTSClient
    tts = TTSClient()
    tts.say("你好，世界")
"""

import os
import logging
from types import GeneratorType
from typing import Generator, Iterable

# 尝试导入 sounddevice；若失败将使用 pyaudio 回退
try:
    import sounddevice as sd  # type: ignore
except ImportError:  # pragma: no cover
    sd = None  # type: ignore
    import pyaudio  # type: ignore

from dotenv import load_dotenv

# Fish Audio SDK (保持原有功能)
try:
    from fish_audio_sdk import WebSocketSession, TTSRequest
    FISH_SDK_AVAILABLE = True
except ImportError:
    FISH_SDK_AVAILABLE = False
    WebSocketSession = None
    TTSRequest = None

# OpenAI客户端 (用于其他TTS服务)
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    OpenAI = None

logger = logging.getLogger(__name__)

# 加载 .env（在项目根或本目录下）
if not load_dotenv():
    # fallback: 兼容 src/fish/env_example.txt 的位置
    load_dotenv(os.path.join(os.path.dirname(__file__), "env_example.txt"))


class TTSClient:
    """TTS客户端 - 使用OpenAI标准配置格式，支持多种TTS服务。"""

    def __init__(
        self,
        api_key: str | None = None,
        base_url: str | None = None,
        model: str | None = None,
        voice: str | None = None,
        sample_rate: int = 44100,
        device_keyword: str | None = None,
        temperature: float | None = None,
        top_p: float | None = None,
        speed: float | None = None,
    ) -> None:
        # 优先使用传入的参数，然后是配置文件，最后是环境变量
        self.api_key = api_key or self._get_config_value('OPENAI_TTS_API_KEY') or os.getenv("OPENAI_API_KEY")
        self.base_url = base_url or self._get_config_value('OPENAI_TTS_BASE_URL') or "https://api.fish.audio/v1"
        self.model = model or self._get_config_value('OPENAI_TTS_MODEL') or "tts-1"
        self.voice = voice or self._get_config_value('OPENAI_TTS_VOICE') or ""

        if not self.api_key:
            raise RuntimeError("API Key 未设置 (请在配置文件、.env 或环境变量中提供)")
        if not self.voice:
            logger.warning("Voice 未设置，将使用默认音色")

        self.sample_rate = sample_rate
        self.temperature = temperature if temperature is not None else self._get_config_value('OPENAI_TTS_TEMPERATURE', 0.7)
        self.top_p = top_p if top_p is not None else self._get_config_value('OPENAI_TTS_TOP_P', 0.7)
        self.speed = speed if speed is not None else self._get_config_value('OPENAI_TTS_SPEED', 1.0)
        self.device_keyword = device_keyword or self._get_tts_device_keyword()

        # 根据base_url判断使用哪种TTS服务
        self.is_fish_audio = "fish.audio" in self.base_url.lower()

        # 初始化对应的客户端
        self._fish_session = None
        self._openai_client = None

        if self.is_fish_audio and FISH_SDK_AVAILABLE:
            # 使用Fish Audio SDK
            try:
                self._fish_session = WebSocketSession(self.api_key)
                logger.info("使用Fish Audio SDK")
            except Exception as e:
                logger.warning(f"初始化Fish Audio SDK失败: {e}")
        elif OPENAI_AVAILABLE:
            # 使用OpenAI客户端
            try:
                self._openai_client = OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url
                )
                logger.info(f"使用OpenAI兼容客户端: {self.base_url}")
            except Exception as e:
                logger.warning(f"初始化OpenAI客户端失败: {e}")
        else:
            logger.warning("没有可用的TTS客户端")

    def _get_config_value(self, config_name: str, default_value=None):
        """从配置中获取值，如果配置未加载则返回默认值"""
        try:
            from src.config import config
            if config_name == 'OPENAI_TTS_API_KEY':
                return config.media.openai_tts.api_key
            elif config_name == 'OPENAI_TTS_BASE_URL':
                return config.media.openai_tts.base_url
            elif config_name == 'OPENAI_TTS_MODEL':
                return config.media.openai_tts.model
            elif config_name == 'OPENAI_TTS_VOICE':
                return config.media.openai_tts.voice
            elif config_name == 'OPENAI_TTS_TEMPERATURE':
                return config.media.openai_tts.temperature
            elif config_name == 'OPENAI_TTS_TOP_P':
                return config.media.openai_tts.top_p
            elif config_name == 'OPENAI_TTS_SPEED':
                return config.media.openai_tts.speed
            else:
                return default_value
        except (ImportError, AttributeError):
            # 配置未加载时返回默认值
            return default_value

    def _get_tts_device_keyword(self) -> str:
        """获取TTS设备关键词，支持防回音配置"""
        try:
            from src.config import VOICE_CALL_ANTI_ECHO_ENABLED, VOICE_CALL_TTS_DEVICE_KEYWORD
            if VOICE_CALL_ANTI_ECHO_ENABLED:
                # 使用配置的TTS专用设备
                return VOICE_CALL_TTS_DEVICE_KEYWORD
            else:
                # 使用默认设备关键词
                return os.getenv("VB_DEVICE", "VB-Audio")
        except ImportError:
            # 配置未加载时使用默认值
            return os.getenv("VB_DEVICE", "VB-Audio")

    # ------------------------------------------------------------------
    # 内部工具
    # ------------------------------------------------------------------
    @staticmethod
    def _find_output_device(keyword: str) -> int | None:
        """查找输出设备索引（兼容 sounddevice / pyaudio），优先匹配最相似的设备。"""
        import difflib

        devices = []

        if sd is not None:
            for idx, dev in enumerate(sd.query_devices()):
                if dev.get("max_output_channels", 0) > 0:
                    devices.append((idx, dev.get("name", "")))
        else:
            pa = pyaudio.PyAudio()
            for idx in range(pa.get_device_count()):
                info = pa.get_device_info_by_index(idx)
                if info.get("maxOutputChannels", 0) > 0:
                    devices.append((idx, info.get("name", "")))
            pa.terminate()

        # 首先尝试精确匹配
        for device_idx, device_name in devices:
            if keyword.lower() in device_name.lower():
                return device_idx

        # 如果没有精确匹配，尝试找最相似的设备
        if keyword:
            device_names = [name for _, name in devices]
            closest_matches = difflib.get_close_matches(keyword, device_names, n=1, cutoff=0.3)

            if closest_matches:
                closest_name = closest_matches[0]
                for device_idx, device_name in devices:
                    if device_name == closest_name:
                        logger.info(f"使用相似匹配的TTS输出设备: {device_name} (匹配关键词: {keyword})")
                        return device_idx

        return None

    @staticmethod
    def _text_iter(text: str) -> Iterable[str]:
        """生成器：一次性输出整段文本并尾随空格，确保服务端 flush。"""
        yield text + " "

    def _play_raw(self, audio_iter: Iterable[bytes], device_idx: int):
        """根据可用后端播放 PCM 音频。"""
        try:
            if sd is not None:
                logger.debug(f"使用sounddevice播放音频，设备索引: {device_idx}")
                with sd.RawOutputStream(
                    samplerate=self.sample_rate,
                    channels=1,
                    dtype="int16",
                    device=device_idx,
                    blocksize=0,
                ) as stream:
                    for chunk in audio_iter:
                        if chunk:
                            stream.write(chunk)
            else:
                logger.debug(f"使用pyaudio播放音频，设备索引: {device_idx}")
                pa = pyaudio.PyAudio()
                try:
                    stream = pa.open(
                        format=pa.get_format_from_width(2),  # int16
                        channels=1,
                        rate=self.sample_rate,
                        output=True,
                        output_device_index=device_idx,
                        frames_per_buffer=1024,
                    )
                    for chunk in audio_iter:
                        if chunk:
                            stream.write(chunk)
                    stream.stop_stream()
                    stream.close()
                except Exception as e:
                    logger.error(f"pyaudio播放失败: {e}")
                    raise
                finally:
                    pa.terminate()
        except Exception as e:
            logger.error(f"音频播放失败，设备索引: {device_idx}, 错误: {e}")
            raise

    # ------------------------------------------------------------------
    # 对外接口
    # ------------------------------------------------------------------
    def say(self, text: str, *, speed: float | None = None, volume: float = 0.0) -> None:
        """阻塞式播放一段文本。

        Args:
            text: 待合成文本
            speed: 语速 (0.5~2.0)，如果为None则使用配置的默认值
            volume: 音量 dB (-20~20)
        """
        if not text:
            return

        # 如果没有指定speed，使用配置的默认值
        if speed is None:
            speed = self.speed

        device_idx = self._find_output_device(self.device_keyword)
        if device_idx is None:
            logger.error(f"未找到包含关键词 '{self.device_keyword}' 的音频输出设备")
            # 列出所有可用的输出设备
            if sd is not None:
                logger.error("可用的输出设备:")
                for idx, dev in enumerate(sd.query_devices()):
                    if dev.get("max_output_channels", 0) > 0:
                        logger.error(f"  [{idx}] {dev['name']}")
            else:
                import pyaudio
                pa = pyaudio.PyAudio()
                logger.error("可用的输出设备:")
                for idx in range(pa.get_device_count()):
                    dev = pa.get_device_info_by_index(idx)
                    if dev.get("maxOutputChannels", 0) > 0:
                        logger.error(f"  [{idx}] {dev['name']}")
                pa.terminate()
            raise RuntimeError(f"未找到包含关键词 '{self.device_keyword}' 的音频输出设备")

        if sd is not None:
            dev_name = sd.query_devices(device_idx)["name"]
        else:
            import pyaudio
            pa_tmp = pyaudio.PyAudio()
            dev_name = pa_tmp.get_device_info_by_index(device_idx)["name"]
            pa_tmp.terminate()
        logger.debug("TTS 输出设备 #%s · %s", device_idx, dev_name)

        # 根据服务类型选择不同的TTS实现
        if self.is_fish_audio and self._fish_session:
            self._say_with_fish(text, speed, volume, device_idx)
        elif self._openai_client:
            self._say_with_openai(text, speed, volume, device_idx)
        else:
            raise RuntimeError("没有可用的TTS客户端")

        logger.debug("TTS 播放完成 (%d chars)", len(text))

    def _say_with_fish(self, text: str, speed: float, volume: float, device_idx: int) -> None:
        """使用Fish Audio SDK进行TTS"""
        if not self._fish_session:
            raise RuntimeError("Fish Audio会话不可用")

        try:
            # 创建TTS请求
            request = TTSRequest(
                text="",
                reference_id=self.voice,
                format="pcm",
                sample_rate=self.sample_rate,
                temperature=self.temperature,
                top_p=self.top_p,
                prosody={"speed": speed, "volume": volume}
            )

            # 生成音频并播放
            def audio_gen():
                for chunk in self._fish_session.tts(request, self._text_iter(text), backend="speech-1.6"):
                    if chunk:
                        yield chunk

            self._play_raw(audio_gen(), device_idx)

        except Exception as e:
            logger.error("Fish TTS 调用失败: %s", e)
            # 重试一次
            try:
                logger.info("Fish TTS 重试中...")
                def audio_gen():
                    for chunk in self._fish_session.tts(request, self._text_iter(text), backend="speech-1.6"):
                        if chunk:
                            yield chunk
                self._play_raw(audio_gen(), device_idx)
            except Exception as e2:
                logger.error("Fish TTS 重试仍失败: %s", e2)
                raise

    def _say_with_openai(self, text: str, speed: float, volume: float, device_idx: int) -> None:
        """使用OpenAI兼容API进行TTS"""
        try:
            # 使用requests直接调用API，避免OpenAI SDK的User-Agent被阻止
            import requests

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"  # 模拟浏览器
            }

            data = {
                "model": self.model,
                "input": text,
                "voice": self.voice,
                "response_format": "wav"  # 请求WAV格式，避免MP3转换问题
            }

            # 发送请求
            response = requests.post(
                f"{self.base_url}/audio/speech",
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                audio_data = response.content
                if audio_data:
                    # 检查Content-Type来判断音频格式
                    content_type = response.headers.get('content-type', '')

                    if 'audio/wav' in content_type or 'audio/wave' in content_type:
                        # WAV格式，跳过头部直接播放PCM数据
                        if len(audio_data) > 44:  # WAV文件头通常是44字节
                            pcm_data = audio_data[44:]  # 跳过WAV头部
                            self._play_raw([pcm_data], device_idx)
                            logger.debug("播放WAV音频到虚拟音频设备")
                        else:
                            logger.warning("WAV文件太小，可能格式不正确")
                    elif 'audio/mpeg' in content_type or 'audio/mp3' in content_type:
                        # MP3格式，需要转换为PCM后播放到虚拟音频设备
                        try:
                            # 尝试使用pydub转换（如果可用）
                            try:
                                from pydub import AudioSegment
                                import io
                                audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
                                # 转换为指定采样率的PCM
                                audio_segment = audio_segment.set_frame_rate(self.sample_rate)
                                audio_segment = audio_segment.set_channels(1)  # 单声道
                                pcm_data = audio_segment.raw_data
                                self._play_raw([pcm_data], device_idx)
                                logger.debug("使用pydub转换MP3并播放到虚拟音频设备")
                            except Exception as pydub_error:
                                logger.warning(f"pydub转换失败: {pydub_error}")
                                # 如果pydub失败，尝试保存为临时文件并用ffmpeg转换
                                import tempfile
                                import subprocess
                                import os

                                with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as tmp_mp3:
                                    tmp_mp3.write(audio_data)
                                    tmp_mp3.flush()
                                    mp3_path = tmp_mp3.name

                                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_wav:
                                    wav_path = tmp_wav.name

                                try:
                                    # 使用ffmpeg转换MP3到WAV
                                    subprocess.run([
                                        'ffmpeg', '-i', mp3_path, '-ar', str(self.sample_rate),
                                        '-ac', '1', '-f', 'wav', wav_path, '-y'
                                    ], check=True, capture_output=True)

                                    # 读取WAV文件并播放
                                    with open(wav_path, 'rb') as f:
                                        wav_data = f.read()
                                        # 跳过WAV头部（通常44字节）
                                        pcm_data = wav_data[44:]
                                        self._play_raw([pcm_data], device_idx)
                                    logger.debug("使用ffmpeg转换MP3并播放到虚拟音频设备")
                                except subprocess.CalledProcessError:
                                    logger.error("ffmpeg转换失败，无法播放MP3音频到虚拟设备")
                                    raise RuntimeError("无法转换MP3音频格式")
                                finally:
                                    # 清理临时文件
                                    try:
                                        os.unlink(mp3_path)
                                        os.unlink(wav_path)
                                    except:
                                        pass
                        except Exception as convert_error:
                            logger.error(f"MP3转换失败: {convert_error}")
                            raise
                    else:
                        # 假设是PCM格式，直接播放到虚拟音频设备
                        self._play_raw([audio_data], device_idx)
                else:
                    logger.warning("TTS API返回空音频数据")
            else:
                logger.error(f"TTS API调用失败: HTTP {response.status_code}")
                logger.error(f"响应内容: {response.text[:500]}")
                raise RuntimeError(f"TTS API调用失败: HTTP {response.status_code}")

        except Exception as e:
            logger.error(f"OpenAI TTS调用失败: {e}")
            raise


# 向后兼容的别名
class FishTTSClient(TTSClient):
    """向后兼容的Fish TTS客户端别名"""

    def __init__(
        self,
        api_key: str | None = None,
        model_id: str | None = None,
        backend: str = "speech-1.6",
        sample_rate: int = 44100,
        device_keyword: str | None = None,
        temperature: float | None = None,
        top_p: float | None = None,
        speed: float | None = None,
    ) -> None:
        # 将旧的参数映射到新的OpenAI格式
        base_url = "https://api.fish.audio/v1"  # 默认使用Fish Audio
        super().__init__(
            api_key=api_key,
            base_url=base_url,
            model="tts-1",  # Fish Audio的默认模型
            voice=model_id,  # 将model_id映射为voice
            sample_rate=sample_rate,
            device_keyword=device_keyword,
            temperature=temperature,
            top_p=top_p,
            speed=speed,
        )
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模块
参考My-Dream-Moments-WeChat-wxauto项目的数据库结构
"""

import json
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import UniqueConstraint
import config

# 创建基类
Base = declarative_base()

# 创建数据库连接
engine = create_engine('sqlite:///chat_history.db')

# 创建会话工厂
Session = sessionmaker(bind=engine)


class ChatMessage(Base):
    """聊天消息记录表"""
    __tablename__ = 'chat_messages'

    id = Column(Integer, primary_key=True)
    sender_id = Column(String(100))  # 发送者微信ID
    sender_name = Column(String(100))  # 发送者昵称
    message = Column(Text)  # 发送的消息
    reply = Column(Text)  # 机器人的回复
    created_at = Column(DateTime, default=datetime.now)
    is_group = Column(Boolean, default=False)
    group_id = Column(String(255), nullable=True)


class Memory(Base):
    """记忆存储表"""
    __tablename__ = 'memories'

    id = Column(Integer, primary_key=True, autoincrement=True)

    # 每个用户的唯一标识（私聊用微信ID，群聊用"群ID"）
    user_id = Column(String(255), nullable=True)
    # 群聊标识字段
    group_id = Column(String(255), nullable=True)
    is_group = Column(Boolean, default=False)
    # 存储最近对话历史（JSON格式）
    history = Column(Text)
    # 最后一次摘要内容
    last_summary = Column(Text)
    # 关键词列表（JSON格式）
    keywords = Column(Text)
    # 最后更新时间
    last_update = Column(DateTime, default=datetime.now)

    # 修改唯一约束
    __table_args__ = (
        UniqueConstraint('user_id', 'group_id', name='uq_memory_identifier'),
    )

    def get_history(self):
        """获取历史对话列表"""
        return json.loads(self.history) if self.history else []

    def get_keywords(self):
        """获取关键词列表"""
        return json.loads(self.keywords) if self.keywords else []


class User(Base):
    """用户信息表（包含好感度系统）"""
    __tablename__ = 'users'

    user_id = Column(String(255), primary_key=True)  # 用户唯一标识
    favorability = Column(Float, default=10.0)  # 好感度，初始值10.0
    last_interaction = Column(DateTime, default=datetime.now)  # 最后交互时间
    interaction_count = Column(Integer, default=0)  # 交互次数
    created_at = Column(DateTime, default=datetime.now)  # 创建时间

    def get_favorability_level(self):
        """获取好感度等级描述"""
        if self.favorability == 100:
            return "♾️ 永恒之约"
        elif self.favorability >= 90:
            return "💞 挚心挚友"
        elif self.favorability >= 80:
            return "✨ 挚友"
        elif self.favorability >= 60:
            return "😊 挚友暖光"
        elif self.favorability >= 40:
            return "🤝 泛泛之交"
        elif self.favorability >= 20:
            return "👋 浅浅之交"
        elif self.favorability >= 0:
            return "❄️ 萍水相逢"
        else:
            return "👿 仇怨相向"


class UserReminder(Base):
    """用户定时提醒表"""
    __tablename__ = 'user_reminders'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(255), nullable=False)  # 用户ID
    chat_id = Column(String(255), nullable=False)  # 聊天ID（私聊为用户名，群聊为群名）
    is_group = Column(Boolean, default=False)  # 是否为群聊
    original_message = Column(Text, nullable=False)  # 用户原始消息
    reminder_message = Column(Text, nullable=True)  # 提醒内容
    event_time = Column(DateTime, nullable=False)  # 事件时间
    reminder_time = Column(DateTime, nullable=False)  # 提醒时间
    is_triggered = Column(Boolean, default=False)  # 是否已触发
    created_at = Column(DateTime, default=datetime.now)  # 创建时间


class GroupContentCache(Base):
    """群聊内容缓存表"""
    __tablename__ = 'group_content_cache'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_id = Column(String(255), nullable=False)  # 群聊ID
    sender_name = Column(String(100), nullable=False)  # 发送者昵称
    message_content = Column(Text, nullable=False)  # 消息内容
    message_time = Column(DateTime, default=datetime.now)  # 消息时间
    created_at = Column(DateTime, default=datetime.now)  # 创建时间

    def to_dict(self):
        """转换为字典格式"""
        return {
            'sender_name': self.sender_name,
            'message_content': self.message_content,
            'message_time': self.message_time.strftime('%H:%M'),
            'created_at': self.created_at
        }



class CheckinRecord(Base):
    """签到记录表"""
    __tablename__ = 'checkin_records'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_name = Column(String(255), nullable=False)
    user_name = Column(String(100), nullable=False)
    checkin_date = Column(String(20), nullable=False)
    checkin_time = Column(String(20), nullable=False)
    created_at = Column(DateTime, default=datetime.now)

    __table_args__ = (UniqueConstraint('group_name', 'user_name', 'checkin_date'),)


class GroupCheckinConfig(Base):
    """群聊签到配置表"""
    __tablename__ = 'group_checkin_config'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_name = Column(String(255), unique=True, nullable=False)
    is_enabled = Column(Boolean, default=True)
    reminder_time = Column(String(10), default='10:00')
    stats_time = Column(String(10), default='23:00')
    kick_time = Column(String(10), default='23:30')
    max_absent_days = Column(Integer, default=3)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class KickRecord(Base):
    """踢人记录表"""
    __tablename__ = 'kick_records'

    id = Column(Integer, primary_key=True, autoincrement=True)
    group_name = Column(String(255), nullable=False)
    user_name = Column(String(100), nullable=False)
    kick_reason = Column(Text, nullable=False)
    kick_date = Column(String(20), nullable=False)
    absent_days = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=datetime.now)

# 创建数据库表

class DailyStats(Base):
    """每日统计数据表"""
    __tablename__ = 'daily_stats'

    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(String(10), unique=True, nullable=False)  # 日期，格式 YYYY-MM-DD
    accepted_friends_count = Column(Integer, default=0)  # 已接受的好友请求数
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

Base.metadata.create_all(engine)
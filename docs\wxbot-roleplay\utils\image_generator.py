#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI图片生成工具模块
支持多种AI图片生成服务
"""

import os
import re
import time
import requests
import base64
from datetime import datetime
from typing import Optional, Tuple, List
from utils.logger import log


class ImageGenerator:
    """AI图片生成器"""
    
    def __init__(self, api_key: str = None, base_url: str = None, model: str = None):
        """
        初始化图片生成器
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model: 使用的模型名称
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.output_dir = "generated_images"
        
        # 确保输出目录存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            log(f"创建图片输出目录: {self.output_dir}")
    
    def is_image_request(self, text: str) -> bool:
        """
        检查文本是否为图片生成请求
        
        Args:
            text: 输入文本
            
        Returns:
            bool: 是否为图片生成请求
        """
        if not text:
            return False
        
        # 图片生成关键词
        image_keywords = [
            "画", "绘制", "生成图片", "生成图像", "画一张", "画个", "画出",
            "帮我画", "给我画", "创作", "制作图片", "做一张图", "生成一张",
            "图片生成", "图像生成", "AI画图", "AI绘画", "画画",
            "draw", "generate image", "create image", "make picture"
        ]
        
        text_lower = text.lower()
        
        for keyword in image_keywords:
            if keyword in text_lower:
                return True
        
        return False
    
    def extract_image_prompt(self, text: str) -> str:
        """
        从文本中提取图片生成提示词
        
        Args:
            text: 输入文本
            
        Returns:
            str: 提取的提示词
        """
        if not text:
            return ""
        
        # 移除常见的请求前缀
        prefixes_to_remove = [
            "画", "绘制", "生成图片", "生成图像", "画一张", "画个", "画出",
            "帮我画", "给我画", "创作", "制作图片", "做一张图",
            "图片生成", "图像生成", "AI画图", "AI绘画", "画画",
            "draw", "generate image", "create image", "make picture"
        ]
        
        prompt = text
        for prefix in prefixes_to_remove:
            if prompt.startswith(prefix):
                prompt = prompt[len(prefix):].strip()
                break
        
        # 移除常见的连接词
        connecting_words = ["一个", "一张", "一幅", "关于", "的图片", "的图像", "的画"]
        for word in connecting_words:
            prompt = prompt.replace(word, "").strip()
        
        return prompt if prompt else text
    
    def generate_image_openai_compatible(self, prompt: str) -> Optional[str]:
        """
        使用OpenAI兼容的API生成图片
        
        Args:
            prompt: 图片描述提示词
            
        Returns:
            Optional[str]: 生成的图片文件路径，失败返回None
        """
        try:
            if not self.api_key or not self.base_url:
                log("图片生成API配置不完整", "ERROR")
                return None
            
            # 构建请求URL
            url = f"{self.base_url.rstrip('/')}/images/generations"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "prompt": prompt,
                "n": 1,
                "size": "1024x1024",
                "response_format": "b64_json"
            }

            # 添加水印控制参数
            import config
            if hasattr(config, 'WATERMARK'):
                data["watermark"] = config.WATERMARK
            
            if self.model:
                data["model"] = self.model
            
            log(f"开始生成图片，提示词: {prompt}")
            
            response = requests.post(url, json=data, headers=headers, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                
                if "data" in result and len(result["data"]) > 0:
                    # 获取base64编码的图片数据
                    image_data = result["data"][0]
                    
                    if "b64_json" in image_data:
                        return self._save_base64_image(image_data["b64_json"], prompt)
                    elif "url" in image_data:
                        return self._download_image(image_data["url"], prompt)
                
                log("API响应中没有图片数据", "ERROR")
                return None
            else:
                log(f"图片生成API请求失败: {response.status_code} - {response.text}", "ERROR")
                return None
                
        except Exception as e:
            log(f"图片生成异常: {str(e)}", "ERROR")
            return None
    
    def generate_image_deepseek(self, prompt: str) -> Optional[str]:
        """
        使用DeepSeek Janus模型生成图片
        
        Args:
            prompt: 图片描述提示词
            
        Returns:
            Optional[str]: 生成的图片文件路径，失败返回None
        """
        try:
            if not self.api_key:
                log("DeepSeek API密钥未配置", "ERROR")
                return None
            
            # DeepSeek Janus API endpoint
            url = "https://api.deepseek.com/v1/chat/completions"
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 构建消息，使用Janus模型的图片生成格式
            messages = [
                {
                    "role": "user",
                    "content": f"Please generate an image of: {prompt}"
                }
            ]
            
            data = {
                "model": "janus-pro-7b",
                "messages": messages,
                "stream": False
            }
            
            log(f"使用DeepSeek Janus生成图片，提示词: {prompt}")
            
            response = requests.post(url, json=data, headers=headers, timeout=120)
            
            if response.status_code == 200:
                result = response.json()
                
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    
                    # 检查是否包含图片数据
                    if "data:image" in content:
                        # 提取base64图片数据
                        base64_match = re.search(r'data:image/[^;]+;base64,([^"]+)', content)
                        if base64_match:
                            base64_data = base64_match.group(1)
                            return self._save_base64_image(base64_data, prompt)
                
                log("DeepSeek响应中没有图片数据", "ERROR")
                return None
            else:
                log(f"DeepSeek API请求失败: {response.status_code} - {response.text}", "ERROR")
                return None
                
        except Exception as e:
            log(f"DeepSeek图片生成异常: {str(e)}", "ERROR")
            return None
    
    def _save_base64_image(self, base64_data: str, prompt: str) -> Optional[str]:
        """
        保存base64编码的图片
        
        Args:
            base64_data: base64编码的图片数据
            prompt: 图片提示词（用于文件命名）
            
        Returns:
            Optional[str]: 保存的图片文件路径
        """
        try:
            # 解码base64数据
            image_bytes = base64.b64decode(base64_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_prompt = re.sub(r'[^\w\s-]', '', prompt)[:20]  # 清理文件名
            filename = f"generated_{timestamp}_{safe_prompt}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            # 保存图片
            with open(filepath, 'wb') as f:
                f.write(image_bytes)
            
            log(f"图片保存成功: {filepath}")
            return filepath
            
        except Exception as e:
            log(f"保存图片失败: {str(e)}", "ERROR")
            return None
    
    def _download_image(self, url: str, prompt: str) -> Optional[str]:
        """
        从URL下载图片
        
        Args:
            url: 图片URL
            prompt: 图片提示词（用于文件命名）
            
        Returns:
            Optional[str]: 保存的图片文件路径
        """
        try:
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                # 生成文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_prompt = re.sub(r'[^\w\s-]', '', prompt)[:20]
                filename = f"generated_{timestamp}_{safe_prompt}.png"
                filepath = os.path.join(self.output_dir, filename)
                
                # 保存图片
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                log(f"图片下载保存成功: {filepath}")
                return filepath
            else:
                log(f"图片下载失败: {response.status_code}", "ERROR")
                return None
                
        except Exception as e:
            log(f"下载图片异常: {str(e)}", "ERROR")
            return None

    def generate_image(self, prompt: str, service: str = "auto") -> Optional[str]:
        """
        生成图片的统一接口

        Args:
            prompt: 图片描述提示词
            service: 使用的服务 ("auto", "openai", "deepseek")

        Returns:
            Optional[str]: 生成的图片文件路径，失败返回None
        """
        if not prompt:
            log("图片生成提示词为空", "WARNING")
            return None

        # 清理和优化提示词
        clean_prompt = self.extract_image_prompt(prompt)

        if service == "auto":
            # 自动选择服务
            if self.model and "janus" in self.model.lower():
                service = "deepseek"
            else:
                service = "openai"

        log(f"使用 {service} 服务生成图片")

        if service == "deepseek":
            return self.generate_image_deepseek(clean_prompt)
        else:
            return self.generate_image_openai_compatible(clean_prompt)

    def cleanup_old_images(self, days: int = 7):
        """
        清理旧的生成图片

        Args:
            days: 保留天数
        """
        try:
            if not os.path.exists(self.output_dir):
                return

            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)

            deleted_count = 0
            for filename in os.listdir(self.output_dir):
                filepath = os.path.join(self.output_dir, filename)

                if os.path.isfile(filepath):
                    file_time = os.path.getmtime(filepath)
                    if file_time < cutoff_time:
                        os.remove(filepath)
                        deleted_count += 1

            if deleted_count > 0:
                log(f"清理了 {deleted_count} 个旧图片文件")

        except Exception as e:
            log(f"清理旧图片失败: {str(e)}", "ERROR")


# 全局图片生成器实例
_image_generator = None


def get_image_generator(api_key: str = None, base_url: str = None, model: str = None) -> ImageGenerator:
    """
    获取图片生成器实例（单例模式）

    Args:
        api_key: API密钥
        base_url: API基础URL
        model: 模型名称

    Returns:
        ImageGenerator: 图片生成器实例
    """
    global _image_generator

    if _image_generator is None:
        _image_generator = ImageGenerator(api_key, base_url, model)

    return _image_generator


def is_image_request(text: str) -> bool:
    """
    检查文本是否为图片生成请求的便捷函数

    Args:
        text: 输入文本

    Returns:
        bool: 是否为图片生成请求
    """
    generator = get_image_generator()
    return generator.is_image_request(text)


def generate_image(prompt: str, api_key: str = None, base_url: str = None,
                  model: str = None, service: str = "auto") -> Optional[str]:
    """
    生成图片的便捷函数

    Args:
        prompt: 图片描述提示词
        api_key: API密钥
        base_url: API基础URL
        model: 模型名称
        service: 使用的服务

    Returns:
        Optional[str]: 生成的图片文件路径，失败返回None
    """
    generator = get_image_generator(api_key, base_url, model)
    return generator.generate_image(prompt, service)

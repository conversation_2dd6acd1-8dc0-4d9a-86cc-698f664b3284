# 人设模式切换功能使用说明

## 功能概述

新增了人设模式切换功能，允许用户通过简单的命令在同一个角色的不同人设模式之间切换。

## 使用方法

### 基本命令格式
使用 `#` 开头 followed by 人设文件名（不包含.md后缀）：

```
#人设文件名
```

### 示例命令

假设当前角色为 `lin`，在 `data/avatars/lin/` 目录下有以下人设文件：
- `avatar.md` - 默认人设
- `action.md` - 行动模式
- `word.md` - 言语模式  
- `sexy.md` - 魅惑模式

可以使用的切换命令：
```
#action    - 切换到行动模式
#word      - 切换到言语模式
#sexy      - 切换到魅惑模式
#avatar    - 切换回默认人设
```

## 功能特点

### 1. 动态人设加载
- 自动检测角色目录下的人设文件
- 支持任意数量的人设文件
- 文件名即为切换命令名

### 2. 全局状态同步
- 切换后立即更新所有对话模块的人设内容
- 定期喂人设功能自动使用新的人设
- 群聊和私聊都支持人设切换

### 3. 错误处理
- 如果指定的人设文件不存在，会显示可用的人设文件列表
- 自动回退到默认人设文件
- 友好的错误提示信息

### 4. 兼容性
- 不影响原有的 `/` 调试命令
- 保持与现有记忆系统的兼容性
- 支持所有现有的对话功能

## 技术实现

### 核心修改
1. **调试命令处理器** (`src/handlers/debug.py`)
   - 新增 `is_avatar_command()` 方法检测 `#` 命令
   - 新增 `process_avatar_command()` 方法处理人设切换
   - 新增 `_switch_to_avatar_file()` 方法执行具体切换

2. **消息处理器** (`src/handlers/message.py`)
   - 修改初始化逻辑支持动态人设文件路径
   - 更新人设切换方法使用新的文件路径逻辑
   - 保持与现有功能的兼容性

3. **配置系统** (`src/config/__init__.py`)
   - 新增 `avatar_file` 配置项支持动态人设文件
   - 保持向后兼容性

4. **主程序** (`src/main.py`)
   - 新增 `update_avatar_content()` 函数更新全局人设内容
   - 支持实时更新所有对话模块的人设

### 工作流程
1. 用户发送 `#人设名` 命令
2. 系统检测到 `#` 前缀，调用人设切换处理
3. 检查指定的人设文件是否存在
4. 读取新的人设文件内容
5. 更新全局配置和所有相关模块
6. 返回切换成功信息

## 测试验证

运行测试脚本验证功能：
```bash
python test_avatar_switching.py
```

测试内容包括：
- 人设文件存在性检查
- 命令检测功能
- 人设切换逻辑
- 配置系统支持
- 消息处理器兼容性

## 注意事项

1. **人设文件命名**
   - 使用英文文件名，避免特殊字符
   - 文件名即为切换命令名，建议简洁明了
   - 必须使用 `.md` 扩展名

2. **文件位置**
   - 人设文件必须放在对应角色的目录下
   - 例如：`data/avatars/角色名/人设文件.md`

3. **默认人设**
   - `avatar.md` 始终作为默认人设文件
   - 当指定的人设文件不存在时自动回退到默认

4. **权限要求**
   - 确保程序有读取人设文件的权限
   - 确保相关目录结构存在

## 故障排除

### 常见问题

1. **命令不生效**
   - 检查命令格式是否正确（#开头）
   - 确认人设文件是否存在
   - 查看日志文件获取详细错误信息

2. **切换后人设内容不变**
   - 重启程序以应用更改
   - 检查文件权限
   - 确认文件内容格式正确

3. **找不到人设文件**
   - 检查文件路径是否正确
   - 确认文件扩展名为.md
   - 使用 `/avatar` 命令查看可用人设

### 日志查看
启用详细日志以帮助诊断问题：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

未来可以考虑的扩展：
1. 人设文件模板自动生成
2. 人设切换历史记录
3. 人设文件在线编辑
4. 更多的人设模式切换效果
5. 用户自定义人设文件

---

此功能完全兼容现有系统，不影响原有功能的使用。
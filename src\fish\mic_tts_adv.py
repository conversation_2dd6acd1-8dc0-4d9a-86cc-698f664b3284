"""mic_tts_adv.py

高级版 VB 虚拟麦克风 TTS：
• 支持命令行参数调整语速、音量、temperature、top_p
• 可指定 backend TTS 模型（speech-1.5 / speech-1.6 / s1 / s1-mini）

使用示例：
python mic_tts_adv.py -t "你好" -s 1.2 -v 3 --backend speech-1.6
"""

from __future__ import annotations

import argparse
import os
import sys

import sounddevice as sd
from dotenv import load_dotenv
from fish_audio_sdk import WebSocketSession, TTSRequest

if not load_dotenv():
    load_dotenv("env_example.txt")

API_KEY = os.getenv("FISH_API_KEY")
MODEL_ID = os.getenv("MODEL_ID")
if not API_KEY:
    raise RuntimeError("请在 .env 或 env_example.txt 中设置 FISH_API_KEY")

SAMPLE_RATE = 44100
CHANNELS = 1
DTYPE = "int16"
VB_DEVICE_NAME = os.getenv("VB_DEVICE", "VB-Audio")


def find_output_device(keyword: str) -> int | None:
    for idx, dev in enumerate(sd.query_devices()):
        if dev["max_output_channels"] > 0 and keyword.lower() in dev["name"].lower():
            return idx
    return None


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Fish Audio TTS → VB 虚拟麦克风")
    parser.add_argument("-t", "--text", help="待合成文本", required=False)
    parser.add_argument("-s", "--speed", type=float, default=1.0, help="语速 0.5-2.0，默认1.0")
    parser.add_argument("-v", "--volume", type=float, default=0.0, help="音量（dB），-20~20，默认0")
    parser.add_argument("--temperature", type=float, default=0.7, help="随机度，默认0.7")
    parser.add_argument("--top_p", type=float, default=0.7, help="多样性，默认0.7")
    parser.add_argument("--backend", default="speech-1.6", choices=["speech-1.5", "speech-1.6", "s1", "s1-mini"], help="TTS模型")
    return parser.parse_args()


def main():
    args = parse_args()

    text = args.text or (" ".join(sys.argv[1:]) if len(sys.argv) > 1 else input("文本> "))

    device_idx = find_output_device(VB_DEVICE_NAME)
    if device_idx is None:
        raise RuntimeError(f"未找到包含关键词 '{VB_DEVICE_NAME}' 的 VB 虚拟麦克风设备")

    print("[mic_tts_adv] 输出设备:", sd.query_devices(device_idx)["name"], flush=True)

    session = WebSocketSession(API_KEY)

    request = TTSRequest(
        text="",
        reference_id=MODEL_ID,
        format="pcm",
        sample_rate=SAMPLE_RATE,
        temperature=args.temperature,
        top_p=args.top_p,
        prosody={
            "speed": args.speed,
            "volume": args.volume,
        },
    )

    def text_stream():
        yield text + " "

    with sd.RawOutputStream(
        samplerate=SAMPLE_RATE,
        channels=CHANNELS,
        dtype=DTYPE,
        device=device_idx,
        blocksize=0,
    ) as stream:
        for chunk in session.tts(request, text_stream(), backend=args.backend):
            if chunk:
                stream.write(chunk)


if __name__ == "__main__":
    main() 
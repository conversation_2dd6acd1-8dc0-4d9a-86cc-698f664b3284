"""
剧情服务
提供剧情数据库查询和管理功能
"""

import os
import sqlite3
import logging
from typing import List, Dict, Optional, Tuple
from .story_classifier import StoryClassifier

logger = logging.getLogger('main')

class StoryService:
    """
    剧情服务
    功能：
    1. 查询剧情数据库
    2. 智能匹配相关剧情
    3. 管理剧情内容
    4. 与分类器协作
    """
    
    def __init__(self, root_dir: str, api_key: str, base_url: str, model: str, max_token: int, temperature: float):
        self.root_dir = root_dir
        self.classifier = StoryClassifier(root_dir, api_key, base_url, model, max_token, temperature)
    
    def _get_story_db_path(self, avatar_name: str) -> str:
        """获取剧情数据库路径"""
        story_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "word")
        db_dir = os.path.join(story_dir, "db")
        os.makedirs(db_dir, exist_ok=True)
        return os.path.join(db_dir, "story_knowledge.db")
    
    def query_relevant_story(self, avatar_name: str, user_message: str, max_results: int = 5) -> Dict[str, List[str]]:
        """
        查询与用户消息相关的剧情内容
        
        Args:
            avatar_name: 角色名称
            user_message: 用户消息
            max_results: 最大返回结果数
            
        Returns:
            Dict[str, List[str]]: 相关剧情内容
        """
        try:
            db_path = self._get_story_db_path(avatar_name)
            
            if not os.path.exists(db_path):
                logger.info(f"角色 {avatar_name} 没有剧情数据库")
                return {}
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询相关内容
            relevant_content = {
                'dialogues': [],
                'plots': [],
                'events': [],
                'relationships': [],
                'background': []
            }
            
            # 简单的关键词匹配查询（可以后续优化为语义搜索）
            keywords = self._extract_keywords(user_message)
            
            if keywords:
                # 查询台词
                for keyword in keywords:
                    cursor.execute('''
                        SELECT dialogue FROM dialogues
                        WHERE dialogue LIKE ?
                        LIMIT ?
                    ''', (f'%{keyword}%', max_results))
                    
                    results = cursor.fetchall()
                    for result in results:
                        if result[0] not in relevant_content['dialogues']:
                            relevant_content['dialogues'].append(result[0])
                
                # 查询剧情内容
                for keyword in keywords:
                    cursor.execute('''
                        SELECT content FROM story_content
                        WHERE content LIKE ? AND category = 'plots'
                        LIMIT ?
                    ''', (f'%{keyword}%', max_results))
                    
                    results = cursor.fetchall()
                    for result in results:
                        if result[0] not in relevant_content['plots']:
                            relevant_content['plots'].append(result[0])
                
                # 查询重要事件
                for keyword in keywords:
                    cursor.execute('''
                        SELECT description FROM important_events
                        WHERE description LIKE ?
                        LIMIT ?
                    ''', (f'%{keyword}%', max_results))
                    
                    results = cursor.fetchall()
                    for result in results:
                        if result[0] not in relevant_content['events']:
                            relevant_content['events'].append(result[0])
                
                # 查询人物关系
                for keyword in keywords:
                    cursor.execute('''
                        SELECT description FROM relationships
                        WHERE description LIKE ? OR character_name LIKE ?
                        LIMIT ?
                    ''', (f'%{keyword}%', f'%{keyword}%', max_results))
                    
                    results = cursor.fetchall()
                    for result in results:
                        if result[0] not in relevant_content['relationships']:
                            relevant_content['relationships'].append(result[0])
            
            conn.close()
            
            # 过滤空结果
            filtered_content = {k: v for k, v in relevant_content.items() if v}
            
            if filtered_content:
                logger.debug(f"为角色 {avatar_name} 找到相关剧情内容: {len(filtered_content)} 个类别")
            
            return filtered_content
            
        except Exception as e:
            logger.error(f"查询相关剧情失败: {e}")
            return {}
    
    def get_all_story_content(self, avatar_name: str) -> Dict[str, List[str]]:
        """
        获取角色的所有剧情内容
        
        Args:
            avatar_name: 角色名称
            
        Returns:
            Dict[str, List[str]]: 所有剧情内容
        """
        try:
            db_path = self._get_story_db_path(avatar_name)
            
            if not os.path.exists(db_path):
                return {}
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            all_content = {
                'dialogues': [],
                'plots': [],
                'events': [],
                'relationships': [],
                'background': []
            }
            
            # 获取所有台词
            cursor.execute('SELECT dialogue FROM dialogues')
            results = cursor.fetchall()
            all_content['dialogues'] = [result[0] for result in results]
            
            # 获取所有剧情内容
            cursor.execute('SELECT content FROM story_content WHERE category = "plots"')
            results = cursor.fetchall()
            all_content['plots'] = [result[0] for result in results]
            
            # 获取所有重要事件
            cursor.execute('SELECT description FROM important_events')
            results = cursor.fetchall()
            all_content['events'] = [result[0] for result in results]
            
            # 获取所有人物关系
            cursor.execute('SELECT description FROM relationships')
            results = cursor.fetchall()
            all_content['relationships'] = [result[0] for result in results]
            
            # 获取背景信息
            cursor.execute('SELECT content FROM story_content WHERE category = "background"')
            results = cursor.fetchall()
            all_content['background'] = [result[0] for result in results]
            
            conn.close()
            
            return all_content
            
        except Exception as e:
            logger.error(f"获取所有剧情内容失败: {e}")
            return {}
    
    def get_random_dialogue(self, avatar_name: str, count: int = 1) -> List[str]:
        """
        获取随机台词
        
        Args:
            avatar_name: 角色名称
            count: 返回台词数量
            
        Returns:
            List[str]: 随机台词列表
        """
        try:
            db_path = self._get_story_db_path(avatar_name)
            
            if not os.path.exists(db_path):
                return []
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT dialogue FROM dialogues ORDER BY RANDOM() LIMIT ?', (count,))
            results = cursor.fetchall()
            
            conn.close()
            
            return [result[0] for result in results]
            
        except Exception as e:
            logger.error(f"获取随机台词失败: {e}")
            return []
    
    def format_story_context(self, story_content: Dict[str, List[str]], max_length: int = 1000) -> str:
        """
        格式化剧情内容为上下文字符串
        
        Args:
            story_content: 剧情内容字典
            max_length: 最大长度限制
            
        Returns:
            str: 格式化后的上下文
        """
        try:
            context_parts = []
            
            # 添加台词
            if story_content.get('dialogues'):
                context_parts.append("相关台词:")
                for dialogue in story_content['dialogues'][:3]:  # 最多3句台词
                    context_parts.append(f"- \"{dialogue}\"")
                context_parts.append("")
            
            # 添加剧情
            if story_content.get('plots'):
                context_parts.append("相关剧情:")
                for plot in story_content['plots'][:2]:  # 最多2个剧情
                    context_parts.append(f"- {plot}")
                context_parts.append("")
            
            # 添加重要事件
            if story_content.get('events'):
                context_parts.append("重要事件:")
                for event in story_content['events'][:2]:  # 最多2个事件
                    context_parts.append(f"- {event}")
                context_parts.append("")
            
            # 添加人物关系
            if story_content.get('relationships'):
                context_parts.append("人物关系:")
                for rel in story_content['relationships'][:2]:  # 最多2个关系
                    context_parts.append(f"- {rel}")
                context_parts.append("")
            
            # 合并并限制长度
            context = "\n".join(context_parts)
            
            if len(context) > max_length:
                context = context[:max_length] + "..."
            
            return context
            
        except Exception as e:
            logger.error(f"格式化剧情上下文失败: {e}")
            return ""
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 关键词列表
        """
        try:
            # 简单的关键词提取（可以后续优化）
            import re
            
            # 移除标点符号并分词
            words = re.findall(r'\b\w+\b', text.lower())
            
            # 过滤停用词（简单版本）
            stop_words = {'的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '没', '不', '也', '就', '都', '要', '会', '能', '可以', '什么', '怎么', '为什么', '哪里', '谁', 'when', 'where', 'what', 'how', 'why', 'who', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
            
            keywords = [word for word in words if len(word) > 1 and word not in stop_words]
            
            # 返回前5个关键词
            return keywords[:5]
            
        except Exception as e:
            logger.error(f"提取关键词失败: {e}")
            return []
    
    def refresh_story_data(self, avatar_name: str) -> bool:
        """
        刷新角色的剧情数据（重新分类和同步）
        
        Args:
            avatar_name: 角色名称
            
        Returns:
            bool: 刷新是否成功
        """
        try:
            # 重新分类剧情内容
            if not self.classifier.classify_story_content(avatar_name):
                logger.error(f"重新分类角色 {avatar_name} 剧情内容失败")
                return False
            
            # 重新同步到数据库
            from modules.newmemory.md_sync_service import MDSyncService
            sync_service = MDSyncService(self.root_dir)
            
            if not sync_service.sync_avatar_story(avatar_name):
                logger.error(f"重新同步角色 {avatar_name} 剧情数据失败")
                return False
            
            logger.info(f"角色 {avatar_name} 剧情数据刷新成功")
            return True
            
        except Exception as e:
            logger.error(f"刷新角色 {avatar_name} 剧情数据失败: {e}")
            return False

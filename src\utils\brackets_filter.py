#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
括号内容过滤模块
提供灵活的括号内容过滤功能，支持多种括号类型和过滤模式
"""

import re
import logging
from typing import List, Dict, Any
from dataclasses import dataclass
from src.config import config

logger = logging.getLogger(__name__)

@dataclass
class FilterResult:
    """过滤结果"""
    original_text: str
    filtered_text: str
    removed_count: int
    
class BracketsFilter:
    """括号过滤器"""
    
    def __init__(self):
        self.config = config.behavior.brackets_filtering
        
    def filter_text(self, text: str, source_type: str = "user_input") -> FilterResult:
        """
        过滤文本中的括号内容（简化版）
        
        Args:
            text: 要过滤的文本
            source_type: 文本来源类型 ("user_input" 或 "ai_response")
            
        Returns:
            FilterResult: 过滤结果
        """
        if not text or not self.config.enabled:
            return FilterResult(
                original_text=text,
                filtered_text=text,
                removed_count=0
            )
        
        try:
            # 根据配置选择要过滤的括号类型
            patterns = []
            if self.config.filter_round:
                patterns.append(r'\([^)]*\)')     # 英文圆括号
                patterns.append(r'（[^）]*）')     # 中文圆括号
            if self.config.filter_square:
                patterns.append(r'\[[^\]]*\]')    # 英文方括号
                patterns.append(r'【[^】]*】')     # 中文方括号
            if self.config.filter_curly:
                patterns.append(r'\{[^}]*\}')     # 花括号
            
            filtered_text = text
            removed_count = 0
            
            for pattern in patterns:
                matches = re.findall(pattern, filtered_text)
                removed_count += len(matches)
                
                if self.config.filter_mode == "remove":
                    # 直接删除
                    filtered_text = re.sub(pattern, '', filtered_text)
                elif self.config.filter_mode == "replace":
                    # 替换为[已过滤]
                    filtered_text = re.sub(pattern, '[已过滤]', filtered_text)
            
            # 清理多余的空格
            filtered_text = re.sub(r'\s+', ' ', filtered_text).strip()
            
            if removed_count > 0:
                mode_text = "删除" if self.config.filter_mode == "remove" else "替换"
                logger.info(f"括号过滤: {mode_text}了 {removed_count} 个括号内容")
            
            return FilterResult(
                original_text=text,
                filtered_text=filtered_text,
                removed_count=removed_count
            )
            
        except Exception as e:
            logger.error(f"括号过滤失败: {e}")
            return FilterResult(
                original_text=text,
                filtered_text=text,
                removed_count=0
            )

# 创建全局过滤器实例
brackets_filter = BracketsFilter()

def filter_brackets(text: str, source_type: str = "user_input") -> str:
    """
    便捷函数：过滤文本中的括号内容
    
    Args:
        text: 要过滤的文本
        source_type: 文本来源类型 ("user_input" 或 "ai_response")
        
    Returns:
        str: 过滤后的文本
    """
    result = brackets_filter.filter_text(text, source_type)
    return result.filtered_text


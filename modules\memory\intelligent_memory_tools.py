"""
智能记忆查询工具集
实现基于Function Calling的智能记忆查询机制
"""

import logging
import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

logger = logging.getLogger('main')

class IntelligentMemoryTools:
    """智能记忆查询工具集"""
    
    def __init__(self, memory_manager):
        """
        初始化智能记忆工具
        
        Args:
            memory_manager: 记忆管理器实例
        """
        self.memory_manager = memory_manager
        
        # 记忆相关关键词列表
        self.memory_keywords = [
            '记得', '还记得', '之前', '昨天', '前天', '上次', '那时候', '当时',
            '以前', '刚才', '刚刚', '之前说过', '提到过', '聊过', '说过'
        ]
        
        # 情感相关关键词
        self.emotion_keywords = [
            '开心', '高兴', '兴奋', '激动', '快乐', '喜悦',
            '难过', '伤心', '沮丧', '失落', '痛苦', '悲伤',
            '愤怒', '生气', '气愤', '恼火', '暴怒',
            '紧张', '焦虑', '担心', '害怕', '恐惧',
            '感动', '温暖', '感谢', '感激'
        ]
        
        # 关系相关关键词
        self.relationship_keywords = [
            '朋友', '同学', '同事', '家人', '父母', '爸爸', '妈妈',
            '兄弟', '姐妹', '哥哥', '弟弟', '姐姐', '妹妹',
            '男朋友', '女朋友', '恋人', '爱人', '老公', '老婆'
        ]

    def get_tools_definitions(self) -> List[Dict]:
        """获取工具定义列表，用于Function Calling"""
        return [
            {
                "type": "function",
                "function": {
                    "name": "search_emotional_memories",
                    "description": "查询与情感相关的重要记忆。当用户表达强烈情感或提到情感相关话题时使用。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "emotion_type": {
                                "type": "string",
                                "description": "情感类型，如：开心、难过、愤怒、紧张等"
                            },
                            "user_id": {
                                "type": "string", 
                                "description": "用户ID"
                            },
                            "avatar_name": {
                                "type": "string",
                                "description": "角色名称"
                            }
                        },
                        "required": ["emotion_type", "user_id", "avatar_name"]
                    }
                }
            },
            {
                "type": "function", 
                "function": {
                    "name": "search_character_background",
                    "description": "查询角色背景和设定信息。当需要保持角色一致性或回答关于角色的问题时使用。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query_type": {
                                "type": "string",
                                "description": "查询类型，如：personality（性格）、background（背景）、preferences（喜好）等"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            },
                            "avatar_name": {
                                "type": "string", 
                                "description": "角色名称"
                            }
                        },
                        "required": ["query_type", "user_id", "avatar_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_relationship_history", 
                    "description": "查询与用户的关系发展历程和重要互动记录。当涉及人际关系话题时使用。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "relationship_aspect": {
                                "type": "string",
                                "description": "关系方面，如：friendship（友谊）、interaction（互动）、shared_experiences（共同经历）等"
                            },
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            },
                            "avatar_name": {
                                "type": "string",
                                "description": "角色名称"
                            }
                        },
                        "required": ["relationship_aspect", "user_id", "avatar_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_recent_conversations",
                    "description": "查询最近的对话记录。当用户明确提到之前的对话或需要上下文连贯性时使用。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "time_range": {
                                "type": "string", 
                                "description": "时间范围，如：today（今天）、yesterday（昨天）、last_week（上周）等"
                            },
                            "limit": {
                                "type": "integer",
                                "description": "返回记录数量限制，默认10条",
                                "default": 10
                            },
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            },
                            "avatar_name": {
                                "type": "string",
                                "description": "角色名称"
                            }
                        },
                        "required": ["time_range", "user_id", "avatar_name"]
                    }
                }
            }
        ]

    def should_trigger_memory_query(self, user_message: str, current_context: List[Dict]) -> Dict[str, Any]:
        """
        智能判断是否需要查询记忆
        
        Args:
            user_message: 用户消息
            current_context: 当前对话上下文
            
        Returns:
            Dict: 包含判断结果和推荐工具的字典
        """
        # 1. 显性引用检测 - 最高优先级
        if self._has_explicit_memory_reference(user_message):
            return {
                "should_query": True,
                "reason": "explicit_reference",
                "confidence": 1.0,
                "recommended_tool": "search_recent_conversations"
            }
        
        # 2. 情感强度检测
        emotion_score = self._analyze_emotional_intensity(user_message)
        if emotion_score > 0.7:
            emotion_type = self._identify_emotion_type(user_message)
            return {
                "should_query": True,
                "reason": "high_emotion",
                "confidence": emotion_score,
                "recommended_tool": "search_emotional_memories",
                "emotion_type": emotion_type
            }
        
        # 3. 角色一致性需求检测
        character_need = self._assess_character_consistency_need(user_message)
        if character_need > 0.6:
            return {
                "should_query": True,
                "reason": "character_consistency",
                "confidence": character_need,
                "recommended_tool": "search_character_background"
            }
        
        # 4. 关系话题检测
        relationship_score = self._detect_relationship_topic(user_message)
        if relationship_score > 0.5:
            return {
                "should_query": True,
                "reason": "relationship_topic", 
                "confidence": relationship_score,
                "recommended_tool": "search_relationship_history"
            }
        
        # 5. 上下文不完整性检测
        context_sufficiency = self._evaluate_context_sufficiency(user_message, current_context)
        if context_sufficiency < 0.4:
            return {
                "should_query": True,
                "reason": "insufficient_context",
                "confidence": 1.0 - context_sufficiency,
                "recommended_tool": "search_recent_conversations"
            }
        
        # 不需要查询记忆
        return {
            "should_query": False,
            "reason": "sufficient_context",
            "confidence": context_sufficiency
        }

    def _has_explicit_memory_reference(self, message: str) -> bool:
        """检测是否有显性记忆引用"""
        for keyword in self.memory_keywords:
            if keyword in message:
                return True
        return False

    def _analyze_emotional_intensity(self, message: str) -> float:
        """分析情感强度 (0-1)"""
        # 简单的情感词汇匹配计分
        emotion_count = 0
        for keyword in self.emotion_keywords:
            if keyword in message:
                emotion_count += 1
        
        # 检测标点符号强度
        punctuation_intensity = 0
        if '！' in message or '!' in message:
            punctuation_intensity += 0.2
        if '？？' in message or '??' in message:
            punctuation_intensity += 0.1
        if '...' in message or '…' in message:
            punctuation_intensity += 0.1
        
        # 综合评分
        total_score = min(emotion_count * 0.3 + punctuation_intensity, 1.0)
        return total_score

    def _identify_emotion_type(self, message: str) -> str:
        """识别情感类型"""
        # 简单的关键词匹配
        if any(word in message for word in ['开心', '高兴', '兴奋', '激动', '快乐']):
            return "positive"
        elif any(word in message for word in ['难过', '伤心', '沮丧', '失落']):
            return "sad"
        elif any(word in message for word in ['愤怒', '生气', '气愤', '恼火']):
            return "angry"
        elif any(word in message for word in ['紧张', '焦虑', '担心', '害怕']):
            return "anxious"
        else:
            return "mixed"

    def _assess_character_consistency_need(self, message: str) -> float:
        """评估角色一致性需求 (0-1)"""
        # 检测角色相关问题
        character_questions = [
            '你是谁', '你叫什么', '你的名字', '自我介绍',
            '你喜欢', '你讨厌', '你的爱好', '你的兴趣',
            '你会', '你能', '你的能力', '你的特长'
        ]
        
        for question in character_questions:
            if question in message:
                return 0.8
        
        return 0.0

    def _detect_relationship_topic(self, message: str) -> float:
        """检测关系话题 (0-1)"""
        relationship_count = 0
        for keyword in self.relationship_keywords:
            if keyword in message:
                relationship_count += 1
        
        return min(relationship_count * 0.4, 1.0)

    def _evaluate_context_sufficiency(self, message: str, current_context: List[Dict]) -> float:
        """评估上下文充分性 (0-1)"""
        # 简单回应列表（这些不需要查询记忆）
        simple_responses = [
            '嗯', '好', '行', '可以', '好的', '对', '是的', '不是',
            '嗯嗯', '好好', '行行', '好吧', '真的', '是啊',
            '哈哈', '呵呀', '汗', '没事', '无聊', '干嘛',
            '…', '...', '你好', '再见', 'ok', 'OK', '没问题'
        ]
        
        # 如果是简单回应，认为上下文充分
        if message.strip() in simple_responses or len(message.strip()) <= 2:
            return 0.9  # 高充分性，不需要查询记忆
        
        # 简单实现：基于上下文长度和代词使用情况
        context_length = len(current_context) if current_context else 0
        
        # 检测代词或指代词
        pronouns = ['他', '她', '它', '这个', '那个', '这样', '那样']
        pronoun_count = sum(1 for pronoun in pronouns if pronoun in message)
        
        # 上下文充分性评分
        if context_length >= 10:
            base_score = 0.8
        elif context_length >= 5:
            base_score = 0.6
        else:
            base_score = 0.3
        
        # 如果有很多代词但上下文不足，降低充分性评分
        if pronoun_count > 2 and context_length < 5:
            base_score *= 0.5
        
        return base_score

    # 工具执行方法
    def search_emotional_memories(self, emotion_type: str, user_id: str, avatar_name: str) -> str:
        """查询情感相关记忆"""
        try:
            # 使用现有的记忆管理器获取核心记忆
            core_memory = self.memory_manager.get_core_memory(avatar_name, user_id)
            
            # 简单的情感相关内容过滤
            if core_memory and any(emotion in core_memory for emotion in self.emotion_keywords):
                return f"=== 情感相关记忆 ===\n{core_memory}\n=== 记忆结束 ==="
            
            return ""
        except Exception as e:
            logger.error(f"查询情感记忆失败: {e}")
            return ""

    def search_character_background(self, query_type: str, user_id: str, avatar_name: str) -> str:
        """查询角色背景信息"""
        try:
            # 获取核心记忆中的角色信息
            core_memory = self.memory_manager.get_core_memory(avatar_name, user_id)
            
            if core_memory:
                return f"=== 角色背景信息 ===\n{core_memory}\n=== 信息结束 ==="
            
            return ""
        except Exception as e:
            logger.error(f"查询角色背景失败: {e}")
            return ""

    def search_relationship_history(self, relationship_aspect: str, user_id: str, avatar_name: str) -> str:
        """查询关系发展历程"""
        try:
            # 获取最近的对话记录，重点关注关系相关内容
            recent_conversations = self.memory_manager.get_recent_context(avatar_name, user_id, context_size=15)
            
            if recent_conversations:
                # 过滤关系相关的对话
                relationship_conversations = []
                for conv in recent_conversations:
                    content = conv.get('content', '')
                    if any(keyword in content for keyword in self.relationship_keywords):
                        relationship_conversations.append(conv)
                
                if relationship_conversations:
                    formatted_conversations = []
                    for conv in relationship_conversations:
                        role = "我" if conv.get('role') == 'assistant' else "用户"
                        formatted_conversations.append(f"{role}: {conv.get('content', '')}")
                    
                    return f"=== 关系相关对话 ===\n" + "\n".join(formatted_conversations) + "\n=== 对话结束 ==="
            
            return ""
        except Exception as e:
            logger.error(f"查询关系历程失败: {e}")
            return ""

    def search_recent_conversations(self, time_range: str, limit: int, user_id: str, avatar_name: str) -> str:
        """查询最近对话记录"""
        try:
            # 根据时间范围调整查询数量
            if time_range == "today":
                query_limit = min(limit, 10)
            elif time_range == "yesterday":
                query_limit = min(limit, 15)
            else:
                query_limit = min(limit, 20)
            
            recent_conversations = self.memory_manager.get_recent_context(avatar_name, user_id, context_size=query_limit)
            
            if recent_conversations:
                formatted_conversations = []
                for conv in recent_conversations:
                    role = "我" if conv.get('role') == 'assistant' else "用户"
                    formatted_conversations.append(f"{role}: {conv.get('content', '')}")
                
                return f"=== 最近对话记录 ===\n" + "\n".join(formatted_conversations) + "\n=== 对话结束 ==="
            
            return ""
        except Exception as e:
            logger.error(f"查询最近对话失败: {e}")
            return ""

    def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """执行工具调用"""
        try:
            if tool_name == "search_emotional_memories":
                return self.search_emotional_memories(**arguments)
            elif tool_name == "search_character_background":
                return self.search_character_background(**arguments)
            elif tool_name == "search_relationship_history":
                return self.search_relationship_history(**arguments)
            elif tool_name == "search_recent_conversations":
                return self.search_recent_conversations(**arguments)
            else:
                logger.warning(f"未知的工具名称: {tool_name}")
                return ""
        except Exception as e:
            logger.error(f"执行工具 {tool_name} 失败: {e}")
            return ""
"""
数据库记忆服务
基于SQLite数据库的新记忆系统实现
"""

import os
import sqlite3
import logging
import json
import time
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from src.services.ai.llm_service import LLMService

# APScheduler导入（带异常处理）
try:
    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.triggers.cron import CronTrigger
    import atexit
    SCHEDULER_AVAILABLE = True
except ImportError:
    print("警告：APScheduler未安装，定时任务功能将被禁用。请安装: pip install apscheduler")
    SCHEDULER_AVAILABLE = False
    BackgroundScheduler = None
    CronTrigger = None
    atexit = None

logger = logging.getLogger('main')

class DatabaseMemoryService:
    """
    数据库记忆服务
    功能：
    1. 基于SQLite的记忆存储
    2. 自动记忆总结和归档
    3. 智能记忆查询
    4. 与MD文件同步
    """
    
    def __init__(self, root_dir: str, api_key: str, base_url: str, model: str, max_token: int, temperature: float, max_groups: int = 10):
        self.root_dir = root_dir
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.max_token = max_token
        self.temperature = temperature
        self.max_groups = max_groups
        self.conversation_count = {}

        # 缓存相关配置
        self.cache = {}  # 缓存存储
        self.cache_expire_time = 300  # 缓存过期时间（5分钟）
        self.max_cache_entries = 100  # 最大缓存条目数

        # 初始化LLM服务
        self.llm_service = LLMService(
            api_key=api_key,
            base_url=base_url,
            model=model,
            max_token=max_token,
            temperature=temperature,
            max_groups=max_groups
        )

        # 初始化数据库表
        self._initialize_database_tables()
        
        # 加载对话计数
        self._load_conversation_counts()
        
        # 初始化定时任务调度器（仅在APScheduler可用时）
        if SCHEDULER_AVAILABLE and BackgroundScheduler is not None:
            self.scheduler = BackgroundScheduler()
            self._setup_daily_summary_task()
            self.scheduler.start()
            
            # 注册关闭时的清理函数
            if atexit is not None:
                atexit.register(self._cleanup_scheduler)
            logger.info("每日记忆总结定时任务已启动")
        else:
            self.scheduler = None
            logger.warning("定时任务功能不可用，将使用手动检查方式执行每日记忆总结")
            # 启动手动检查任务
            self._start_manual_daily_check()

    def _start_manual_daily_check(self):
        """启动手动每日检查任务"""
        try:
            import threading
            import time
            
            def daily_check_worker():
                while True:
                    try:
                        # 检查是否到了执行时间（每天凌晨0点）
                        now = datetime.now()
                        if now.hour == 0 and now.minute < 5:  # 0:00-0:05之间执行
                            logger.info("开始执行手动每日记忆检查...")
                            self._daily_summary_job()
                            # 等待到明天再检查
                            time.sleep(24 * 60 * 60 - 5 * 60)  # 24小时减5分钟
                        else:
                            # 每分钟检查一次
                            time.sleep(60)
                    except Exception as e:
                        logger.error(f"手动每日检查任务出错: {e}")
                        time.sleep(300)  # 出错后5分钟再重试
            
            # 启动后台线程
            check_thread = threading.Thread(target=daily_check_worker, daemon=True)
            check_thread.start()
            logger.info("手动每日检查任务已启动")
            
        except Exception as e:
            logger.error(f"启动手动每日检查任务失败: {e}")

    def _get_cache_key(self, avatar_name: str, user_id: str) -> str:
        """生成缓存键"""
        return f"{avatar_name}_{user_id}"

    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not cache_entry:
            return False
        current_time = time.time()
        return current_time - cache_entry.get('timestamp', 0) < self.cache_expire_time

    def _clean_expired_cache(self):
        """清理过期的缓存条目"""
        current_time = time.time()
        expired_keys = []

        for key, entry in self.cache.items():
            if current_time - entry.get('timestamp', 0) >= self.cache_expire_time:
                expired_keys.append(key)

        for key in expired_keys:
            del self.cache[key]

        # 如果缓存条目过多，删除最旧的条目
        if len(self.cache) > self.max_cache_entries:
            sorted_cache = sorted(self.cache.items(), key=lambda x: x[1].get('timestamp', 0))
            for key, _ in sorted_cache[:len(self.cache) - self.max_cache_entries]:
                del self.cache[key]

    def _update_cache(self, avatar_name: str, user_id: str, short_memory: List[Dict], core_memory: str):
        """更新缓存"""
        cache_key = self._get_cache_key(avatar_name, user_id)
        self.cache[cache_key] = {
            'short_memory': short_memory,
            'core_memory': core_memory,
            'timestamp': time.time()
        }

        # 清理过期缓存
        self._clean_expired_cache()

    def _get_from_cache(self, avatar_name: str, user_id: str) -> Optional[Dict]:
        """从缓存获取数据"""
        cache_key = self._get_cache_key(avatar_name, user_id)
        cache_entry = self.cache.get(cache_key)

        if cache_entry and self._is_cache_valid(cache_entry):
            return cache_entry

        # 缓存无效，删除
        if cache_key in self.cache:
            del self.cache[cache_key]

        return None

    def _invalidate_cache(self, avatar_name: str, user_id: str):
        """使缓存失效"""
        cache_key = self._get_cache_key(avatar_name, user_id)
        if cache_key in self.cache:
            del self.cache[cache_key]
    
    def _get_avatar_newmemory_dir(self, avatar_name: str) -> str:
        """获取角色新记忆目录"""
        memory_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "memory")
        os.makedirs(memory_dir, exist_ok=True)
        os.makedirs(os.path.join(memory_dir, "db"), exist_ok=True)
        os.makedirs(os.path.join(memory_dir, "md"), exist_ok=True)
        os.makedirs(os.path.join(memory_dir, "archived_memory"), exist_ok=True)
        return memory_dir
    
    def _get_core_memory_db_path(self, avatar_name: str) -> str:
        """获取核心记忆数据库路径"""
        memory_dir = self._get_avatar_newmemory_dir(avatar_name)
        return os.path.join(memory_dir, "db", "core_memory.db")
    
    def _get_short_memory_db_path(self, avatar_name: str) -> str:
        """获取短期记忆数据库路径"""
        memory_dir = self._get_avatar_newmemory_dir(avatar_name)
        return os.path.join(memory_dir, "db", "short_memory.db")
    
    def add_conversation(self, avatar_name: str, user_message: str, bot_reply: str, user_id: str, is_system_message: bool = False):
        """
        添加对话到短期记忆数据库（保存当天所有对话）
        
        Args:
            avatar_name: 角色名称
            user_message: 用户消息
            bot_reply: 机器人回复
            user_id: 用户ID
            is_system_message: 是否为系统消息
        """
        # 跳过系统消息和错误消息
        if is_system_message or bot_reply.startswith("Error:"):
            return
        
        try:
            # 获取数据库路径
            db_path = self._get_short_memory_db_path(avatar_name)
            
            # 连接数据库
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取当前时间信息
            now = datetime.now()
            current_week = now.isocalendar()[1]
            date_key = now.strftime("%Y-%m-%d")  # 日期键，用于按天组织数据
            
            # 插入对话记录（新增 date_key 字段）
            cursor.execute('''
                INSERT INTO short_memory (user_id, user_message, bot_reply, timestamp, week_number, date_key)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, user_message, bot_reply, now, current_week, date_key))
            
            conn.commit()
            conn.close()
            
            # 更新对话计数（保持原有逻辑）
            conversation_key = f"{avatar_name}_{user_id}"
            if conversation_key not in self.conversation_count:
                self.conversation_count[conversation_key] = 0
                logger.info(f"初始化对话计数器: {conversation_key}")
            
            self.conversation_count[conversation_key] += 1
            logger.debug(f"对话计数更新: {conversation_key} = {self.conversation_count[conversation_key]}")
            
            # 保存对话计数到数据库
            self._save_conversation_count(avatar_name, user_id, self.conversation_count[conversation_key])
            
            # 每10轮对话更新一次核心记忆（保持原有逻辑）
            if self.conversation_count[conversation_key] >= 10:
                logger.info(f"角色 {avatar_name} 为用户 {user_id} 达到10轮对话，开始更新核心记忆")
                context = self.get_recent_context(avatar_name, user_id)
                logger.info(f"获取到上下文长度: {len(context)} 条消息")
                if len(context) == 0:
                    logger.warning(f"警告：获取到的上下文为空，跳过核心记忆更新")
                else:
                    success = self.update_core_memory(avatar_name, user_id, context)
                    if success:
                        logger.info(f"核心记忆更新成功")
                    else:
                        logger.warning(f"核心记忆更新失败")
                self.conversation_count[conversation_key] = 0
                # 保存重置后的计数
                self._save_conversation_count(avatar_name, user_id, 0)
            
            # 检查是否需要归档（每周检查一次）
            self._check_and_archive_memory(avatar_name, user_id)
            
            # 每次添加对话后同步短期记忆到MD文件（确保MD文件最新）
            self._sync_short_memory_to_md(avatar_name, user_id)

            # 使缓存失效，强制下次查询时重新从数据库获取
            self._invalidate_cache(avatar_name, user_id)

            logger.info(f"对话已添加到数据库: 角色={avatar_name}, 用户ID={user_id}, 日期={date_key}")

        except Exception as e:
            logger.error(f"添加对话到数据库失败: {e}")

    def get_comprehensive_memory(self, avatar_name: str, user_id: str, short_memory_limit: int = 50) -> Dict:
        """
        获取综合记忆数据（短期记忆 + 核心记忆）
        优先从缓存获取，缓存未命中时从数据库查询

        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            short_memory_limit: 短期记忆查询数量限制

        Returns:
            Dict: 包含短期记忆和核心记忆的字典
        """
        try:
            # 先尝试从缓存获取
            cached_data = self._get_from_cache(avatar_name, user_id)
            if cached_data:
                logger.info(f"✓ 缓存命中 - 角色={avatar_name}, 用户ID={user_id}, 短期记忆={len(cached_data['short_memory'])}条")
                return {
                    'short_memory': cached_data['short_memory'],
                    'core_memory': cached_data['core_memory']
                }

            # 缓存未命中，从数据库查询
            logger.info(f"✗ 缓存未命中，查询数据库 - 角色={avatar_name}, 用户ID={user_id}")

            # 查询短期记忆（最近50条）
            short_memory = self._get_recent_short_memory(avatar_name, user_id, short_memory_limit)

            # 查询全部核心记忆
            core_memory = self._get_all_core_memory(avatar_name, user_id)

            # 更新缓存
            self._update_cache(avatar_name, user_id, short_memory, core_memory)
            logger.info(f"✓ 缓存已更新 - 角色={avatar_name}, 用户ID={user_id}, 短期记忆={len(short_memory)}条")

            return {
                'short_memory': short_memory,
                'core_memory': core_memory
            }

        except Exception as e:
            logger.error(f"获取综合记忆数据失败: {e}")
            return {
                'short_memory': [],
                'core_memory': ""
            }

    def _get_recent_short_memory(self, avatar_name: str, user_id: str, limit: int = 50) -> List[Dict]:
        """
        获取最近的短期记忆

        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            limit: 查询数量限制

        Returns:
            List[Dict]: 短期记忆列表
        """
        try:
            db_path = self._get_short_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return []

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询最近的对话记录
            cursor.execute('''
                SELECT user_message, bot_reply, timestamp FROM short_memory
                WHERE user_id = ? OR user_id = '*'
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (user_id, limit))

            results = cursor.fetchall()
            conn.close()

            # 转换为标准格式
            memory_list = []
            for user_message, bot_reply, timestamp in reversed(results):  # 反转以保持时间顺序
                memory_list.append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": timestamp
                })
                memory_list.append({
                    "role": "assistant",
                    "content": bot_reply,
                    "timestamp": timestamp
                })

            return memory_list

        except Exception as e:
            logger.error(f"获取短期记忆失败: {e}")
            return []

    def _get_all_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        获取全部核心记忆

        Args:
            avatar_name: 角色名称
            user_id: 用户ID

        Returns:
            str: 核心记忆内容
        """
        try:
            db_path = self._get_core_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return ""

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询用户的全部核心记忆
            cursor.execute('''
                SELECT content, category, importance FROM core_memory
                WHERE user_id = ?
                ORDER BY importance DESC, updated_at DESC
            ''', (user_id,))

            results = cursor.fetchall()
            conn.close()

            if results:
                # 按类别和重要性组织核心记忆内容
                memory_sections = []
                for content, category, importance in results:
                    memory_sections.append(f"[{category}] {content}")

                return "\n\n".join(memory_sections)

            return ""

        except Exception as e:
            logger.error(f"获取核心记忆失败: {e}")
            return ""

    def get_recent_context(self, avatar_name: str, user_id: str, context_size: Optional[int] = None) -> List[Dict]:
        """
        获取最近的对话上下文
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            context_size: 上下文大小（轮数）
        
        Returns:
            List[Dict]: 对话上下文列表
        """
        try:
            if context_size is None:
                context_size = self.max_groups
            
            db_path = self._get_short_memory_db_path(avatar_name)
            
            if not os.path.exists(db_path):
                return []
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询最近的对话记录，支持特定用户ID和通用用户ID（'*'）
            # 注意：context_size 是对话轮数，每轮对话包含2条记录（用户消息和机器人回复）
            # 所以需要查询 context_size * 2 条记录
            cursor.execute('''
                SELECT user_message, bot_reply FROM short_memory
                WHERE user_id = ? OR user_id = '*'
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (user_id, context_size * 2))
            
            results = cursor.fetchall()
            conn.close()
            
            # 转换为LLM接口要求的格式
            context = []
            for user_message, bot_reply in reversed(results):  # 反转以保持时间顺序
                context.append({"role": "user", "content": user_message})
                context.append({"role": "assistant", "content": bot_reply})
            
            return context
            
        except Exception as e:
            logger.error(f"获取最近对话上下文失败: {e}")
            return []
    
    def get_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        获取核心记忆内容
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
        
        Returns:
            str: 核心记忆内容
        """
        try:
            db_path = self._get_core_memory_db_path(avatar_name)
            
            if not os.path.exists(db_path):
                return ""
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在且结构正常
            try:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='core_memory';")
                table_exists = cursor.fetchone()
                
                if not table_exists:
                    logger.warning(f"核心记忆表不存在，尝试从md文件重建 - 角色={avatar_name}")
                    conn.close()
                    return self._rebuild_core_memory_from_md(avatar_name, user_id)
                
                # 检查表结构
                cursor.execute("PRAGMA table_info(core_memory);")
                schema_info = cursor.fetchone()
                if not schema_info:
                    logger.warning(f"核心记忆表结构损坏，尝试从md文件重建 - 角色={avatar_name}")
                    conn.close()
                    return self._rebuild_core_memory_from_md(avatar_name, user_id)
                
                # 查询用户的核心记忆
                cursor.execute('''
                    SELECT content FROM core_memory
                    WHERE user_id = ?
                    ORDER BY importance DESC, updated_at DESC
                ''', (user_id,))
                
                results = cursor.fetchall()
                conn.close()
                
                if results:
                    # 合并所有核心记忆内容
                    memory_content = "\n\n".join([result[0] for result in results])
                    return memory_content
                
                return ""
                
            except sqlite3.OperationalError as e:
                logger.error(f"数据库表损坏: {e}，尝试从md文件重建 - 角色={avatar_name}")
                conn.close()
                return self._rebuild_core_memory_from_md(avatar_name, user_id)
            
        except Exception as e:
            logger.error(f"获取核心记忆失败: {e}")
            return ""
    
    def update_core_memory(self, avatar_name: str, user_id: str, context: List[Dict]) -> bool:
        """
        更新核心记忆（智能模式：初始生成或增量追加）
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            context: 对话上下文
        
        Returns:
            bool: 更新是否成功
        """
        try:
            if not context:
                return False
            
            # 获取现有核心记忆
            existing_memory = self.get_core_memory(avatar_name, user_id)
            
            # 格式化对话上下文
            formatted_context = self._format_context_for_memory(context)
            
            # 检查是否为初始状态（只有模板内容，无实际记忆）
            if self._is_initial_memory_state(existing_memory):
                logger.info(f"检测到初始记忆状态，使用初始生成模式 - 角色={avatar_name}, 用户={user_id}")
                # 使用初始记忆生成模式
                prompt = self._build_initial_memory_prompt(formatted_context)
                new_memory = self._generate_initial_memory(prompt, context, user_id)
                
                if not new_memory or 'Error' in new_memory:
                    logger.warning(f"初始记忆生成失败，保留模板。返回值: {new_memory}")
                    return False
                
                updated_memory = new_memory
            else:
                # 使用增量追加模式
                prompt = self._build_incremental_memory_prompt(existing_memory, formatted_context)
                
                # 调用LLM生成增量记忆内容
                logger.info("开始调用LLM生成增量记忆内容...")
                incremental_memory = self._generate_incremental_memory(prompt, existing_memory, context, user_id)
                
                logger.info(f"LLM返回的增量记忆: {incremental_memory}")
                
                if not incremental_memory or 'Error' in incremental_memory:
                    logger.warning(f"生成增量记忆失败，保留原有记忆。返回值: {incremental_memory}")
                    return False
                
                # 如果LLM返回"无需更新"，则保持原样
                if "无需更新" in incremental_memory or "无新信息" in incremental_memory:
                    logger.info(f"角色 {avatar_name} 用户 {user_id} 无需更新核心记忆")
                    return True
                
                # 处理[新增信息]前缀，提取实际的新记忆内容
                processed_memory = self._extract_incremental_content(incremental_memory)
                
                if not processed_memory:
                    logger.warning("提取增量记忆内容失败，保留原有记忆")
                    return False
                
                # 合并现有记忆和增量记忆
                updated_memory = self._merge_core_memory(existing_memory, processed_memory)
            
            # 保存到数据库（使用事务确保安全）
            db_path = self._get_core_memory_db_path(avatar_name)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                # 先备份现有记忆（安全机制）
                cursor.execute('''
                    INSERT INTO core_memory (user_id, content, category, importance)
                    VALUES (?, ?, ?, ?)
                ''', (f"{user_id}_backup_{int(time.time())}", existing_memory, 'backup', 0))
                
                # 更新核心记忆（增量追加而非删除）
                cursor.execute('''
                    UPDATE core_memory 
                    SET content = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ?
                ''', (updated_memory, user_id))
                
                # 如果更新失败（记录不存在），则插入新记录
                if cursor.rowcount == 0:
                    cursor.execute('''
                        INSERT INTO core_memory (user_id, content, category, importance)
                        VALUES (?, ?, ?, ?)
                    ''', (user_id, updated_memory, 'general', 1))
                
                conn.commit()
                logger.info(f"核心记忆增量更新成功: 角色={avatar_name}, 用户ID={user_id}")
                
            except Exception as e:
                conn.rollback()
                logger.error(f"数据库操作失败，已回滚: {e}")
                return False
            finally:
                conn.close()
            
            # 同步到MD文件
            self._sync_core_memory_to_md(avatar_name, user_id, updated_memory)

            # 使缓存失效，强制下次查询时重新从数据库获取
            self._invalidate_cache(avatar_name, user_id)

            return True
            
        except Exception as e:
            logger.error(f"更新核心记忆失败: {e}")
            return False
    
    def restore_core_memory_from_backup(self, avatar_name: str, user_id: str, backup_timestamp: int = None) -> bool:
        """
        从备份恢复核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            backup_timestamp: 备份时间戳，如果为None则恢复最新备份
        
        Returns:
            bool: 恢复是否成功
        """
        try:
            db_path = self._get_core_memory_db_path(avatar_name)
            if not os.path.exists(db_path):
                logger.error(f"数据库文件不存在: {db_path}")
                return False
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查找备份记录
            if backup_timestamp:
                backup_user_id = f"{user_id}_backup_{backup_timestamp}"
            else:
                # 恢复最新备份
                cursor.execute('''
                    SELECT user_id, content FROM core_memory 
                    WHERE user_id LIKE ? 
                    ORDER BY user_id DESC 
                    LIMIT 1
                ''', (f"{user_id}_backup_%",))
                backup_record = cursor.fetchone()
                
                if not backup_record:
                    logger.warning(f"未找到用户 {user_id} 的备份记录")
                    return False
                
                backup_user_id, backup_content = backup_record
            
            # 获取备份内容
            cursor.execute('SELECT content FROM core_memory WHERE user_id = ?', (backup_user_id,))
            backup_record = cursor.fetchone()
            
            if not backup_record:
                logger.error(f"指定的备份记录不存在: {backup_user_id}")
                return False
            
            backup_content = backup_record[0]
            
            # 恢复到主记录
            cursor.execute('''
                UPDATE core_memory 
                SET content = ?, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ?
            ''', (backup_content, user_id))
            
            if cursor.rowcount == 0:
                # 如果主记录不存在，创建新记录
                cursor.execute('''
                    INSERT INTO core_memory (user_id, content, category, importance)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, backup_content, 'general', 1))
            
            conn.commit()
            conn.close()
            
            logger.info(f"成功恢复用户 {user_id} 的核心记忆从备份 {backup_user_id}")
            
            # 同步到MD文件
            self._sync_core_memory_to_md(avatar_name, user_id, backup_content)
            
            # 使缓存失效
            self._invalidate_cache(avatar_name, user_id)
            
            return True
            
        except Exception as e:
            logger.error(f"恢复核心记忆失败: {e}")
            return False

    def cleanup_old_backups(self, avatar_name: str, keep_count: int = 5) -> bool:
        """
        清理旧的备份记录，只保留最新的几个
        
        Args:
            avatar_name: 角色名称
            keep_count: 保留的备份数量
        
        Returns:
            bool: 清理是否成功
        """
        try:
            db_path = self._get_core_memory_db_path(avatar_name)
            if not os.path.exists(db_path):
                return True
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取所有备份记录
            cursor.execute('''
                SELECT user_id FROM core_memory 
                WHERE user_id LIKE '%_backup_%' 
                AND category = 'backup'
                ORDER BY user_id DESC
            ''')
            
            all_backups = cursor.fetchall()
            
            # 删除超过保留数量的备份
            if len(all_backups) > keep_count:
                backups_to_delete = all_backups[keep_count:]
                for backup in backups_to_delete:
                    cursor.execute('DELETE FROM core_memory WHERE user_id = ?', (backup[0],))
            
            conn.commit()
            conn.close()
            
            logger.info(f"清理了 {len(backups_to_delete)} 个旧备份记录")
            return True
            
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")
            return False

    def update_core_memory_content(self, avatar_name: str, user_id: str, memory_content: str) -> bool:
        """
        直接更新核心记忆内容（安全模式）
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            memory_content: 新的记忆内容
            
        Returns:
            bool: 更新是否成功
        """
        try:
            logger.info(f"数据库记忆系统：安全更新核心记忆 - 角色: {avatar_name}, 用户: {user_id}")
            
            # 获取现有记忆用于备份
            existing_memory = self.get_core_memory(avatar_name, user_id)
            
            # 保存到数据库（使用事务确保安全）
            db_path = self._get_core_memory_db_path(avatar_name)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            try:
                # 先备份现有记忆（安全机制）
                if existing_memory:
                    cursor.execute('''
                        INSERT INTO core_memory (user_id, content, category, importance)
                        VALUES (?, ?, ?, ?)
                    ''', (f"{user_id}_backup_{int(time.time())}", existing_memory, 'backup', 0))
                
                # 更新核心记忆（使用UPDATE而不是DELETE+INSERT）
                cursor.execute('''
                    UPDATE core_memory 
                    SET content = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ?
                ''', (memory_content, user_id))
                
                # 如果更新失败（记录不存在），则插入新记录
                if cursor.rowcount == 0:
                    cursor.execute('''
                        INSERT INTO core_memory (user_id, content, category, importance)
                        VALUES (?, ?, ?, ?)
                    ''', (user_id, memory_content, 'general', 1))
                
                conn.commit()
                
                # 清理旧备份（只保留最新5个）
                self.cleanup_old_backups(avatar_name, 5)
                
            except Exception as e:
                conn.rollback()
                logger.error(f"数据库操作失败，已回滚: {e}")
                return False
            finally:
                conn.close()
            
            # 同步到MD文件
            self._sync_core_memory_to_md(avatar_name, user_id, memory_content)
            
            # 使缓存失效
            self._invalidate_cache(avatar_name, user_id)
            
            logger.info(f"已安全更新角色 {avatar_name} 用户 {user_id} 的核心记忆")
            return True
            
        except Exception as e:
            logger.error(f"直接更新核心记忆失败: {str(e)}")
            return False

    def correct_memory(self, avatar_name: str, user_id: str, wrong_memory: str, correct_memory: str) -> bool:
        """
        修正核心记忆中的错误信息
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            wrong_memory: 错误的记忆内容
            correct_memory: 正确的记忆内容
            
        Returns:
            bool: 修正是否成功
        """
        try:
            logger.info(f"数据库记忆系统：开始修正记忆 - 角色: {avatar_name}, 用户: {user_id}")
            logger.info(f"错误记忆: {wrong_memory}")
            logger.info(f"正确记忆: {correct_memory}")
            
            # 获取现有核心记忆
            existing_memory = self.get_core_memory(avatar_name, user_id)
            
            if not existing_memory:
                logger.warning(f"未找到用户 {user_id} 的核心记忆")
                return False
            
            # 检查错误记忆是否存在于现有记忆中
            if wrong_memory not in existing_memory:
                logger.warning(f"错误记忆 '{wrong_memory}' 未在现有核心记忆中找到")
                logger.info(f"现有核心记忆: {existing_memory}")
                return False
            
            # 替换错误记忆为正确记忆
            new_memory = existing_memory.replace(wrong_memory, correct_memory)
            
            # 保存到数据库
            db_path = self._get_core_memory_db_path(avatar_name)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 删除旧的核心记忆
            cursor.execute('DELETE FROM core_memory WHERE user_id = ?', (user_id,))
            
            # 插入新的核心记忆
            cursor.execute('''
                INSERT INTO core_memory (user_id, content, category, importance)
                VALUES (?, ?, ?, ?)
            ''', (user_id, new_memory, 'general', 1))
            
            conn.commit()
            conn.close()
            
            # 同步到MD文件
            self._sync_core_memory_to_md(avatar_name, user_id, new_memory)
            
            # 使缓存失效，强制下次查询时重新从数据库获取
            self._invalidate_cache(avatar_name, user_id)
            
            logger.info(f"核心记忆修正成功: 角色={avatar_name}, 用户ID={user_id}")
            logger.info(f"修正后记忆: {new_memory}")
            return True
            
        except Exception as e:
            logger.error(f"修正核心记忆失败: {e}")
            return False
    
    def _build_memory_prompt(self, existing_memory: str = "", context: str = "") -> str:
        """构建记忆生成提示词并替换所有变量"""
        prompt_path = ""  # 初始化以避免作用域问题
        
        try:
            prompt_path = os.path.join(self.root_dir, 'data/base/memory.md')

            # 检查文件是否存在
            if not os.path.exists(prompt_path):
                logger.warning(f"记忆提示词文件不存在: {prompt_path}")
                return "请根据对话内容生成简洁的核心记忆摘要。"

            with open(prompt_path, 'r', encoding='utf-8') as f:
                prompt_template = f.read()

            # 获取当前时间信息
            from datetime import datetime
            now = datetime.now()
            current_date = now.strftime("%m月%d日")  # 简短格式：8月16日
            current_time = now.strftime("%H:%M")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(now.strftime("%A"), now.strftime("%A"))

            # 替换所有变量，使用安全的格式化方法
            try:
                prompt = prompt_template.format(
                    current_date=current_date,
                    current_time=current_time,
                    weekday=weekday,
                    existing_core_memory=existing_memory,
                    context=context
                )
                return prompt
            except KeyError as ke:
                logger.error(f"记忆提示词模板中存在未定义的变量: {ke}")
                # 如果格式化失败，返回安全的默认提示词，而不是原始模板
                return f"""请根据对话内容生成简洁的核心记忆摘要。

现有核心记忆：
{existing_memory}

对话上下文：
{context}

请生成新的核心记忆："""
            except ValueError as ve:
                logger.error(f"记忆提示词模板格式错误: {ve}")
                # 如果格式化失败，返回安全的默认提示词
                return f"""请根据对话内容生成简洁的核心记忆摘要。

现有核心记忆：
{existing_memory}

对话上下文：
{context}

请生成新的核心记忆："""

        except FileNotFoundError:
            logger.error(f"记忆提示词文件未找到: {prompt_path}")
            return "请根据对话内容生成简洁的核心记忆摘要。"
        except UnicodeDecodeError:
            logger.error(f"记忆提示词文件编码错误: {prompt_path}")
            return "请根据对话内容生成简洁的核心记忆摘要。"
        except Exception as e:
            logger.error(f"读取记忆提示词失败: {e}")
            return "请根据对话内容生成简洁的核心记忆摘要。"
    
    def _generate_core_memory(self, prompt: str, existing_memory: str, context: List[Dict], user_id: str) -> str:
        """生成核心记忆"""
        try:
            # 如果传入的prompt已经是完整的（包含现有记忆和上下文），直接使用
            if "现有核心记忆" in prompt and "对话上下文" in prompt:
                system_prompt = prompt
                user_message = "请根据上述要求生成新的核心记忆。"
            else:
                # 否则，构建完整的提示词（保持向后兼容）
                # 获取当前时间信息
                from datetime import datetime, timedelta
                now = datetime.now()
                current_date_short = now.strftime("%m月%d日")  # 简短格式：8月30日
                current_time = now.strftime("%H:%M")
                weekday_map = {
                    'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                    'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
                }
                weekday = weekday_map.get(now.strftime("%A"), now.strftime("%A"))

                # 生成详细的时间转换参考表
                tomorrow = now + timedelta(days=1)
                day_after_tomorrow = now + timedelta(days=2)
                next_monday = now + timedelta(days=(7 - now.weekday()))
                next_week_same_day = now + timedelta(days=7)
                
                time_reference = f"""
时间转换参考（重要）：
- 今天 = {current_date_short} {weekday}
- 明天 = {tomorrow.strftime('%m月%d日')} {weekday_map.get(tomorrow.strftime('%A'), tomorrow.strftime('%A'))}
- 后天 = {day_after_tomorrow.strftime('%m月%d日')} {weekday_map.get(day_after_tomorrow.strftime('%A'), day_after_tomorrow.strftime('%A'))}
- 下周一 = {next_monday.strftime('%m月%d日')}
- 下周 = {next_week_same_day.strftime('%m月%d日')}左右
"""

                # 构建包含详细时间信息的消息
                system_prompt = prompt
                user_message = f"""请根据设定和要求，生成新的核心记忆。

{time_reference}

【强制要求】：
1. 禁止使用任何相对时间词（明天、后天、下周等）
2. 所有时间相关内容必须使用具体日期
3. 格式示例："8月31日用户说明天要看电影" 而不是 "明天用户要看电影"
4. 当前时间：{current_date_short} {weekday} {current_time}

现有的核心记忆为：
{existing_memory}

最近的对话上下文：
{self._format_context_for_memory(context)}

请严格按照时间转换参考表，将对话中的相对时间转换为具体日期后记录。"""

            response = self.llm_service.get_response(
                message=user_message,
                user_id=user_id,
                system_prompt=system_prompt,
                core_memory=existing_memory,
                previous_context=context
            )
            return response
        except Exception as e:
            logger.error(f"生成核心记忆失败: {e}")
            return ""

    def _build_incremental_memory_prompt(self, existing_memory: str, context: str) -> str:
        """
        构建增量记忆生成的提示词
        
        Args:
            existing_memory: 现有核心记忆
            context: 对话上下文
            
        Returns:
            str: 完整的提示词
        """
        try:
            # 读取基础提示词模板
            template_path = os.path.join(self.root_dir, "data", "base", "memory_incremental.md")
            
            if not os.path.exists(template_path):
                # 如果增量模板不存在，使用默认模板
                return self._get_default_incremental_prompt(existing_memory, context)
            
            with open(template_path, 'r', encoding='utf-8') as f:
                prompt_template = f.read()
            
            # 获取当前时间信息
            now = datetime.now()
            current_date = now.strftime("%m月%d日")
            current_time = now.strftime("%H:%M")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(now.strftime("%A"), now.strftime("%A"))
            
            # 对长记忆进行智能摘要，减少提示词长度
            optimized_memory = self._optimize_memory_for_incremental(existing_memory, avatar_name, user_id)
            
            # 替换变量
            prompt = prompt_template.format(
                current_date=current_date,
                current_time=current_time,
                weekday=weekday,
                existing_core_memory=optimized_memory,
                context=context
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"构建增量记忆提示词失败: {e}")
            return self._get_default_incremental_prompt(existing_memory, context)

    def _optimize_memory_for_incremental(self, existing_memory: str, avatar_name: str = None, user_id: str = None) -> str:
        """
        为增量分析优化现有记忆内容，减少长度同时保留关键信息
        
        Args:
            existing_memory: 现有核心记忆
            avatar_name: 角色名称（用于备份）
            user_id: 用户ID（用于备份）
            
        Returns:
            str: 优化后的记忆内容
        """
        try:
            # 如果记忆较短（<1000字符），直接使用
            if len(existing_memory) < 1000:
                return existing_memory
            
            # 对于长记忆，提取关键信息并备份原始记忆
            memory_lines = existing_memory.split('；')
            
            # 按时间倒序排列，优先保留最近的记忆
            memory_lines.reverse()
            
            # 提取关键信息类型
            keywords = [
                '恋爱', '关系', '喜欢', '爱', '亲', '抱', '吻', '亲密',
                '约定', '计划', '承诺', '见面', '约会',
                '重要', '特殊', '第一次', '纪念日',
                '用户说', '用户表达', '用户承认'
            ]
            
            # 评分系统：根据关键词和新鲜度给每条记忆评分
            scored_lines = []
            for i, line in enumerate(memory_lines):
                line = line.strip()
                if not line:
                    continue
                
                score = 0
                
                # 新鲜度分数（越近分数越高）
                score += (len(memory_lines) - i) * 0.1
                
                # 关键词分数
                for keyword in keywords:
                    if keyword in line:
                        score += 1
                
                # 时间信息分数
                if any(month in line for month in ['8月', '9月', '10月', '11月', '12月']):
                    score += 0.5
                
                scored_lines.append((score, line))
            
            # 按分数排序，取前N条
            scored_lines.sort(reverse=True)
            
            # 选择高分记忆，控制总长度在1500字符以内
            selected_lines = []
            total_length = 0
            max_length = 1500
            
            for score, line in scored_lines:
                if total_length + len(line) + 1 <= max_length:
                    selected_lines.append(line)
                    total_length += len(line) + 1
                else:
                    break
            
            # 按时间正序重新排列
            selected_lines.reverse()
            
            optimized_memory = '；'.join(selected_lines)
            
            # 添加摘要说明
            if len(optimized_memory) < len(existing_memory):
                optimized_memory += f"\n\n[注：已智能摘要，原始记忆{len(existing_memory)}字符，优化后{len(optimized_memory)}字符]"
                
                # 备份原始记忆到archived_memory
                if avatar_name and user_id:
                    self._backup_original_memory_to_archive(avatar_name, user_id, existing_memory)
            
            logger.info(f"记忆优化完成：{len(existing_memory)} -> {len(optimized_memory)} 字符")
            return optimized_memory
            
        except Exception as e:
            logger.error(f"优化记忆内容失败: {e}")
            return existing_memory  # 出错时返回原记忆

    def _backup_original_memory_to_archive(self, avatar_name: str, user_id: str, original_memory: str):
        """
        备份原始记忆到archived_memory文件夹
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            original_memory: 原始记忆内容
        """
        try:
            from datetime import datetime
            
            # 创建备份文件路径
            archive_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "memory", "archived_memory")
            os.makedirs(archive_dir, exist_ok=True)
            
            # 生成备份文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"core_memory_backup_{user_id}_{timestamp}.md"
            backup_path = os.path.join(archive_dir, backup_filename)
            
            # 检查是否已经存在相同内容的备份，避免重复
            existing_backups = [f for f in os.listdir(archive_dir) if f.startswith(f"core_memory_backup_{user_id}_")]
            
            # 检查最新备份的内容是否相同
            if existing_backups:
                latest_backup = sorted(existing_backups)[-1]
                latest_backup_path = os.path.join(archive_dir, latest_backup)
                
                try:
                    with open(latest_backup_path, 'r', encoding='utf-8') as f:
                        latest_content = f.read()
                    
                    # 如果内容相同，跳过备份
                    if latest_content.strip() == original_memory.strip():
                        logger.debug(f"原始记忆未变化，跳过备份: {backup_filename}")
                        return
                except Exception:
                    pass  # 如果读取失败，继续备份
            
            # 创建备份内容
            backup_content = f"""# 核心记忆备份

**备份时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**角色**: {avatar_name}
**用户ID**: {user_id}
**原因**: 记忆优化压缩备份
**原始长度**: {len(original_memory)} 字符

## 备份内容

{original_memory}

---
*此备份为记忆优化前的原始完整记忆*
"""
            
            # 写入备份文件
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(backup_content)
            
            logger.info(f"原始记忆已备份到: {backup_path}")
            
        except Exception as e:
            logger.error(f"备份原始记忆失败: {e}")

    def _get_default_incremental_prompt(self, existing_memory: str, context: str) -> str:
        """获取默认的增量记忆提示词"""
        return f"""你现在是一个核心记忆增量分析模块。你的任务是分析对话上下文，只提取需要追加到现有核心记忆中的新信息。

**核心原则：**
1. 保留现有核心记忆不变
2. 只提取全新的、重要的信息
3. 如果没有新信息，返回"无需更新"
4. 严禁重复或覆盖现有记忆

**现有核心记忆：**
{existing_memory}

**最新对话上下文：**
{context}

**分析要求：**
1. 对比现有记忆和对话内容
2. 只提取确实需要补充的新信息
3. 如果没有新信息，直接返回"无需更新"
4. 如果有新信息，简洁明了地描述

请生成需要追加的新记忆内容（如果没有新信息请返回"无需更新"）："""

    def _generate_incremental_memory(self, prompt: str, existing_memory: str, context: List[Dict], user_id: str) -> str:
        """
        生成增量记忆内容
        
        Args:
            prompt: 提示词
            existing_memory: 现有记忆
            context: 对话上下文
            user_id: 用户ID
            
        Returns:
            str: 增量记忆内容
        """
        try:
            logger.info(f"开始生成增量记忆，上下文长度: {len(context)}")
            logger.info(f"系统提示词长度: {len(prompt)}")
            
            # 优化调用方式：prompt已包含完整指令，避免重复传递信息
            response = self.llm_service.get_response(
                message=prompt,
                user_id=user_id,
                system_prompt="你是核心记忆增量分析专家。专注于识别真正的新信息，避免重复内容。",
                # 不再传递core_memory和previous_context，因为prompt中已包含
            )
            
            logger.info(f"LLM原始响应: {response}")
            
            if not response or len(response.strip()) == 0:
                logger.warning("LLM返回空响应，返回'无需更新'")
                return "无需更新"
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"生成增量记忆失败: {e}")
            logger.error(f"错误详情: {str(e)}")
            return ""

    def _extract_incremental_content(self, incremental_memory: str) -> str:
        """
        从LLM返回的增量记忆中提取实际的新记忆内容
        
        Args:
            incremental_memory: LLM返回的原始增量记忆
            
        Returns:
            str: 提取后的新记忆内容
        """
        try:
            if not incremental_memory:
                return ""
            
            # 处理[新增信息]前缀
            if "[新增信息]" in incremental_memory:
                # 提取[新增信息]后面的所有内容（支持单个或多个[新增信息]标记）
                import re
                # 方法1：提取所有[新增信息]后面的内容，直到结尾或下一个[新增信息]
                pattern = r'\[新增信息\]\s*([^[]+(?=\[新增信息\]|$))'
                matches = re.findall(pattern, incremental_memory)
                if matches:
                    # 合并所有匹配的内容
                    extracted_content = '；'.join([match.strip().rstrip('；') for match in matches])
                    logger.info(f"从[新增信息]格式中提取到记忆内容: {extracted_content}")
                    return extracted_content
                else:
                    # 如果正则匹配失败，尝试简单的字符串替换
                    content = incremental_memory.replace("[新增信息]", "").strip()
                    logger.info(f"通过字符串替换提取到记忆内容: {content}")
                    return content
            
            # 如果没有[新增信息]前缀，直接返回原内容
            return incremental_memory.strip()
            
        except Exception as e:
            logger.error(f"提取增量记忆内容失败: {e}")
            return incremental_memory or ""

    def _is_initial_memory_state(self, existing_memory: str) -> bool:
        """
        检查是否为初始记忆状态（只有模板内容，无实际记忆）
        
        Args:
            existing_memory: 现有核心记忆内容
            
        Returns:
            bool: 是否为初始状态
        """
        try:
            # 检查是否包含模板特征
            initial_indicators = [
                "暂无记录",
                "未设置",
                "新用户",
                "初始化",
                "基本信息",
                "重要事件记录",
                "用户偏好",
                "关系发展"
            ]
            
            # 如果记忆为空，认为是初始状态
            if not existing_memory or existing_memory.strip() == "":
                return True
            
            # 检查是否包含多个初始指示器
            indicator_count = 0
            for indicator in initial_indicators:
                if indicator in existing_memory:
                    indicator_count += 1
            
            # 如果包含3个或以上初始指示器，认为是初始状态
            return indicator_count >= 3
            
        except Exception as e:
            logger.error(f"检查初始记忆状态失败: {e}")
            return False  # 出错时认为是非初始状态，使用增量模式

    def _build_initial_memory_prompt(self, context: str) -> str:
        """
        构建初始记忆生成的提示词
        
        Args:
            context: 对话上下文
            
        Returns:
            str: 初始记忆生成提示词
        """
        try:
            # 读取基础记忆提示词模板
            template_path = os.path.join(self.root_dir, "data", "base", "memory.md")
            
            if not os.path.exists(template_path):
                # 如果基础模板不存在，使用默认模板
                return self._get_default_initial_memory_prompt(context)
            
            with open(template_path, 'r', encoding='utf-8') as f:
                prompt_template = f.read()
            
            # 获取当前时间信息
            now = datetime.now()
            current_date = now.strftime("%m月%d日")
            current_time = now.strftime("%H:%M")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(now.strftime("%A"), now.strftime("%A"))
            
            # 替换变量，对于初始记忆，existing_core_memory设为空
            prompt = prompt_template.format(
                current_date=current_date,
                current_time=current_time,
                weekday=weekday,
                existing_core_memory="这是初次对话，暂无现有核心记忆",
                context=context
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"构建初始记忆提示词失败: {e}")
            return self._get_default_initial_memory_prompt(context)

    def _get_default_initial_memory_prompt(self, context: str) -> str:
        """获取默认的初始记忆提示词"""
        return f"""你现在是一个核心记忆初始生成模块。基于初次对话内容，生成用户的核心记忆摘要。

**要求：**
1. 提取用户的基本信息和特征
2. 记录重要的个人偏好和特点
3. 分析对话中体现的情感状态
4. 记录具体的日期和时间信息
5. 使用简洁明了的语言表达
6. 字数控制在50-100字内

**对话上下文：**
{context}

**当前时间：**{datetime.now().strftime("%m月%d日 %H:%M")}

请基于对话内容生成初始核心记忆，包含用户的基本信息、偏好特征和重要细节："""

    def _generate_initial_memory(self, prompt: str, context: List[Dict], user_id: str) -> str:
        """
        生成初始核心记忆
        
        Args:
            prompt: 提示词
            context: 对话上下文
            user_id: 用户ID
            
        Returns:
            str: 初始核心记忆内容
        """
        try:
            logger.info(f"开始生成初始核心记忆，上下文长度: {len(context)}")
            
            response = self.llm_service.get_response(
                message=prompt,
                user_id=user_id,
                system_prompt="你是一个专业的核心记忆初始生成模块。请基于初次对话生成用户的核心记忆摘要。",
                core_memory="",
                previous_context=context
            )
            
            logger.info(f"LLM返回的初始记忆: {response}")
            
            if not response or len(response.strip()) == 0:
                logger.warning("LLM返回空响应，返回默认记忆")
                return self._generate_default_initial_memory()
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"生成初始记忆失败: {e}")
            return self._generate_default_initial_memory()

    def _generate_default_initial_memory(self) -> str:
        """生成默认的初始记忆内容"""
        now = datetime.now()
        return f"""### 基本信息
- 用户ID: 0
- 初次对话时间: {now.strftime("%Y年%m月%d日 %H:%M")}
- 对话风格: 待进一步了解
- 基本特征: 需要通过更多对话来了解

### 初步观察
- 首次互动记录: {now.strftime("%m月%d日")}
- 关系状态: 新用户，正在建立了解
- 互动频率: 首次对话

*基于初次对话生成的初始记忆，有待后续完善*"""

    def _merge_core_memory(self, existing_memory: str, incremental_memory: str) -> str:
        """
        合并现有记忆和增量记忆
        
        Args:
            existing_memory: 现有记忆
            incremental_memory: 增量记忆
            
        Returns:
            str: 合并后的记忆
        """
        try:
            if not existing_memory or existing_memory.strip() == "":
                return incremental_memory
            
            if not incremental_memory or incremental_memory.strip() == "" or "无需更新" in incremental_memory:
                return existing_memory
            
            logger.info(f"开始合并记忆 - 现有记忆长度: {len(existing_memory)}, 增量记忆长度: {len(incremental_memory)}")
            
            # 智能合并：检查是否有重复内容
            existing_lines = existing_memory.split('；')
            incremental_lines = incremental_memory.split('；')
            
            # 去重并合并
            unique_lines = []
            for line in existing_lines:
                line = line.strip()
                if line and line not in unique_lines:
                    unique_lines.append(line)
            
            # 添加新的增量记忆（优先添加）
            new_lines_added = 0
            for line in incremental_lines:
                line = line.strip()
                if line and line not in unique_lines:
                    unique_lines.append(line)
                    new_lines_added += 1
            
            # 用分号连接，确保格式统一
            merged_memory = '；'.join(unique_lines)
            
            logger.info(f"合并后记忆长度: {len(merged_memory)}, 新增记忆条数: {new_lines_added}")
            
            # 确保长度合理（防止过长）
            if len(merged_memory) > 1000:  # 限制总长度
                merged_memory = merged_memory[:1000] + "..."
            
            return merged_memory
            
        except Exception as e:
            logger.error(f"合并记忆失败: {e}")
            return existing_memory  # 出错时返回原有记忆

    def _format_context_for_memory(self, context: List[Dict]) -> str:
        """
        格式化对话上下文，便于记忆生成

        Args:
            context: 对话上下文列表

        Returns:
            str: 格式化后的对话内容
        """
        if not context:
            return "无对话上下文"

        formatted_lines = []
        for item in context:
            if isinstance(item, dict):
                if item.get("role") == "user":
                    formatted_lines.append(f"用户：{item.get('content', '')}")
                elif item.get("role") == "assistant":
                    formatted_lines.append(f"我：{item.get('content', '')}")
                    formatted_lines.append("---")

        return "\n".join(formatted_lines)

    def _sync_core_memory_to_md(self, avatar_name: str, user_id: str, memory_content: str):
        """将核心记忆同步到MD文件"""
        try:
            memory_dir = self._get_avatar_newmemory_dir(avatar_name)
            md_path = os.path.join(memory_dir, "md", "core_memory.md")
            
            # 构建MD文件内容
            md_content = f"""# 核心记忆

## 用户: {user_id}

{memory_content}

---
*最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*记录状态: 自动更新*
"""
            
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            logger.debug(f"核心记忆已同步到MD文件: {md_path}")
            
        except Exception as e:
            logger.error(f"同步核心记忆到MD文件失败: {e}")

    def _sync_short_memory_to_md(self, avatar_name: str, user_id: str):
        """将短期记忆同步到MD文件（使用旧记忆系统的格式）"""
        try:
            newmemory_dir = self._get_avatar_newmemory_dir(avatar_name)
            md_path = os.path.join(newmemory_dir, "md", "short_memory.md")

            # 从数据库获取最近的对话记录
            db_path = self._get_short_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查询最近10条对话记录
            cursor.execute('''
                SELECT user_message, bot_reply, timestamp FROM short_memory
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT 10
            ''', (user_id,))

            records = cursor.fetchall()
            conn.close()

            if not records:
                return

            # 构建JSON格式的对话记录（与旧记忆系统格式兼容）
            conversations = []
            for user_message, bot_reply, timestamp in reversed(records):  # 反转以保持时间顺序
                # 如果timestamp是字符串，直接使用；如果是datetime对象，转换为字符串
                if isinstance(timestamp, str):
                    timestamp_str = timestamp
                else:
                    timestamp_str = timestamp.strftime("%Y-%m-%d %H:%M:%S")

                conversation = {
                    "timestamp": timestamp_str,
                    "user": user_message,
                    "bot": bot_reply
                }
                conversations.append(conversation)

            # 将对话记录保存为JSON格式到MD文件
            import json
            with open(md_path, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, ensure_ascii=False, indent=2)

            logger.debug(f"短期记忆已同步到MD文件: {md_path}")

        except Exception as e:
            logger.error(f"同步短期记忆到MD文件失败: {e}")

    def _check_and_archive_memory(self, avatar_name: str, user_id: str):
        """检查并归档过期的短期记忆"""
        try:
            db_path = self._get_short_memory_db_path(avatar_name)

            if not os.path.exists(db_path):
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 从配置获取归档天数
            from src.config import config
            archive_days = config.memory_and_story.memory_archive_days
            logger.debug(f"使用配置的归档天数: {archive_days} 天")

            # 获取指定天数前的记录
            archive_cutoff = datetime.now() - timedelta(days=archive_days)
            current_week = datetime.now().isocalendar()[1]
            
            cursor.execute('''
                SELECT * FROM short_memory
                WHERE user_id = ? AND timestamp < ?
            ''', (user_id, archive_cutoff))
            
            old_records = cursor.fetchall()
            
            if old_records:
                # 归档到MD文件
                self._archive_to_md(avatar_name, user_id, old_records)
                
                # 删除数据库中的旧记录
                cursor.execute('''
                    DELETE FROM short_memory
                    WHERE user_id = ? AND timestamp < ?
                ''', (user_id, archive_cutoff))
                
                conn.commit()
                logger.info(f"已归档 {len(old_records)} 条过期记忆记录（超过{archive_days}天）到 archived_memory 文件夹")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"检查和归档记忆失败: {e}")
    
    def _archive_to_md(self, avatar_name: str, user_id: str, records: List[Tuple]):
        """将记录归档到MD文件"""
        try:
            # 获取归档天数配置
            from src.config import config
            archive_days = config.memory_and_story.memory_archive_days

            memory_dir = self._get_avatar_newmemory_dir(avatar_name)
            archive_dir = os.path.join(memory_dir, "archived_memory")
            os.makedirs(archive_dir, exist_ok=True)
            
            # 按周分组归档
            week_groups = {}
            for record in records:
                week_num = record[5]  # week_number字段
                if week_num not in week_groups:
                    week_groups[week_num] = []
                week_groups[week_num].append(record)
            
            # 为每周创建归档文件
            for week_num, week_records in week_groups.items():
                year = datetime.now().year
                archive_file = os.path.join(archive_dir, f"{year}-week{week_num}_archived.md")

                # 构建归档内容
                archive_content = f"# {year}年第{week_num}周对话记录（已归档）\n\n"
                archive_content += f"## 用户: {user_id}\n"
                archive_content += f"## 归档原因: 超过{archive_days}天的记忆自动归档\n\n"
                
                for record in week_records:
                    timestamp = record[4]  # timestamp字段
                    user_msg = record[2]   # user_message字段
                    bot_reply = record[3]  # bot_reply字段
                    
                    archive_content += f"### {timestamp}\n"
                    archive_content += f"**用户**: {user_msg}\n"
                    archive_content += f"**回复**: {bot_reply}\n\n"
                
                archive_content += f"---\n*归档时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
                
                with open(archive_file, 'w', encoding='utf-8') as f:
                    f.write(archive_content)
                
                logger.info(f"已创建归档文件: {archive_file}")
            
        except Exception as e:
            logger.error(f"归档记录到MD文件失败: {e}")

    def _load_conversation_counts(self):
        """从数据库加载对话计数"""
        try:
            self.conversation_count = {}
            
            # 遍历所有角色
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")
            if not os.path.exists(avatars_dir):
                return
            
            for avatar_name in os.listdir(avatars_dir):
                avatar_path = os.path.join(avatars_dir, avatar_name)
                if os.path.isdir(avatar_path):
                    db_path = self._get_short_memory_db_path(avatar_name)
                    if not os.path.exists(db_path):
                        continue
                    
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # 查询该角色的所有对话计数
                    cursor.execute('SELECT user_id, count FROM conversation_counts')
                    results = cursor.fetchall()
                    
                    for user_id, count in results:
                        conversation_key = f"{avatar_name}_{user_id}"
                        self.conversation_count[conversation_key] = count
                    
                    conn.close()
            
            logger.info(f"从数据库加载了 {len(self.conversation_count)} 个对话计数")
            
        except Exception as e:
            logger.error(f"加载对话计数失败: {e}")
            self.conversation_count = {}

    def _save_conversation_count(self, avatar_name: str, user_id: str, count: int):
        """保存对话计数到数据库"""
        try:
            db_path = self._get_short_memory_db_path(avatar_name)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 更新或插入对话计数
            cursor.execute('''
                INSERT OR REPLACE INTO conversation_counts (avatar_name, user_id, count, last_updated)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (avatar_name, user_id, count))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"保存对话计数失败: {e}")

    def _initialize_database_tables(self):
        """初始化所有角色的数据库表"""
        try:
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")

            if not os.path.exists(avatars_dir):
                return

            for avatar_name in os.listdir(avatars_dir):
                avatar_path = os.path.join(avatars_dir, avatar_name)
                if os.path.isdir(avatar_path):
                    self._initialize_avatar_tables(avatar_name)

            logger.info("数据库表初始化完成")

        except Exception as e:
            logger.error(f"初始化数据库表失败: {e}")

    def _initialize_avatar_tables(self, avatar_name: str):
        """初始化指定角色的数据库表"""
        try:
            # 初始化核心记忆表
            core_db_path = self._get_core_memory_db_path(avatar_name)
            self._create_memory_tables(core_db_path)

            # 初始化短期记忆表
            short_db_path = self._get_short_memory_db_path(avatar_name)
            self._create_memory_tables(short_db_path)

            logger.debug(f"角色 {avatar_name} 数据库表初始化完成")

        except Exception as e:
            logger.error(f"初始化角色 {avatar_name} 数据库表失败: {e}")

    def _rebuild_core_memory_from_md(self, avatar_name: str, user_id: str) -> str:
        """
        从md文件重建核心记忆数据库表
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
        
        Returns:
            str: 从md文件中提取的记忆内容
        """
        try:
            # 读取md文件
            md_path = os.path.join(self.root_dir, "data", "avatars", avatar_name, "memory", "core_memory.md")
            
            if not os.path.exists(md_path):
                logger.warning(f"核心记忆md文件不存在: {md_path}")
                return ""
            
            with open(md_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # 解析md文件内容
            memory_content = self._parse_core_memory_md(md_content)
            
            if not memory_content:
                logger.warning(f"无法从md文件解析记忆内容: {md_path}")
                return ""
            
            # 重建数据库表
            db_path = self._get_core_memory_db_path(avatar_name)
            
            # 备份现有数据库文件
            if os.path.exists(db_path):
                backup_path = f"{db_path}.backup_{int(time.time())}"
                shutil.copy2(db_path, backup_path)
                logger.info(f"已备份原数据库文件: {backup_path}")
            
            # 重新创建数据库表
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 删除现有的损坏表
            cursor.execute("DROP TABLE IF EXISTS core_memory;")
            
            # 创建新的表结构
            cursor.execute('''
                CREATE TABLE core_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    category TEXT DEFAULT 'general',
                    importance INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 插入解析的记忆内容
            cursor.execute('''
                INSERT INTO core_memory (user_id, content, category, importance)
                VALUES (?, ?, ?, ?)
            ''', (user_id, memory_content, 'general', 1))
            
            conn.commit()
            conn.close()
            
            logger.info(f"成功从md文件重建核心记忆表 - 角色={avatar_name}, 用户={user_id}")
            return memory_content
            
        except Exception as e:
            logger.error(f"从md文件重建核心记忆失败: {e}")
            return ""

    def _parse_core_memory_md(self, md_content: str) -> str:
        """
        解析核心记忆md文件内容
        
        Args:
            md_content: md文件内容
        
        Returns:
            str: 解析后的记忆内容
        """
        try:
            lines = md_content.split('\n')
            memory_lines = []
            in_memory_section = False
            
            for line in lines:
                line = line.strip()
                
                # 跳过标题和分隔线
                if line.startswith('#') or line.startswith('---') or line.startswith('*'):
                    continue
                
                # 跳过用户标识行
                if line.startswith('## 用户:'):
                    continue
                
                # 如果是空行且已经在记忆部分，可能表示记忆段的结束
                if line == '' and in_memory_section:
                    # 检查接下来是否是新的段落
                    continue
                
                # 如果有内容，添加到记忆中
                if line and not line.startswith('##'):
                    memory_lines.append(line)
                    in_memory_section = True
            
            # 合并所有记忆行
            memory_content = '；'.join(memory_lines)
            
            # 清理格式
            memory_content = memory_content.replace('；；', '；')
            memory_content = memory_content.strip('；')
            
            return memory_content
            
        except Exception as e:
            logger.error(f"解析核心记忆md文件失败: {e}")
            return ""

    def _create_memory_tables(self, db_path: str):
        """创建记忆数据库表"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 核心记忆表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS core_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    category TEXT DEFAULT 'general',
                    importance INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 短期记忆表（新增日期字段）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS short_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    user_message TEXT NOT NULL,
                    bot_reply TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    week_number INTEGER NOT NULL,
                    date_key TEXT NOT NULL
                )
            ''')
            
            # 检查并添加新字段（兼容性迁移）
            self._migrate_short_memory_table(cursor)
            
            # 在迁移完成后创建索引
            try:
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_date_user 
                    ON short_memory (date_key, user_id)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_timestamp 
                    ON short_memory (timestamp)
                ''')
            except Exception as idx_error:
                logger.warning(f"创建索引失败，继续使用无索引表: {idx_error}")

            # 文件同步状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_status (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT NOT NULL,
                    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 对话计数表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_counts (
                    avatar_name TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    count INTEGER DEFAULT 0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (avatar_name, user_id)
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"创建记忆数据库表失败: {e}")

    def initialize_avatar_memory(self, avatar_name: str, user_id: str):
        """初始化角色记忆文件和数据库"""
        try:
            # 确保目录结构存在
            memory_dir = self._get_avatar_newmemory_dir(avatar_name)

            # 初始化数据库表
            self._initialize_avatar_tables(avatar_name)

            # 创建默认MD文件（如果不存在）
            self._create_default_md_files(avatar_name, memory_dir, user_id)

            logger.info(f"角色 {avatar_name} 记忆系统初始化完成")

        except Exception as e:
            logger.error(f"初始化角色 {avatar_name} 记忆系统失败: {e}")

    def _create_default_md_files(self, avatar_name: str, memory_dir: str, user_id: str):
        """创建默认的MD文件"""
        try:
            from datetime import datetime

            md_dir = os.path.join(memory_dir, "md")

            # 创建默认核心记忆文件
            core_memory_path = os.path.join(md_dir, "core_memory.md")
            if not os.path.exists(core_memory_path):
                core_memory_content = f"""# {avatar_name} 核心记忆

## 用户: {user_id}

### 基本信息
- 用户ID: {user_id}
- 称呼偏好: 未设置
- 基本特征: 暂无记录

### 重要事件记录
- 暂无重要事件记录

### 用户偏好
- 对话风格: 未确定
- 兴趣爱好: 暂无记录
- 特殊需求: 暂无记录

### 关系发展
- 初次见面时间: 未记录
- 关系状态: 新用户
- 互动频率: 暂无数据

---
*最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*记录状态: 初始化*
"""
                with open(core_memory_path, 'w', encoding='utf-8') as f:
                    f.write(core_memory_content)

            # 创建默认短期记忆文件（只在文件不存在时创建，避免覆盖现有记忆）
            short_memory_path = os.path.join(md_dir, "short_memory.md")
            if not os.path.exists(short_memory_path):
                # 创建空的JSON数组，与旧记忆系统格式兼容
                with open(short_memory_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump([], f, ensure_ascii=False, indent=2)
                logger.info(f"创建默认短期记忆文件: {short_memory_path}")
            else:
                logger.debug(f"短期记忆文件已存在，跳过创建: {short_memory_path}")

            logger.debug(f"为角色 {avatar_name} 创建默认MD文件成功")

        except Exception as e:
            logger.error(f"创建默认MD文件失败: {e}")

    def _setup_daily_summary_task(self):
        """设置每日总结任务（每晚12点触发）"""
        try:
            if not SCHEDULER_AVAILABLE or CronTrigger is None or self.scheduler is None:
                logger.warning("无法设置定时任务：APScheduler不可用")
                return
                
            # 每晚12点触发总结
            trigger = CronTrigger(hour=0, minute=0, second=0)
            self.scheduler.add_job(
                func=self._daily_summary_job,
                trigger=trigger,
                id='daily_memory_summary',
                name='每日记忆总结任务',
                replace_existing=True
            )
            logger.info("每日记忆总结任务已设置：每晚12:00触发")
            
        except Exception as e:
            logger.error(f"设置每日总结任务失败: {e}")

    def _daily_summary_job(self):
        """每日总结任务：分析当天所有对话并更新核心记忆"""
        try:
            logger.info("开始执行每日记忆总结任务...")
            
            # 获取所有角色
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")
            if not os.path.exists(avatars_dir):
                logger.warning("没有找到角色目录，跳过每日总结")
                return
            
            summary_count = 0
            for avatar_name in os.listdir(avatars_dir):
                avatar_path = os.path.join(avatars_dir, avatar_name)
                if os.path.isdir(avatar_path):
                    count = self._process_avatar_daily_summary(avatar_name)
                    summary_count += count
            
            logger.info(f"每日记忆总结任务完成，处理了 {summary_count} 个用户的记忆")
            
        except Exception as e:
            logger.error(f"执行每日记忆总结任务失败: {e}")

    def _process_avatar_daily_summary(self, avatar_name: str) -> int:
        """处理指定角色的每日总结"""
        try:
            db_path = self._get_short_memory_db_path(avatar_name)
            if not os.path.exists(db_path):
                return 0
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取今天的日期键
            today = datetime.now().strftime("%Y-%m-%d")
            
            # 查找今天有对话的所有用户
            cursor.execute('''
                SELECT DISTINCT user_id 
                FROM short_memory 
                WHERE date_key = ?
            ''', (today,))
            
            user_ids = cursor.fetchall()
            conn.close()
            
            summary_count = 0
            for (user_id,) in user_ids:
                if self._generate_daily_summary(avatar_name, user_id, today):
                    summary_count += 1
            
            return summary_count
            
        except Exception as e:
            logger.error(f"处理角色 {avatar_name} 每日总结失败: {e}")
            return 0

    def _generate_daily_summary(self, avatar_name: str, user_id: str, date_key: str) -> bool:
        """为指定用户生成当天的详细核心记忆总结"""
        try:
            # 获取当天的所有对话
            daily_conversations = self._get_daily_conversations(avatar_name, user_id, date_key)
            
            if not daily_conversations:
                logger.debug(f"用户 {user_id} 在 {date_key} 没有对话记录，跳过总结")
                return False
            
            # 获取现有核心记忆
            existing_memory = self.get_core_memory(avatar_name, user_id)
            
            # 生成新的详细核心记忆
            new_memory = self._generate_detailed_core_memory(
                existing_memory, daily_conversations, user_id, date_key
            )
            
            if not new_memory or 'Error' in new_memory:
                logger.warning(f"用户 {user_id} 的每日总结生成失败，保留原有记忆")
                return False
            
            # 保存到数据库
            if self._save_detailed_core_memory(avatar_name, user_id, new_memory):
                logger.info(f"用户 {user_id} 在 {date_key} 的详细记忆总结完成")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"生成用户 {user_id} 每日总结失败: {e}")
            return False

    def _get_daily_conversations(self, avatar_name: str, user_id: str, date_key: str) -> List[Dict]:
        """获取指定日期的所有对话"""
        try:
            db_path = self._get_short_memory_db_path(avatar_name)
            if not os.path.exists(db_path):
                return []
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取指定日期的所有对话（按时间排序）
            cursor.execute('''
                SELECT user_message, bot_reply, timestamp 
                FROM short_memory 
                WHERE user_id = ? AND date_key = ?
                ORDER BY timestamp ASC
            ''', (user_id, date_key))
            
            results = cursor.fetchall()
            conn.close()
            
            # 转换为标准格式
            conversations = []
            for user_message, bot_reply, timestamp in results:
                conversations.append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": timestamp
                })
                conversations.append({
                    "role": "assistant", 
                    "content": bot_reply,
                    "timestamp": timestamp
                })
            
            return conversations
            
        except Exception as e:
            logger.error(f"获取每日对话失败: {e}")
            return []

    def _generate_detailed_core_memory(self, existing_memory: str, daily_conversations: List[Dict], user_id: str, date_key: str) -> str:
        """生成详细的核心记忆（基于全天对话）"""
        try:
            # 构建提示词
            prompt = self._build_detailed_memory_prompt()
            
            # 构建详细消息
            from datetime import datetime
            parsed_date = datetime.strptime(date_key, "%Y-%m-%d")
            formatted_date = parsed_date.strftime("%m月%d日")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(parsed_date.strftime('%A'), parsed_date.strftime('%A'))
            
            detailed_message = f"""请基于 {formatted_date} {weekday} 的全天对话，在现有核心记忆的基础上生成更加详细的新核心记忆。

现有核心记忆：
{existing_memory}

{formatted_date} {weekday} 的完整对话记录：
{self._format_daily_conversations_for_summary(daily_conversations)}

要求：
1. 保留原有核心记忆中的重要信息
2. 添加今天对话中的重要细节和新信息
3. 整理和扩展用户个性、喜好、关系发展等信息
4. 记录具体日期和重要事件（禁止使用相对时间词）
5. 字数控制在50-100字内，保持核心记忆的精炼性
6. 使用第一人称视角，仅保留关键信息"""
            
            response = self.llm_service.get_response(
                message=detailed_message,
                user_id=user_id,
                system_prompt=prompt,
                core_memory=existing_memory,
                previous_context=daily_conversations[-20:] if len(daily_conversations) > 20 else daily_conversations  # 只传递最后20条作为上下文
            )
            
            return response
            
        except Exception as e:
            logger.error(f"生成详细核心记忆失败: {e}")
            return ""

    def _build_detailed_memory_prompt(self) -> str:
        """构建详细记忆生成提示词"""
        try:
            # 使用现有的记忆提示词模板
            prompt_path = os.path.join(self.root_dir, 'data/base/memory.md')
            
            if os.path.exists(prompt_path):
                with open(prompt_path, 'r', encoding='utf-8') as f:
                    base_prompt = f.read()
                
                # 为详细记忆添加特殊要求
                detailed_prompt = base_prompt + """

特别指示（每日记忆模式）：
1. 这是基于全天对话的记忆总结，但仍需保持精炼
2. 可以记录更多的对话细节和情感信息
3. 字数控制在50-100字，与普通核心记忆一致
4. 重点关注用户个性、情绪变化、关系发展等
5. 必须使用具体日期而非相对时间词
"""
                return detailed_prompt
            else:
                return "请根据对话内容生成详细的核心记忆摘要。"
                
        except Exception as e:
            logger.error(f"构建详细记忆提示词失败: {e}")
            return "请根据对话内容生成详细的核心记忆摘要。"

    def _format_daily_conversations_for_summary(self, conversations: List[Dict]) -> str:
        """格式化全天对话供总结使用"""
        formatted_lines = []
        
        for i in range(0, len(conversations), 2):
            if i + 1 < len(conversations):
                user_msg = conversations[i].get('content', '')
                bot_msg = conversations[i + 1].get('content', '')
                timestamp = conversations[i].get('timestamp', '')
                
                # 格式化时间戳
                if isinstance(timestamp, str):
                    time_str = timestamp.split(' ')[1] if ' ' in timestamp else timestamp
                else:
                    time_str = str(timestamp)
                
                formatted_lines.append(f"[{time_str}]")
                formatted_lines.append(f"我：{bot_msg}")
                formatted_lines.append(f"用户：{user_msg}")
                formatted_lines.append("---")
        
        return "\n".join(formatted_lines)

    def _save_detailed_core_memory(self, avatar_name: str, user_id: str, memory_content: str) -> bool:
        """保存详细核心记忆到数据库"""
        try:
            db_path = self._get_core_memory_db_path(avatar_name)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 删除旧的核心记忆
            cursor.execute('DELETE FROM core_memory WHERE user_id = ?', (user_id,))
            
            # 插入新的详细核心记忆
            cursor.execute('''
                INSERT INTO core_memory (user_id, content, category, importance)
                VALUES (?, ?, ?, ?)
            ''', (user_id, memory_content, 'detailed_daily', 2))  # importance=2表示详细记忆
            
            conn.commit()
            conn.close()
            
            # 同步到MD文件
            self._sync_core_memory_to_md(avatar_name, user_id, memory_content)
            
            # 使缓存失效
            self._invalidate_cache(avatar_name, user_id)
            
            return True
            
        except Exception as e:
            logger.error(f"保存详细核心记忆失败: {e}")
            return False

    def _cleanup_scheduler(self):
        """清理调度器资源"""
        try:
            if hasattr(self, 'scheduler') and self.scheduler:
                self.scheduler.shutdown(wait=False)
                logger.info("调度器已关闭")
        except Exception as e:
            logger.error(f"关闭调度器失败: {e}")

    def get_daily_conversation_count(self, avatar_name: str, user_id: str, date_key: Optional[str] = None) -> int:
        """获取指定日期的对话数量（新增方法）"""
        try:
            if date_key is None:
                date_key = datetime.now().strftime("%Y-%m-%d")
            
            db_path = self._get_short_memory_db_path(avatar_name)
            if not os.path.exists(db_path):
                return 0
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT COUNT(*) FROM short_memory 
                WHERE user_id = ? AND date_key = ?
            ''', (user_id, date_key))
            
            count = cursor.fetchone()[0]
            conn.close()
            
            return count
            
        except Exception as e:
            logger.error(f"获取每日对话数量失败: {e}")
            return 0

    def _migrate_short_memory_table(self, cursor):
        """迁移短期记忆表，添加新字段（如果不存在）"""
        try:
            # 检查表结构
            cursor.execute("PRAGMA table_info(short_memory)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # 检查是否需要添加 date_key 字段
            if 'date_key' not in column_names:
                logger.info("检测到旧版本表结构，正在添加 date_key 字段...")
                
                # 添加新字段
                cursor.execute("ALTER TABLE short_memory ADD COLUMN date_key TEXT")
                
                # 为现有记录更新 date_key
                cursor.execute("""
                    UPDATE short_memory 
                    SET date_key = DATE(timestamp) 
                    WHERE date_key IS NULL
                """)
                
                logger.info("成功添加 date_key 字段并更新现有记录")
                
        except Exception as e:
            logger.warning(f"数据库迁移警告: {e}，继续使用现有表结构")

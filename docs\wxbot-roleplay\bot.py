#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信角色扮演机器人主类
参考SiverWxBotWebV2.2.3项目的代码结构
"""

import time
import re
import os
import traceback
import threading
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from openai import OpenAI
from wxautox import WeChat
from wxautox.msgs import FriendMessage, SystemMessage
import config
from utils.logger import log
from database import Session
from utils.memory_manager import MemoryManager
from utils.weather import WeatherManager
from utils.favorability import FavorabilityManager
from utils.text_processor import (
    remove_brackets_content,
    format_response,
    split_sentences,
    clean_group_message,
    extract_image_tags,
    has_think_tags,
    remove_think_tags,

    apply_message_delay,
    sentiment_analyzer
)
from utils.proactive_message import ProactiveMessageManager
from utils.daily_schedule import DailyScheduleManager
from utils.reminder_manager import ReminderManager
from utils.group_participation import GroupParticipationManager
from utils.group_manager import GroupManager
from utils.checkin_manager import CheckinManager
from utils.checkin_scheduler import CheckinScheduler
from utils.image_generator import ImageGenerator, is_image_request
from utils.message_queue import MessageQueueManager
from utils.emotion_state import EmotionStateManager

from utils.friend_request_manager import FriendRequestManager


class WXRolePlayBot:
    """微信角色扮演机器人主类"""

    def __init__(self):
        self.wx = None
        self.api_client = None
        self.prompt_content = ""
        self.running = False
        self.model = config.MODEL  # 添加模型配置

        # 初始化数据库会话
        self.session = Session()

        # 初始化记忆管理器
        self.memory_manager = MemoryManager()

        # 初始化天气管理器
        self.weather_manager = WeatherManager()

        # 初始化调度器
        self.scheduler = BackgroundScheduler()

        # 初始化天气缓存
        self.cached_weather = {}

        # 初始化主动发消息管理器
        self.proactive_message_manager = None

        # 初始化每日日程管理器
        self.schedule_manager = DailyScheduleManager(bot=self)

        # 初始化定时提醒管理器
        self.reminder_manager = ReminderManager(bot=self)
        # 立即设置调度器
        self.reminder_manager.set_scheduler(self.scheduler)

        # 初始化群聊参与管理器
        self.group_participation_manager = GroupParticipationManager()

        # 初始化群聊管理器
        self.group_manager = GroupManager()

        # 初始化签到管理器
        self.checkin_manager = CheckinManager(session=self.session)
        self.checkin_scheduler = None

        # 初始化图片生成器
        self.image_generator = None

        # 初始化消息队列管理器
        self.message_queue_manager = None

        # 初始化情感状态管理器
        self.emotion_manager = None

        # 初始化
        self.init_api_client()
        self.load_prompt()
        self.init_image_generator()
        self.init_message_queue()

        # 初始化好友请求管理器
        self.friend_request_manager = FriendRequestManager(session=self.session)

        self.init_emotion_state()

    def init_api_client(self):
        """初始化API客户端"""
        try:
            self.api_client = OpenAI(
                api_key=config.API_KEY,
                base_url=config.BASE_URL
            )
            log("API客户端初始化成功")
        except Exception as e:
            log(f"API客户端初始化失败: {str(e)}", "ERROR")
            self.api_client = None

    def init_image_generator(self):
        """初始化图片生成器"""
        try:
            if getattr(config, 'ENABLE_IMAGE_GENERATION', False):
                # 获取图片生成API配置
                image_api_key = getattr(config, 'IMAGE_API_KEY', None) or config.API_KEY
                image_base_url = getattr(config, 'IMAGE_BASE_URL', None) or config.BASE_URL
                image_model = getattr(config, 'IMAGE_MODEL', None) or config.MODEL

                self.image_generator = ImageGenerator(
                    api_key=image_api_key,
                    base_url=image_base_url,
                    model=image_model
                )
                log("图片生成器初始化成功")
            else:
                log("图片生成功能已禁用")
                self.image_generator = None
        except Exception as e:
            log(f"图片生成器初始化失败: {str(e)}", "ERROR")
            self.image_generator = None

    def init_message_queue(self):
        """初始化消息队列管理器"""
        try:
            if getattr(config, 'ENABLE_MESSAGE_QUEUE', True):
                max_queue_size = getattr(config, 'MESSAGE_QUEUE_MAX_SIZE', 100)
                self.message_queue_manager = MessageQueueManager(
                    bot_instance=self,
                    max_queue_size=max_queue_size
                )
                # log("消息队列管理器初始化成功")
            else:
                log("消息队列功能已禁用")
                self.message_queue_manager = None
        except Exception as e:
            log(f"消息队列管理器初始化失败: {str(e)}", "ERROR")
            self.message_queue_manager = None

    def init_emotion_state(self):
        """初始化情感状态管理器"""
        try:
            if getattr(config, 'ENABLE_EMOTION_STATE', True):
                # 使用机器人名称作为用户ID
                self.emotion_manager = EmotionStateManager(user_id=config.ROBOT_WX_NAME)
                log("情感状态管理器初始化成功")

                # 启动定时衰减任务
                if getattr(config, 'EMOTION_DECAY_CONFIG', {}).get('auto_decay_enabled', True):
                    cleanup_interval = getattr(config, 'EMOTION_DECAY_CONFIG', {}).get('cleanup_interval', 300)
                    self.scheduler.add_job(
                        func=self.emotion_manager.apply_time_decay,
                        trigger='interval',
                        seconds=cleanup_interval,
                        id='emotion_decay',
                        replace_existing=True
                    )
                    log(f"情感状态自动衰减任务已启动，间隔: {cleanup_interval}秒")
            else:
                log("情感状态系统已禁用")
                self.emotion_manager = None
        except Exception as e:
            log(f"情感状态管理器初始化失败: {str(e)}", "ERROR")
            self.emotion_manager = None

    def load_prompt(self):
        """加载所有提示词文件（角色、好感度、防御）"""
        try:
            # 加载角色提示词
            role_prompt = self.load_single_prompt(config.PROMPT_NAME, "角色提示词")

            # 加载好感度提示词
            favorability_prompt = self.load_single_prompt(config.FAVORABILITY_PROMPT_NAME, "好感度提示词")

            # 加载防御提示词
            defense_prompt = self.load_single_prompt(config.DEFENSE_PROMPT_NAME, "防御提示词")

            # 合并所有提示词
            self.prompt_content = self.merge_prompts(role_prompt, favorability_prompt, defense_prompt)

            log("所有提示词加载并合并完成")
            log(f"合并后的提示词总长度: {len(self.prompt_content)} 字符")

        except Exception as e:
            log(f"加载提示词失败: {str(e)}", "ERROR")
            self.prompt_content = "你是一个AI助手，请友好地回复用户。"

    def load_single_prompt(self, filename: str, prompt_type: str) -> str:
        """加载单个提示词文件"""
        try:
            prompt_path = os.path.join("prompts", filename)
            if os.path.exists(prompt_path):
                with open(prompt_path, "r", encoding="utf-8") as file:
                    content = file.read().strip()
                log(f"{prompt_type}加载成功: {filename}")
                return content
            else:
                log(f"{prompt_type}文件不存在: {filename}", "WARNING")
                return ""
        except Exception as e:
            log(f"加载{prompt_type}失败: {str(e)}", "ERROR")
            return ""

    def merge_prompts(self, role_prompt: str, favorability_prompt: str, defense_prompt: str) -> str:
        """合并多个提示词"""
        merged_prompt = ""

        # 添加角色提示词（主要内容）
        if role_prompt:
            merged_prompt += role_prompt
        else:
            merged_prompt += "你是一个AI助手，请友好地回复用户。"

        # 添加好感度提示词
        if favorability_prompt:
            merged_prompt += f"\n\n{favorability_prompt}"

        # 添加防御提示词
        if defense_prompt:
            merged_prompt += f"\n\n{defense_prompt}"

        return merged_prompt

    def get_prompt_status(self) -> str:
        """获取提示词加载状态"""
        try:
            status_lines = ["【提示词加载状态】"]

            # 检查角色提示词
            role_path = os.path.join("prompts", config.PROMPT_NAME)
            if os.path.exists(role_path):
                with open(role_path, "r", encoding="utf-8") as f:
                    role_content = f.read().strip()
                status_lines.append(f"✅ 角色提示词: {config.PROMPT_NAME} ({len(role_content)}字符)")
            else:
                status_lines.append(f"❌ 角色提示词: {config.PROMPT_NAME} (文件不存在)")

            # 检查好感度提示词
            fav_path = os.path.join("prompts", config.FAVORABILITY_PROMPT_NAME)
            if os.path.exists(fav_path):
                with open(fav_path, "r", encoding="utf-8") as f:
                    fav_content = f.read().strip()
                status_lines.append(f"✅ 好感度提示词: {config.FAVORABILITY_PROMPT_NAME} ({len(fav_content)}字符)")
            else:
                status_lines.append(f"❌ 好感度提示词: {config.FAVORABILITY_PROMPT_NAME} (文件不存在)")

            # 检查防御提示词
            def_path = os.path.join("prompts", config.DEFENSE_PROMPT_NAME)
            if os.path.exists(def_path):
                with open(def_path, "r", encoding="utf-8") as f:
                    def_content = f.read().strip()
                status_lines.append(f"✅ 防御提示词: {config.DEFENSE_PROMPT_NAME} ({len(def_content)}字符)")
            else:
                status_lines.append(f"❌ 防御提示词: {config.DEFENSE_PROMPT_NAME} (文件不存在)")

            # 显示合并后的总长度
            status_lines.append(f"\n📝 合并后总长度: {len(self.prompt_content)}字符")

            return "\n".join(status_lines)

        except Exception as e:
            return f"获取提示词状态失败: {str(e)}"

    def init_wechat(self):
        """初始化微信客户端"""
        try:
            self.wx = WeChat()
            if not self.wx.IsOnline():
                log("微信未登录或不在线", "ERROR")
                return False

            log(f"微信客户端初始化成功，昵称: {getattr(self.wx, 'nickname', '未知')}")

            # 设置群聊管理器的微信实例
            self.group_manager.set_wx_instance(self.wx)
            log("群聊管理器初始化完成")

            # 设置签到管理器的群聊管理器
            self.checkin_manager.set_group_manager(self.group_manager)

            # 根据配置文件自动启用群聊签到功能
            self.checkin_manager.auto_enable_groups_from_config()

            # 初始化签到定时任务管理器
            self.checkin_scheduler = CheckinScheduler(self.checkin_manager, self.wx)
            self.checkin_scheduler.start_scheduler()

            # 检查定时任务设置状态
            import schedule
            total_jobs = len(schedule.jobs)
            log(f"签到管理器初始化完成，当前定时任务数量: {total_jobs}")

            if total_jobs > 0:
                log("定时任务列表:")
                for i, job in enumerate(schedule.jobs):
                    log(f"  任务{i+1}: {job.job_func.__name__} - 下次执行: {job.next_run}")
            else:
                log("警告: 没有设置任何定时任务！", "WARNING")

            # 启动监听
            self.wx.StartListening()
            log("微信消息监听已启动")

            return True

        except Exception as e:
            log(f"初始化微信客户端失败: {str(e)}", "ERROR")
            return False

    def add_listeners(self):
        """添加消息监听"""
        if not self.wx:
            log("微信客户端未初始化", "ERROR")
            return

        # 添加管理员监听
        for admin in config.ADMIN_LIST:
            try:
                result = self.wx.AddListenChat(
                    nickname=admin,
                    callback=self.message_callback
                )
                if result:
                    log(f"已添加管理员监听: {admin}")
                else:
                    log(f"添加管理员监听失败: {admin}", "WARNING")
            except Exception as e:
                log(f"添加管理员监听异常: {e}", "ERROR")

        # 添加用户监听
        for user in config.LISTEN_LIST:
            try:
                result = self.wx.AddListenChat(
                    nickname=user,
                    callback=self.message_callback
                )
                if result:
                    log(f"已添加用户监听: {user}")
                else:
                    log(f"添加用户监听失败: {user}", "WARNING")
            except Exception as e:
                log(f"添加用户监听异常: {e}", "ERROR")

        # 添加群组监听
        if config.GROUP_SWITCH:
            for group in config.GROUP_LIST:
                try:
                    result = self.wx.AddListenChat(
                        nickname=group,
                        callback=self.message_callback
                    )
                    if result:
                        log(f"已添加群组监听: {group}")
                    else:
                        log(f"添加群组监听失败: {group}", "WARNING")
                except Exception as e:
                    log(f"添加群组监听异常: {e}", "ERROR")

    def message_callback(self, msg, chat):
        """消息回调函数"""
        try:
            # 获取基本信息
            chat_name = getattr(chat, 'who', 'Unknown')
            sender = getattr(msg, 'sender', 'Unknown')
            content = getattr(msg, 'content', '')


            log(f"收到消息 - 窗口:{chat_name} 发送人:{sender} 内容:{content[:50]}...")

            # 如果启用了消息队列，将消息加入队列
            if self.message_queue_manager and self.message_queue_manager.is_running:
                # 判断消息类型
                if isinstance(msg, FriendMessage):
                    message_type = "friend"
                elif isinstance(msg, SystemMessage):
                    message_type = "system"
                else:
                    # 对于其他类型的消息，记录详细信息以便调试
                    message_type = "unknown"
                    log(f"检测到未知消息类型: {type(msg).__name__}, 内容: {content[:30]}...", "DEBUG")

                # 判断是否为群聊
                is_group = chat_name in config.GROUP_LIST

                # 将消息加入队列
                success = self.message_queue_manager.add_message(
                    msg=msg,
                    chat=chat,
                    chat_name=chat_name,
                    sender=sender,
                    content=content,
                    is_group=is_group,
                    message_type=message_type
                )

                if not success:
                    # 队列满了，直接处理消息
                    # log("消息队列已满，直接处理消息", "WARNING")
                    self._handle_message_direct(msg, chat)
            else:
                # 消息队列未启用，直接处理消息
                self._handle_message_direct(msg, chat)

        except Exception as e:
            log(f"消息回调处理异常: {str(e)}", "ERROR")
            print(traceback.format_exc())

    def _handle_message_direct(self, msg, chat):
        """直接处理消息（不通过队列）"""
        try:
            # 处理不同类型的消息
            if isinstance(msg, FriendMessage):
                self.handle_friend_message(msg, chat)
            elif isinstance(msg, SystemMessage):
                self.handle_system_message(msg, chat)
        except Exception as e:
            log(f"直接处理消息异常: {str(e)}", "ERROR")

    def handle_friend_message_direct(self, msg, chat):
        """直接处理好友消息（供消息队列调用）"""
        return self.handle_friend_message(msg, chat)

    def handle_friend_message(self, msg, chat):
        """处理好友消息"""
        try:
            chat_name = getattr(chat, 'who', 'Unknown')
            sender = getattr(msg, 'sender', 'Unknown')
            content = getattr(msg, 'content', '')

            if not content:
                return

            # 清理消息内容
            cleaned_content = self.clean_message_content(content)
            # 如果清理后内容为空，但原消息不为空（可能是只@了机器人），则使用@提示信息
            if not cleaned_content and content.strip():
                cleaned_content = "我@了你"  # @提示信息

            # 检查消息长度限制
            max_message_length = getattr(config, 'MAX_MESSAGE_LENGTH', 240)
            if len(cleaned_content) > max_message_length:
                log(f"消息超过长度限制({max_message_length}字)，跳过处理 - 发送人:{sender} 长度:{len(cleaned_content)} 内容:{cleaned_content[:50]}...")

                # 发送提醒消息给用户
                reminder_template = getattr(config, 'MESSAGE_TOO_LONG_REMINDER',
                    "您的消息太长了，请发送不超过{max_length}字的消息哦~")
                reminder_message = reminder_template.format(
                    max_length=max_message_length,
                    current_length=len(cleaned_content)
                )

                # 判断是否为群聊，决定发送方式
                is_group = chat_name in config.GROUP_LIST
                try:
                    if is_group:
                        chat.SendMsg(msg=reminder_message, at=sender)
                    else:
                        chat.SendMsg(msg=reminder_message)
                    log(f"已发送消息长度提醒给用户: {sender}")
                except Exception as e:
                    log(f"发送消息长度提醒失败: {str(e)}", "ERROR")

                return

            # 判断是否为群聊
            is_group = chat_name in config.GROUP_LIST

            # 群聊消息处理
            if is_group:
                # 缓存所有群聊消息内容（无论是否@机器人）
                if getattr(config, 'ENABLE_GROUP_CONTENT_CACHE', True) and cleaned_content:
                    self.memory_manager.save_group_content_cache(
                        group_id=chat_name,
                        sender_name=sender,
                        message_content=cleaned_content
                    )

                # 检查是否@了机器人
                is_at_message = self.is_at_message(content, config.ROBOT_WX_NAME)

                # 智能群聊参与决策
                should_reply = False
                participation_reason = ""
                participation_score = 0.0

                if is_at_message:
                    # 如果被@了，一定回复
                    should_reply = True
                    participation_reason = "被@消息"
                    participation_score = 1.0
                    # 移除@信息后重新清理内容
                    cleaned_content = self.clean_message_content(content)
                    if not cleaned_content:
                        cleaned_content = "我@了你"

                    # 检查是否为签到消息
                    if self.is_checkin_message(cleaned_content):
                        _, message = self.checkin_manager.user_checkin(chat_name, sender)
                        chat.SendMsg(remove_brackets_content(message))
                        return  # 签到处理完成，直接返回
                else:
                    # 使用智能参与管理器判断是否回复
                    should_reply, participation_reason, participation_score = \
                        self.group_participation_manager.should_participate(
                            chat_name, sender, cleaned_content, config
                        )

                # 记录参与决策日志
                if getattr(config, 'ENABLE_GROUP_PARTICIPATION_STATS', True):
                    log(f"群聊参与决策 - 群:{chat_name} 发送人:{sender} "
                        f"是否回复:{should_reply} 评分:{participation_score:.2f} "
                        f"原因:{participation_reason}")

                # 如果决定不回复，直接返回
                if not should_reply:
                    return

            # 检查是否为管理员命令
            if sender in config.ADMIN_LIST:
                if self.handle_admin_command(cleaned_content, chat, sender):
                    return

            # 检查是否为天气查询
            if self.weather_manager.is_weather_request(cleaned_content):
                self.handle_weather_request(cleaned_content, chat, sender, is_group)
                return

            # 检查是否为好感度查询
            if FavorabilityManager.is_favorability_query(cleaned_content):
                self.handle_favorability_query(sender, chat, is_group)
                return

            # 检查是否为定时提醒请求
            if getattr(config, 'ENABLE_REMINDER', False) and self.reminder_manager.is_reminder_request(cleaned_content):
                self.handle_reminder_request(cleaned_content, sender, chat_name, is_group, chat)
                return

            # 检查是否为图片生成请求
            if getattr(config, 'ENABLE_IMAGE_GENERATION', False) and self.image_generator and is_image_request(cleaned_content):
                self.handle_image_generation_request(cleaned_content, sender, chat_name, is_group, chat)
                return

            # 生成AI回复
            # 判断是否为智能插话（非@消息的群聊回复）
            is_smart_participation = is_group and not is_at_message and should_reply
            self.generate_and_send_reply(cleaned_content, chat_name, sender, chat, is_group, is_smart_participation)

        except Exception as e:
            log(f"处理好友消息异常: {str(e)}", "ERROR")

    def handle_weather_request(self, content, chat, sender, is_group=False):
        """处理天气查询请求"""
        try:
            city = self.weather_manager.extract_city(content)
            if not city:
                reply = "想查哪个城市的天气呢？"
            else:
                reply = self.weather_manager.get_weather(city)

            # 移除括号内容
            cleaned_reply = remove_brackets_content(reply)

            # 发送天气信息
            if is_group:
                chat.SendMsg(msg=cleaned_reply, at=sender)
            else:
                chat.SendMsg(msg=cleaned_reply)

            log(f"天气查询回复发送成功: {city if city else '未知城市'}")

        except Exception as e:
            log(f"处理天气查询异常: {str(e)}", "ERROR")
            error_reply = "天气查询失败，请稍后再试"
            # 移除括号内容
            cleaned_error_reply = remove_brackets_content(error_reply)
            if is_group:
                chat.SendMsg(msg=cleaned_error_reply, at=sender)
            else:
                chat.SendMsg(msg=cleaned_error_reply)

    def handle_favorability_query(self, sender, chat, is_group=False):
        """处理好感度查询请求"""
        try:
            reply = FavorabilityManager.get_favorability_query_reply(sender)

            # 移除括号内容
            cleaned_reply = remove_brackets_content(reply)

            # 发送好感度信息
            if is_group:
                chat.SendMsg(msg=cleaned_reply, at=sender)
            else:
                chat.SendMsg(msg=cleaned_reply)

            log(f"好感度查询回复发送成功: {sender}")

        except Exception as e:
            log(f"处理好感度查询异常: {str(e)}", "ERROR")
            error_reply = "好感度查询失败，请稍后再试"
            # 移除括号内容
            cleaned_error_reply = remove_brackets_content(error_reply)
            if is_group:
                chat.SendMsg(msg=cleaned_error_reply, at=sender)
            else:
                chat.SendMsg(msg=cleaned_error_reply)

    def handle_reminder_request(self, content, sender, chat_name, is_group, chat):
        """处理定时提醒请求"""
        try:
            # 使用提醒管理器处理请求
            reply = self.reminder_manager.handle_reminder_request(
                content=content,
                user_id=sender,
                chat_id=chat_name,
                is_group=is_group,
                chat=chat
            )

            # 发送确认回复
            if reply:
                # 移除括号内容
                cleaned_reply = remove_brackets_content(reply)

                if is_group:
                    chat.SendMsg(msg=cleaned_reply, at=sender)
                else:
                    chat.SendMsg(msg=cleaned_reply)

                log(f"定时提醒设置回复发送成功: {sender}")

        except Exception as e:
            log(f"处理定时提醒请求异常: {str(e)}", "ERROR")
            error_reply = "定时提醒设置失败，请稍后再试"
            # 移除括号内容
            cleaned_error_reply = remove_brackets_content(error_reply)
            if is_group:
                chat.SendMsg(msg=cleaned_error_reply, at=sender)
            else:
                chat.SendMsg(msg=cleaned_error_reply)

    def handle_image_generation_request(self, content, sender, _, is_group, chat):
        """处理图片生成请求"""
        try:
            if not self.image_generator:
                error_reply = "图片生成功能未启用"
                cleaned_error_reply = remove_brackets_content(error_reply)
                if is_group:
                    chat.SendMsg(msg=cleaned_error_reply, at=sender)
                else:
                    chat.SendMsg(msg=cleaned_error_reply)
                return

            # 发送生成中的提示消息
            generating_reply = "正在为你生成图片，请稍等..."
            cleaned_generating_reply = remove_brackets_content(generating_reply)
            if is_group:
                chat.SendMsg(msg=cleaned_generating_reply, at=sender)
            else:
                chat.SendMsg(msg=cleaned_generating_reply)

            log(f"开始处理图片生成请求: {content}")

            # 获取图片生成服务类型
            service = getattr(config, 'IMAGE_GENERATION_SERVICE', 'auto')

            # 生成图片
            image_path = self.image_generator.generate_image(content, service)

            if image_path and os.path.exists(image_path):
                # 图片生成成功，发送图片
                try:
                    chat.SendFiles(filepath=image_path)
                    log(f"图片发送成功: {image_path}")

                    # 根据配置决定是否删除本地文件
                    if getattr(config, 'AUTO_DELETE_SENT_IMAGES', True):
                        try:
                            os.remove(image_path)
                            log(f"本地图片文件已删除: {image_path}")
                        except Exception as delete_e:
                            log(f"删除本地图片文件失败: {str(delete_e)}", "WARNING")

                    # 发送成功消息
                    success_message = getattr(config, 'IMAGE_GENERATION_SUCCESS_MESSAGE', '图片生成完成啦！')
                    cleaned_success_message = remove_brackets_content(success_message)
                    if is_group:
                        chat.SendMsg(msg=cleaned_success_message, at=sender)
                    else:
                        chat.SendMsg(msg=cleaned_success_message)

                except Exception as send_e:
                    log(f"图片发送失败: {str(send_e)}", "ERROR")

                    # 发送失败时根据配置决定是否删除本地文件（避免文件积累）
                    if getattr(config, 'AUTO_DELETE_SENT_IMAGES', True):
                        try:
                            os.remove(image_path)
                            log(f"发送失败，本地图片文件已删除: {image_path}")
                        except Exception as delete_e:
                            log(f"删除本地图片文件失败: {str(delete_e)}", "WARNING")

                    error_reply = "图片生成成功，但发送失败了"
                    cleaned_error_reply = remove_brackets_content(error_reply)
                    if is_group:
                        chat.SendMsg(msg=cleaned_error_reply, at=sender)
                    else:
                        chat.SendMsg(msg=cleaned_error_reply)
            else:
                # 图片生成失败
                log("图片生成失败", "ERROR")
                error_message = getattr(config, 'IMAGE_GENERATION_FAILED_MESSAGE', '抱歉，图片生成失败了，请稍后再试~')
                cleaned_error_message = remove_brackets_content(error_message)
                if is_group:
                    chat.SendMsg(msg=cleaned_error_message, at=sender)
                else:
                    chat.SendMsg(msg=cleaned_error_message)

        except Exception as e:
            log(f"处理图片生成请求异常: {str(e)}", "ERROR")
            error_reply = "图片生成过程中出现异常，请稍后再试"
            cleaned_error_reply = remove_brackets_content(error_reply)
            if is_group:
                chat.SendMsg(msg=cleaned_error_reply, at=sender)
            else:
                chat.SendMsg(msg=cleaned_error_reply)

    def handle_system_message(self, msg, chat):
        """处理系统消息"""
        try:
            content = getattr(msg, 'content', '')
            chat_name = getattr(chat, 'who', 'Unknown')
            sender = getattr(msg, 'sender', 'Unknown')

            log(f"系统消息 [{chat_name}]: {content}")

            # 检查是否为拍一拍消息
            if self.is_pat_message(content):
                self.handle_pat_message(content, sender, chat_name, chat)

        except Exception as e:
            log(f"处理系统消息异常: {str(e)}", "ERROR")

    def is_pat_message(self, content):
        """检查是否为拍一拍消息"""
        if not content:
            return False

        # 检查拍一拍的关键词
        pat_keywords = [
            "拍了拍我",
            "拍一拍我",
            "轻拍了我",
            f"拍了拍 {config.ROBOT_WX_NAME}",
            f"拍了拍{config.ROBOT_WX_NAME}",
        ]

        for keyword in pat_keywords:
            if keyword in content:
                return True

        return False

    def extract_pat_sender(self, content):
        """从拍一拍消息内容中提取真正的发送者"""
        if not content:
            return "Unknown"

        # 常见的拍一拍消息格式：
        # "张三拍了拍你"
        # "李四拍了拍 林小葵"
        # "王五轻拍了你"
        # "丨[1条]拍了拍你"

        import re

        # 尝试匹配各种拍一拍格式
        patterns = [
            r'^(.+?)拍了拍我',  # 匹配 "用户名拍了拍..."
            r'^(.+?)轻拍了我',  # 匹配 "用户名轻拍了..."
            r'^(.+?)拍一拍我',  # 匹配 "用户名拍一拍..."
        ]

        for pattern in patterns:
            match = re.match(pattern, content)
            if match:
                sender_name = match.group(1).strip()

                # 清理用户名中的额外信息
                sender_name = self.clean_sender_name(sender_name)

                return sender_name

        # 如果都匹配不到，返回Unknown
        return "Unknown"

    def clean_sender_name(self, sender_name):
        """清理发送者名称中的额外信息"""
        if not sender_name:
            return "Unknown"

        import re

        # 去除各种可能的额外信息
        # 1. 去除引号，如 '"丨"' -> '丨'
        sender_name = re.sub(r'^["\']|["\']$', '', sender_name)

        # 2. 去除方括号内容，如 "[1条]", "[未读]", "[3条消息]" 等
        sender_name = re.sub(r'\[.*?\]', '', sender_name)

        # 3. 去除圆括号内容，如 "(群昵称)", "(在线)" 等
        sender_name = re.sub(r'\(.*?\)', '', sender_name)

        # 4. 去除花括号内容，如 "{状态}" 等
        sender_name = re.sub(r'\{.*?\}', '', sender_name)

        # 5. 去除常见的状态标识
        status_patterns = [
            r'未读\d*',
            r'\d+条',
            r'在线',
            r'离线',
            r'忙碌',
            r'离开',
        ]

        for status_pattern in status_patterns:
            sender_name = re.sub(status_pattern, '', sender_name)

        # 6. 清理多余的空白字符
        sender_name = re.sub(r'\s+', ' ', sender_name).strip()

        # 7. 再次去除可能残留的引号
        sender_name = sender_name.strip('"\'')

        # 8. 如果清理后为空，返回Unknown
        if not sender_name:
            return "Unknown"

        return sender_name

    def _build_user_identity_info(self, sender_name, group_id=None):
        """构建用户身份识别信息，帮助AI更好地区分不同用户"""
        try:
            identity_parts = []

            # 1. 基础身份信息
            if group_id:
                identity_parts.append(f"来自群聊: {group_id}")
            else:
                identity_parts.append("私聊用户")

            # 2. 用户类型标识
            if sender_name in config.ADMIN_LIST:
                identity_parts.append("身份: 管理员")
            elif sender_name in config.LISTEN_LIST:
                identity_parts.append("身份: 监听用户")
            else:
                identity_parts.append("身份: 普通用户")

            # 3. 好感度信息（如果启用）
            try:
                from utils.favorability import FavorabilityManager
                fav_info = FavorabilityManager.get_favorability_info(sender_name)
                if fav_info and fav_info.get('favorability') is not None:
                    fav_level = fav_info.get('level', '未知')
                    fav_value = fav_info.get('favorability', 0)
                    identity_parts.append(f"好感度: {fav_level}({fav_value:.1f})")
            except Exception:
                pass

            # 4. 历史交互信息
            try:
                # 获取用户的交互次数
                memory_key = self.memory_manager.get_memory_key(
                    sender_name if not group_id else group_id,
                    bool(group_id)
                )
                interaction_count = self.memory_manager.get_user_interaction_count(sender_name, memory_key)
                if interaction_count > 0:
                    if interaction_count == 1:
                        identity_parts.append("交互状态: 首次对话")
                    elif interaction_count <= 5:
                        identity_parts.append(f"交互状态: 新用户(第{interaction_count}次对话)")
                    elif interaction_count <= 20:
                        identity_parts.append(f"交互状态: 熟悉用户(共{interaction_count}次对话)")
                    else:
                        identity_parts.append(f"交互状态: 老朋友(共{interaction_count}次对话)")
                else:
                    identity_parts.append("交互状态: 首次对话")
            except Exception:
                identity_parts.append("交互状态: 首次对话")

            # 5. 时间相关信息
            try:
                from datetime import datetime
                current_hour = datetime.now().hour
                if 6 <= current_hour < 12:
                    time_period = "上午"
                elif 12 <= current_hour < 18:
                    time_period = "下午"
                elif 18 <= current_hour < 22:
                    time_period = "晚上"
                else:
                    time_period = "深夜"
                identity_parts.append(f"时段: {time_period}")
            except Exception:
                pass

            # 组合所有信息
            if identity_parts:
                return f"\n[用户信息: {' | '.join(identity_parts)}]"
            else:
                return ""

        except Exception as e:
            log(f"构建用户身份信息失败: {str(e)}", "WARNING")
            return ""

    def handle_pat_message(self, content, sender, chat_name, chat):
        """处理拍一拍消息"""
        try:
            # 判断是否为群聊
            is_group = chat_name in config.GROUP_LIST

            # 从消息内容中提取真正的发送者名称
            real_sender = self.extract_pat_sender(content)

            log(f"收到拍一拍 - 真实发送者:{real_sender} 系统发送者:{sender} 聊天:{chat_name} 内容:{content}")

            # 检查是否在监听列表中
            if not is_group and chat_name not in config.LISTEN_LIST:
                log(f"私聊 {chat_name} 不在监听列表中，跳过拍一拍处理")
                return

            if is_group and chat_name not in config.GROUP_LIST:
                log(f"群聊 {chat_name} 不在群聊列表中，跳过拍一拍处理")
                return

            # 生成拍一拍回复，使用真实的发送者名称
            self.generate_and_send_reply("我拍了拍你", chat_name, real_sender, chat, is_group, False)

        except Exception as e:
            log(f"处理拍一拍消息异常: {str(e)}", "ERROR")

    def clean_message_content(self, content):
        """清理消息内容"""
        if not content:
            return ""

        # 移除@信息
        content = re.sub(r'@[^\s]+\s*', '', content)

        # 移除多余的空白字符
        content = re.sub(r'\s+', ' ', content).strip()

        return content

    def is_at_message(self, content, bot_name):
        """检查消息是否@了机器人或提到了机器人"""
        if not content or not bot_name:
            return False

        # 检查是否包含@机器人名称
        at_pattern = rf'@{re.escape(bot_name)}'
        if re.search(at_pattern, content):
            return True

        # 检查是否直接提到机器人名称或昵称
        # 支持：林小葵、小葵
        mention_keywords = [bot_name, "小葵"]

        for keyword in mention_keywords:
            # 检查关键词是否在消息中，且不是其他词的一部分
            if keyword in content:
                # 找到所有匹配位置
                start = 0
                while True:
                    pos = content.find(keyword, start)
                    if pos == -1:
                        break

                    # 检查前后字符，确保是独立的词
                    before_char = content[pos-1] if pos > 0 else ' '
                    after_char = content[pos+len(keyword)] if pos+len(keyword) < len(content) else ' '

                    # 检查前后字符，确保不是英文字母数字或中文字符的连续部分
                    before_ok = not (before_char.isascii() and (before_char.isalpha() or before_char.isdigit()))
                    after_ok = not (after_char.isascii() and (after_char.isalpha() or after_char.isdigit()))

                    # 对于中文字符，也要避免连续匹配（如"小葵子"中的"小葵"）
                    if after_char and '\u4e00' <= after_char <= '\u9fff':
                        # 如果后面是中文字符，检查是否为常见的后缀
                        common_suffixes = ['子', '葵', '儿', '妹', '姐', '哥', '弟']
                        if after_char in common_suffixes:
                            after_ok = False

                    if before_ok and after_ok:
                        return True

                    start = pos + 1

        return False

    def is_checkin_message(self, content):
        """检查消息是否为签到消息"""
        if not content:
            return False

        # 签到关键词
        checkin_keywords = [
            "签到", "打卡", "报到", "check in", "checkin",
            "签个到", "打个卡", "来签到", "我签到"
        ]

        content_lower = content.lower().strip()

        for keyword in checkin_keywords:
            if keyword in content_lower:
                return True

        return False

    def handle_admin_command(self, content, chat, sender):
        """处理管理员命令"""
        # 确保正确导入config模块，解决作用域问题
        import config
        
        if not content.startswith('/'):
            return False

        try:
            parts = content.split()
            command = parts[0][1:]  # 移除/
            full_content = content  # 保存完整内容

            if command == "help" or command == "帮助":
                help_text = self.get_help_text()
                chat.SendMsg(remove_brackets_content(help_text))
                return True

            elif command == "status" or command == "状态":
                status_text = self.get_status_text()
                chat.SendMsg(remove_brackets_content(status_text))
                return True

            elif command == "add_user" and len(parts) >= 2:
                username = parts[1]
                if username not in config.LISTEN_LIST:
                    config.LISTEN_LIST.append(username)
                    # 动态添加监听
                    self.wx.AddListenChat(nickname=username, callback=self.message_callback)
                    chat.SendMsg(remove_brackets_content(f"已添加用户监听: {username}"))
                else:
                    chat.SendMsg(remove_brackets_content(f"用户 {username} 已在监听列表中"))
                return True

            elif command == "remove_user" and len(parts) >= 2:
                username = parts[1]
                if username in config.LISTEN_LIST:
                    config.LISTEN_LIST.remove(username)
                    # 动态移除监听
                    self.wx.RemoveListenChat(username)
                    chat.SendMsg(remove_brackets_content(f"已移除用户监听: {username}"))
                else:
                    chat.SendMsg(remove_brackets_content(f"用户 {username} 不在监听列表中"))
                return True

            elif command == "add_group" and len(parts) >= 2:
                groupname = parts[1]
                if groupname not in config.GROUP_LIST:
                    config.GROUP_LIST.append(groupname)
                    # 动态添加监听
                    self.wx.AddListenChat(nickname=groupname, callback=self.message_callback)
                    chat.SendMsg(remove_brackets_content(f"已添加群组监听: {groupname}"))
                else:
                    chat.SendMsg(remove_brackets_content(f"群组 {groupname} 已在监听列表中"))
                return True

            elif command == "remove_group" and len(parts) >= 2:
                groupname = parts[1]
                if groupname in config.GROUP_LIST:
                    config.GROUP_LIST.remove(groupname)
                    # 动态移除监听
                    self.wx.RemoveListenChat(groupname)
                    chat.SendMsg(remove_brackets_content(f"已移除群组监听: {groupname}"))
                else:
                    chat.SendMsg(remove_brackets_content(f"群组 {groupname} 不在监听列表中"))
                return True

            elif command == "test_ai":
                if self.test_api_connection():
                    chat.SendMsg(remove_brackets_content("AI连接测试成功"))
                else:
                    chat.SendMsg(remove_brackets_content("AI连接测试失败"))
                return True

            elif command == "memory_stats":
                # 显示记忆统计
                stats = self.memory_manager.get_memory_stats()
                stats_text = f"""记忆统计信息：
总记忆数: {stats.get('total_memories', 0)}
用户记忆: {stats.get('user_memories', 0)}
群组记忆: {stats.get('group_memories', 0)}
总消息数: {stats.get('total_messages', 0)}
缓存大小: {stats.get('cache_size', 0)}"""
                chat.SendMsg(remove_brackets_content(stats_text))
                return True

            elif command == "prompt_status" or command == "提示词状态":
                # 显示提示词加载状态
                prompt_status = self.get_prompt_status()
                chat.SendMsg(remove_brackets_content(prompt_status))
                return True

            elif command == "reload_prompts" or command == "重载提示词":
                # 重新加载提示词
                self.load_prompt()
                chat.SendMsg(remove_brackets_content("提示词已重新加载"))
                return True

            elif command.startswith("clear_memory"):
                # 清除记忆 /clear_memory 用户名/群名
                parts = full_content.split()
                if len(parts) >= 2:
                    target = parts[1]
                    # 判断是用户还是群组
                    if target in config.LISTEN_LIST:
                        memory_key = self.memory_manager.get_memory_key(target, False)
                    elif target in config.GROUP_LIST:
                        memory_key = self.memory_manager.get_memory_key(target, True)
                    else:
                        chat.SendMsg(remove_brackets_content(f"未找到目标: {target}"))
                        return True

                    if self.memory_manager.clear_memory(memory_key):
                        chat.SendMsg(remove_brackets_content(f"已清除 {target} 的记忆"))
                    else:
                        chat.SendMsg(remove_brackets_content(f"清除 {target} 的记忆失败"))
                else:
                    chat.SendMsg(remove_brackets_content("用法: /clear_memory 用户名/群名"))
                return True

            elif command == "cleanup_memory":
                # 清理旧记忆
                self.memory_manager.cleanup_old_memories(config.MEMORY_RETENTION_DAYS)
                chat.SendMsg(remove_brackets_content("旧记忆清理完成"))
                return True

            elif command == "group_stats" and len(parts) >= 2:
                # 查看群聊参与统计 /group_stats 群名
                group_name = parts[1]
                if group_name in config.GROUP_LIST:
                    stats = self.group_participation_manager.get_participation_stats(group_name)
                    stats_text = f"""群聊参与统计 - {group_name}：
总消息数: {stats['total_messages']}
活跃用户数: {stats['active_users_count']}
最近消息: {stats['recent_messages_count']}条
上次回复: {stats['last_reply_ago']:.0f}秒前
上次消息: {stats['last_message_ago']:.0f}秒前
参与评分: {stats['last_participation_score']:.2f}"""
                    chat.SendMsg(remove_brackets_content(stats_text))
                else:
                    chat.SendMsg(remove_brackets_content(f"群组 {group_name} 不在监听列表中"))
                return True

            elif command == "participation_mode" and len(parts) >= 2:
                # 切换群聊参与模式 /participation_mode smart/at_only/all
                mode = parts[1].lower()
                if mode in ['smart', 'at_only', 'all']:
                    config.GROUP_PARTICIPATION_MODE = mode
                    mode_desc = {
                        'smart': '智能模式',
                        'at_only': '仅@回复模式',
                        'all': '全部回复模式'
                    }
                    chat.SendMsg(remove_brackets_content(f"群聊参与模式已切换为: {mode_desc[mode]}"))
                else:
                    chat.SendMsg(remove_brackets_content("可用模式: smart, at_only, all"))
                return True

            elif command == "emotion_status" or command == "情感状态":
                # 查看AI情感状态 /emotion_status
                if not self.emotion_manager:
                    chat.SendMsg(remove_brackets_content("情感状态系统未启用"))
                    return True

                try:
                    stats = self.emotion_manager.get_emotion_stats()
                    dominant_emotion, intensity = self.emotion_manager.get_dominant_emotion()

                    status_text = f"""【AI情感状态】
主导情感: {stats['dominant_emotion']} ({intensity:.2f})
基础情感: {stats['base_emotion']}
活跃情感数: {stats['active_emotions_count']}
历史记录数: {stats['total_history_count']}

活跃情感详情:"""

                    if stats['active_emotions']:
                        for emotion_name, emotion_list in stats['active_emotions'].items():
                            status_text += f"\n• {emotion_name}:"
                            for emotion_data in emotion_list:
                                status_text += f"\n  - 强度:{emotion_data['intensity']:.2f} 开始:{emotion_data['start_time']} 原因:{emotion_data['trigger']}"
                    else:
                        status_text += "\n暂无活跃情感"

                    chat.SendMsg(remove_brackets_content(status_text))
                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"获取情感状态失败: {str(e)}"))
                return True

            elif command == "reset_emotion" or command == "重置情感":
                # 重置AI情感状态 /reset_emotion
                if not self.emotion_manager:
                    chat.SendMsg(remove_brackets_content("情感状态系统未启用"))
                    return True

                try:
                    self.emotion_manager.reset_emotions()
                    chat.SendMsg(remove_brackets_content("AI情感状态已重置"))
                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"重置情感状态失败: {str(e)}"))
                return True

            elif command == "set_emotion" and len(parts) >= 3:
                # 设置AI情感状态 /set_emotion 情感类型 强度 [持续时间] [原因]
                if not self.emotion_manager:
                    chat.SendMsg(remove_brackets_content("情感状态系统未启用"))
                    return True

                try:
                    from utils.emotion_state import EmotionType, EmotionIntensity

                    emotion_name = parts[1].lower()
                    intensity_value = int(parts[2])
                    duration = int(parts[3]) if len(parts) > 3 else 3600
                    reason = " ".join(parts[4:]) if len(parts) > 4 else f"管理员手动设置"

                    # 验证情感类型
                    emotion_type = None
                    for et in EmotionType:
                        if et.value == emotion_name:
                            emotion_type = et
                            break

                    if not emotion_type:
                        available_emotions = [et.value for et in EmotionType]
                        chat.SendMsg(remove_brackets_content(f"无效的情感类型。可用类型: {', '.join(available_emotions)}"))
                        return True

                    # 验证强度
                    if intensity_value < 1 or intensity_value > 5:
                        chat.SendMsg(remove_brackets_content("强度必须在1-5之间"))
                        return True

                    emotion_intensity = EmotionIntensity(intensity_value)

                    # 添加情感状态
                    self.emotion_manager.add_emotion(emotion_type, emotion_intensity, duration, reason)

                    chat.SendMsg(remove_brackets_content(f"已设置情感状态: {emotion_type.value} (强度:{intensity_value}, 持续:{duration}秒)"))

                except ValueError:
                    chat.SendMsg(remove_brackets_content("用法: /set_emotion 情感类型 强度(1-5) [持续时间(秒)] [原因]"))
                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"设置情感状态失败: {str(e)}"))
                return True

            elif command == "emotion_help" or command == "情感帮助":
                # 情感状态系统帮助 /emotion_help
                help_text = """【情感状态系统帮助】

可用命令:
/emotion_status - 查看当前AI情感状态
/reset_emotion - 重置所有情感状态
/set_emotion 类型 强度 [时间] [原因] - 手动设置情感

情感类型:
happy(开心), excited(兴奋), calm(平静), sad(悲伤),
angry(愤怒), anxious(焦虑), surprised(惊讶),
confused(困惑), tired(疲惫), energetic(精力充沛),
lonely(孤独), grateful(感激), curious(好奇),
bored(无聊), neutral(中性)

强度等级: 1(很低) 2(低) 3(中等) 4(高) 5(很高)

示例:
/set_emotion happy 3 1800 测试开心情感
/emotion_status
/reset_emotion"""

                chat.SendMsg(remove_brackets_content(help_text))
                return True

            elif command == "group_members" and len(parts) >= 2:
                # 获取群聊成员列表 /group_members 群名
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                group_name = parts[1]
                members = self.group_manager.get_group_members(group_name)
                if members:
                    member_list = "\n".join([f"{i+1}. {member}" for i, member in enumerate(members)])
                    reply = f"群聊 {group_name} 成员列表（共{len(members)}人）：\n{member_list}"
                    chat.SendMsg(remove_brackets_content(reply))
                else:
                    chat.SendMsg(remove_brackets_content(f"获取群聊 {group_name} 成员列表失败"))
                return True

            elif command == "add_group_member" and len(parts) >= 3:
                # 添加群聊成员 /add_group_member 群名 成员名 [申请理由]
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                group_name = parts[1]
                member_name = parts[2]
                reason = " ".join(parts[3:]) if len(parts) > 3 else None

                success = self.group_manager.add_group_members(group_name, member_name, reason)
                if success:
                    chat.SendMsg(remove_brackets_content(f"成功添加 {member_name} 到群聊 {group_name}"))
                else:
                    chat.SendMsg(remove_brackets_content(f"添加群聊成员失败"))
                return True

            elif command == "remove_group_member" and len(parts) >= 3:
                # 移除群聊成员 /remove_group_member 群名 成员名
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                group_name = parts[1]
                member_name = parts[2]

                success = self.group_manager.remove_group_members(group_name, member_name)
                if success:
                    chat.SendMsg(remove_brackets_content(f"成功从群聊 {group_name} 移除 {member_name}"))
                else:
                    chat.SendMsg(remove_brackets_content(f"移除群聊成员失败"))
                return True

            elif command == "group_info" and len(parts) >= 2:
                # 获取群聊信息 /group_info 群名
                group_name = parts[1]
                info = self.group_manager.get_group_info(group_name)
                if info:
                    info_text = f"群聊 {group_name} 信息：\n"
                    for key, value in info.items():
                        info_text += f"{key}: {value}\n"
                    chat.SendMsg(remove_brackets_content(info_text))
                else:
                    chat.SendMsg(remove_brackets_content(f"获取群聊 {group_name} 信息失败"))
                return True

            elif command == "at_all" and len(parts) >= 3:
                # @所有人发送消息 /at_all 群名 消息内容
                group_name = parts[1]
                message = " ".join(parts[2:])

                success = self.group_manager.at_all_members(group_name, message)
                if success:
                    chat.SendMsg(remove_brackets_content(f"成功在群聊 {group_name} @所有人发送消息"))
                else:
                    chat.SendMsg(remove_brackets_content(f"@所有人发送消息失败"))
                return True

            elif command == "recent_groups":
                # 获取最近群聊列表 /recent_groups
                groups = self.group_manager.get_all_recent_groups()
                if groups:
                    group_list = "\n".join([f"{i+1}. {group}" for i, group in enumerate(groups)])
                    reply = f"最近群聊列表（共{len(groups)}个）：\n{group_list}"
                    chat.SendMsg(remove_brackets_content(reply))
                else:
                    chat.SendMsg(remove_brackets_content("获取最近群聊列表失败"))
                return True

            elif command == "contact_groups":
                # 获取通讯录群聊列表 /contact_groups
                groups = self.group_manager.get_contact_groups()
                if groups:
                    group_list = "\n".join([f"{i+1}. {group}" for i, group in enumerate(groups)])
                    reply = f"通讯录群聊列表（共{len(groups)}个）：\n{group_list}"
                    chat.SendMsg(remove_brackets_content(reply))
                else:
                    chat.SendMsg(remove_brackets_content("获取通讯录群聊列表失败"))
                return True

            elif command == "group_settings" and len(parts) >= 2:
                # 修改群聊设置 /group_settings 群名 [name=新群名] [remark=新备注] [myname=我的群昵称] [notice=群公告]
                group_name = parts[1]
                settings = {}

                # 解析设置参数
                for part in parts[2:]:
                    if '=' in part:
                        key, value = part.split('=', 1)
                        settings[key] = value

                if settings:
                    success = self.group_manager.manage_group_settings(group_name, **settings)
                    if success:
                        settings_desc = ", ".join([f"{k}={v}" for k, v in settings.items()])
                        chat.SendMsg(remove_brackets_content(f"成功修改群聊 {group_name} 设置: {settings_desc}"))
                    else:
                        chat.SendMsg(remove_brackets_content(f"修改群聊设置失败"))
                else:
                    chat.SendMsg(remove_brackets_content("用法: /group_settings 群名 key=value [key2=value2...]"))
                return True

            elif command == "enable_checkin" and len(parts) >= 2:
                # 启用群聊签到功能 /enable_checkin 群名 [reminder_time=10:00] [stats_time=23:00] [kick_time=23:30] [max_absent_days=3]
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                group_name = parts[1]
                config_params = {}

                # 解析配置参数
                for part in parts[2:]:
                    if '=' in part:
                        key, value = part.split('=', 1)
                        if key in ['reminder_time', 'stats_time', 'kick_time']:
                            config_params[key] = value
                        elif key == 'max_absent_days':
                            try:
                                config_params[key] = int(value)
                            except ValueError:
                                chat.SendMsg(remove_brackets_content(f"无效的缺勤天数: {value}"))
                                return True

                success = self.checkin_manager.enable_group_checkin(group_name, **config_params)
                if success:
                    chat.SendMsg(remove_brackets_content(f"群聊 {group_name} 签到功能已启用"))
                else:
                    chat.SendMsg(remove_brackets_content(f"启用群聊 {group_name} 签到功能失败"))
                return True

            elif command == "disable_checkin" and len(parts) >= 2:
                # 禁用群聊签到功能 /disable_checkin 群名
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                group_name = parts[1]
                success = self.checkin_manager.disable_group_checkin(group_name)
                if success:
                    chat.SendMsg(remove_brackets_content(f"群聊 {group_name} 签到功能已禁用"))
                else:
                    chat.SendMsg(remove_brackets_content(f"禁用群聊 {group_name} 签到功能失败"))
                return True

            elif command == "checkin_stats" and len(parts) >= 2:
                # 查看签到统计 /checkin_stats 群名 [日期]
                group_name = parts[1]
                date = parts[2] if len(parts) > 2 else None

                stats = self.checkin_manager.get_daily_checkin_stats(group_name, date)
                if stats:
                    message = f"""📊 {stats['group_name']} 签到统计

📅 日期：{stats['date']}
👥 总人数：{stats['total_members']}
✅ 已签到：{stats['checkin_count']}
❌ 未签到：{stats['not_checkin_count']}
📈 签到率：{stats['checkin_rate']:.1f}%"""

                    chat.SendMsg(remove_brackets_content(message))
                else:
                    chat.SendMsg(remove_brackets_content(f"获取群聊 {group_name} 签到统计失败"))
                return True

            elif command == "checkin_reminder" and len(parts) >= 2:
                # 手动发送签到提醒 /checkin_reminder 群名
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                group_name = parts[1]
                if self.checkin_scheduler:
                    success = self.checkin_scheduler.manual_reminder(group_name)
                    if success:
                        chat.SendMsg(remove_brackets_content(f"已向群聊 {group_name} 发送签到提醒"))
                    else:
                        chat.SendMsg(remove_brackets_content(f"发送签到提醒失败"))
                else:
                    chat.SendMsg(remove_brackets_content("签到定时任务未启动"))
                return True

            elif command == "kick_absent" and len(parts) >= 2:
                # 手动踢出缺勤用户 /kick_absent 群名
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                group_name = parts[1]
                config_info = self.checkin_manager.get_group_checkin_config(group_name)
                if not config_info:
                    chat.SendMsg(remove_brackets_content(f"群聊 {group_name} 未启用签到功能"))
                    return True

                max_absent_days = config_info.get('max_absent_days', 3)
                kicked_users = self.checkin_manager.kick_absent_users(group_name, max_absent_days)

                if kicked_users:
                    user_list = "、".join(kicked_users)
                    chat.SendMsg(remove_brackets_content(f"已踢出 {len(kicked_users)} 名缺勤用户：{user_list}"))
                else:
                    chat.SendMsg(remove_brackets_content("没有需要踢出的缺勤用户"))
                return True

            elif command == "checkin_status":
                # 查看签到功能状态 /checkin_status
                try:
                    enabled_groups = self.checkin_manager.get_enabled_groups()

                    if not enabled_groups:
                        chat.SendMsg(remove_brackets_content("当前没有启用签到功能的群聊"))
                        return True

                    status_message = f"📋 签到功能状态报告\n\n"
                    status_message += f"✅ 已启用签到的群聊数量：{len(enabled_groups)}\n\n"

                    for i, group_name in enumerate(enabled_groups, 1):
                        config_info = self.checkin_manager.get_group_checkin_config(group_name)
                        if config_info:
                            status_message += f"{i}. {group_name}\n"
                            status_message += f"   提醒时间：{config_info.get('reminder_time', '未设置')}\n"
                            status_message += f"   统计时间：{config_info.get('stats_time', '未设置')}\n"
                            status_message += f"   踢人时间：{config_info.get('kick_time', '未设置')}\n"
                            status_message += f"   最大缺勤：{config_info.get('max_absent_days', '未设置')}天\n\n"

                    # 检查配置文件中的自动启用列表
                    try:
                        import config
                        auto_groups = getattr(config, 'AUTO_ENABLE_CHECKIN_GROUPS', [])
                        if auto_groups:
                            status_message += f"🔧 配置文件中的自动启用群聊：\n"
                            for group_config in auto_groups:
                                if isinstance(group_config, str):
                                    status_message += f"   • {group_config}（使用默认配置）\n"
                                elif isinstance(group_config, dict):
                                    group_name = group_config.get('group_name', '未知')
                                    status_message += f"   • {group_name}（自定义配置）\n"
                    except Exception as e:
                        status_message += f"⚠️ 读取配置文件失败：{str(e)}\n"

                    chat.SendMsg(remove_brackets_content(status_message))

                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"获取签到状态失败：{str(e)}"))
                return True

            elif command == "reload_checkin_config":
                # 重新加载签到配置 /reload_checkin_config
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                try:
                    # 重新执行自动启用配置
                    self.checkin_manager.auto_enable_groups_from_config()
                    chat.SendMsg(remove_brackets_content("签到配置已重新加载，请查看日志了解详情"))
                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"重新加载签到配置失败：{str(e)}"))
                return True

            elif command == "cleanup_checkin_records":
                # 手动清理无效签到记录 /cleanup_checkin_records
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                try:
                    # 执行清理
                    cleanup_stats = self.checkin_manager.cleanup_invalid_checkin_records()

                    if not cleanup_stats:
                        chat.SendMsg(remove_brackets_content("清理完成，但无数据需要处理"))
                        return True
                    # 生成清理报告
                    cleanup_message = self.checkin_manager.get_cleanup_stats_message(cleanup_stats)
                    chat.SendMsg(remove_brackets_content(cleanup_message))

                    # 记录操作日志
                    total_cleaned = sum(count for count in cleanup_stats.values() if count > 0)
                    log(f"管理员 {sender} 手动清理了 {total_cleaned} 条无效签到记录")

                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"清理签到记录失败：{str(e)}"))
                return True

            elif command == "scheduler_status":
                # 查看定时任务状态 /scheduler_status
                try:
                    import schedule

                    status_message = "🕐 定时任务状态报告\n\n"

                    if self.checkin_scheduler:
                        status_message += f"调度器运行状态: {'✅ 运行中' if self.checkin_scheduler.is_running else '❌ 已停止'}\n"

                        if self.checkin_scheduler.scheduler_thread:
                            thread_alive = self.checkin_scheduler.scheduler_thread.is_alive()
                            status_message += f"调度器线程状态: {'✅ 活跃' if thread_alive else '❌ 停止'}\n"
                        else:
                            status_message += "调度器线程状态: ❌ 未创建\n"
                    else:
                        status_message += "调度器状态: ❌ 未初始化\n"

                    total_jobs = len(schedule.jobs)
                    status_message += f"定时任务数量: {total_jobs}\n\n"

                    if total_jobs > 0:
                        status_message += "定时任务列表:\n"
                        for i, job in enumerate(schedule.jobs):
                            task_name = job.job_func.__name__
                            next_run = job.next_run.strftime('%Y-%m-%d %H:%M:%S')
                            status_message += f"{i+1}. {task_name}\n"
                            status_message += f"   下次执行: {next_run}\n"
                    else:
                        status_message += "⚠️ 没有设置任何定时任务\n"

                    # 检查启用签到的群聊
                    enabled_groups = self.checkin_manager.get_enabled_groups()
                    status_message += f"\n启用签到的群聊数量: {len(enabled_groups)}\n"

                    if enabled_groups:
                        status_message += "启用签到的群聊:\n"
                        for group in enabled_groups:
                            status_message += f"• {group}\n"

                    chat.SendMsg(remove_brackets_content(status_message))

                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"获取定时任务状态失败：{str(e)}"))



                return True

            elif command == "reload_scheduler":
                # 重新加载定时任务配置 /reload_scheduler
                if not self.group_manager.check_group_management_permission(sender):
                    chat.SendMsg(remove_brackets_content("您没有群聊管理权限"))
                    return True

                try:
                    if self.checkin_scheduler:
                        success = self.checkin_scheduler.reload_schedule_config()

                        if success:
                            # 显示新的配置
                            import schedule

                            message = "✅ 定时任务配置重新加载成功\n\n"
                            message += f"当前定时任务数量: {len(schedule.jobs)}\n\n"

                            if schedule.jobs:
                                message += "更新后的任务列表:\n"
                                for i, job in enumerate(schedule.jobs):
                                    task_name = job.job_func.__name__
                                    next_run = job.next_run.strftime('%H:%M')
                                    message += f"{i+1}. {task_name} - {next_run}\n"

                            chat.SendMsg(remove_brackets_content(message))
                        else:
                            chat.SendMsg(remove_brackets_content("❌ 定时任务配置重新加载失败，请查看日志"))
                    else:
                        chat.SendMsg(remove_brackets_content("❌ 定时任务管理器未初始化"))

                except Exception as e:
                    chat.SendMsg(remove_brackets_content(f"重新加载定时任务配置失败：{str(e)}"))
                return True

            elif command.startswith("test_text"):
                # 测试文本处理 /test_text 测试文本(包含括号)
                if len(parts) >= 2:
                    test_text = " ".join(parts[1:])

                    # 演示各种文本处理功能
                    original = test_text
                    cleaned = remove_brackets_content(test_text)
                    formatted = format_response(cleaned)
                    sentences = split_sentences(formatted)

                    result = f"""文本处理测试：
原文: {original}
移除括号: {cleaned}
格式化: {formatted}
分割句子: {sentences}"""
                    chat.SendMsg(remove_brackets_content(result))
                else:
                    chat.SendMsg(remove_brackets_content("用法: /test_text 测试文本(包含括号)"))
                return True

            elif command.startswith("weather"):
                # 天气查询 /weather 城市名
                if len(parts) >= 2:
                    city = parts[1]
                    weather_info = self.weather_manager.get_weather(city)
                    chat.SendMsg(remove_brackets_content(weather_info))
                else:
                    chat.SendMsg(remove_brackets_content("用法: /weather 城市名"))
                return True

            elif command == "send_weather":
                # 手动发送天气播报给所有监听用户、管理员和群聊
                self.send_weather_broadcast()
                chat.SendMsg(remove_brackets_content("天气播报已发送给所有监听用户、管理员和群聊"))
                return True

            elif command == "update_weather":
                # 更新天气缓存
                self.weather_manager.update_weather_cache(config.WEATHER_CITIES)
                chat.SendMsg(remove_brackets_content("天气缓存已更新"))
                return True

            elif command == "update_xuzhou_weather":
                # 更新徐州天气缓存
                if self.fetch_and_cache_xuzhou_weather():
                    weather_prompt = self.get_cached_weather_prompt()
                    chat.SendMsg(remove_brackets_content(f"徐州天气缓存已更新\n{weather_prompt}"))
                else:
                    chat.SendMsg(remove_brackets_content("徐州天气缓存更新失败"))
                return True

            elif command.startswith("favor_info"):
                # 查看用户好感度 /favor_info 用户名
                if len(parts) >= 2:
                    user_id = parts[1]
                    info = FavorabilityManager.get_favorability_info(user_id)
                    reply = f"""用户 {user_id} 的好感度信息：
等级：{info['level']}
数值：{info['favorability']:.1f}/100
交互次数：{info['interaction_count']}次
最后交互：{info['last_interaction'] or '从未'}
创建时间：{info['created_at'] or '未知'}"""
                    chat.SendMsg(remove_brackets_content(reply))
                else:
                    chat.SendMsg(remove_brackets_content("用法: /favor_info 用户名"))
                return True

            elif command.startswith("favor_set"):
                # 设置用户好感度 /favor_set 用户名 数值
                if len(parts) >= 3:
                    user_id = parts[1]
                    try:
                        new_value = float(parts[2])
                        if FavorabilityManager.reset_favorability(user_id, new_value):
                            chat.SendMsg(remove_brackets_content(f"已设置用户 {user_id} 的好感度为 {new_value:.1f}"))
                        else:
                            chat.SendMsg(remove_brackets_content(f"设置用户 {user_id} 的好感度失败"))
                    except ValueError:
                        chat.SendMsg(remove_brackets_content("好感度数值必须是数字"))
                else:
                    chat.SendMsg(remove_brackets_content("用法: /favor_set 用户名 数值"))
                return True

            elif command == "favor_stats":
                # 显示好感度统计
                stats = FavorabilityManager.get_favorability_stats()
                stats_text = f"""好感度系统统计：
总用户数: {stats.get('total_users', 0)}
高好感度用户(≥80): {stats.get('high_favor_users', 0)}
中等好感度用户(40-79): {stats.get('medium_favor_users', 0)}
低好感度用户(<40): {stats.get('low_favor_users', 0)}
平均好感度: {stats.get('average_favorability', 0):.1f}"""
                chat.SendMsg(remove_brackets_content(stats_text))
                return True

            elif command == "favor_list":
                # 显示所有用户好感度列表
                users = FavorabilityManager.get_all_users_favorability()
                if users:
                    reply_lines = ["用户好感度排行榜："]
                    for i, user in enumerate(users[:10], 1):  # 只显示前10名
                        reply_lines.append(f"{i}. {user['user_id']}: {user['favorability']:.1f} ({user['level']})")
                    chat.SendMsg(remove_brackets_content("\n".join(reply_lines)))
                else:
                    chat.SendMsg(remove_brackets_content("暂无用户好感度数据"))
                return True

            elif command == "proactive_status":
                # 查看主动发消息状态
                if self.proactive_message_manager:
                    status = self.proactive_message_manager.get_status()
                    status_text = f"""主动发消息功能状态：
运行状态: {'运行中' if status['is_running'] else '已停止'}
发送目标数: {status['total_targets']}个
当前是否静默: {'是' if status['is_quiet_time'] else '否'}
静默时间: {status['quiet_hours']}
发送间隔: {status['interval_range']}

发送目标列表:"""
                    for target in status['targets']:
                        status_text += f"\n- {target['name']} ({target['type']})"

                    if status['last_send_times']:
                        status_text += "\n\n最近发送记录:"
                        for target_key, send_time in status['last_send_times'].items():
                            status_text += f"\n- {target_key}: {send_time.strftime('%Y-%m-%d %H:%M:%S')}"

                    chat.SendMsg(remove_brackets_content(status_text))
                else:
                    chat.SendMsg(remove_brackets_content("主动发消息功能未启用"))
                return True

            elif command == "proactive_start":
                # 启动主动发消息功能
                if not self.proactive_message_manager:
                    self.proactive_message_manager = ProactiveMessageManager(self)

                if not self.proactive_message_manager.is_running:
                    self.proactive_message_manager.start()
                    chat.SendMsg(remove_brackets_content("主动发消息功能已启动"))
                else:
                    chat.SendMsg(remove_brackets_content("主动发消息功能已在运行中"))
                return True

            elif command == "proactive_stop":
                # 停止主动发消息功能
                if self.proactive_message_manager and self.proactive_message_manager.is_running:
                    self.proactive_message_manager.stop()
                    chat.SendMsg(remove_brackets_content("主动发消息功能已停止"))
                else:
                    chat.SendMsg(remove_brackets_content("主动发消息功能未在运行"))
                return True

            elif command == "proactive_test":
                # 测试主动发消息功能
                if self.proactive_message_manager:
                    self.proactive_message_manager.run_proactive_message_cycle()
                    chat.SendMsg(remove_brackets_content("已执行一次主动发消息测试"))
                else:
                    chat.SendMsg(remove_brackets_content("主动发消息功能未启用"))
                return True

            elif command == "schedule_status":
                # 查看日程功能状态
                if getattr(config, 'ENABLE_DAILY_SCHEDULE', False):
                    schedule = self.schedule_manager.get_today_schedule()
                    if schedule:
                        schedule_text = "📅 今日日程：\n"
                        for item in schedule['schedule']:
                            schedule_text += f"{item['time']} - {item['activity']}\n"
                        chat.SendMsg(remove_brackets_content(schedule_text))
                    else:
                        chat.SendMsg(remove_brackets_content("今日日程尚未生成"))
                else:
                    chat.SendMsg(remove_brackets_content("每日日程功能未启用"))
                return True

            elif command == "schedule_generate":
                # 手动生成今日日程
                if getattr(config, 'ENABLE_DAILY_SCHEDULE', False):
                    success = self.schedule_manager.generate_daily_schedule(date_offset=0)
                    if success:
                        chat.SendMsg(remove_brackets_content("今日日程生成成功"))
                    else:
                        chat.SendMsg(remove_brackets_content("今日日程生成失败"))
                else:
                    chat.SendMsg(remove_brackets_content("每日日程功能未启用"))
                return True

            elif command == "schedule_tomorrow":
                # 手动生成明日日程
                if getattr(config, 'ENABLE_DAILY_SCHEDULE', False):
                    success = self.schedule_manager.generate_daily_schedule(date_offset=1)
                    if success:
                        chat.SendMsg(remove_brackets_content("明日日程生成成功"))
                    else:
                        chat.SendMsg(remove_brackets_content("明日日程生成失败"))
                else:
                    chat.SendMsg(remove_brackets_content("每日日程功能未启用"))
                return True

            elif command == "reminder_stats":
                # 显示定时提醒统计
                if getattr(config, 'ENABLE_REMINDER', False):
                    stats = self.reminder_manager.get_reminder_stats()

                    # 检查调度器中的提醒任务
                    scheduler_jobs = 0
                    if self.scheduler:
                        jobs = self.scheduler.get_jobs()
                        scheduler_jobs = len([job for job in jobs if job.id.startswith('reminder_')])

                    stats_text = f"""📊 定时提醒统计：
总提醒数: {stats.get('total_reminders', 0)}
活跃提醒: {stats.get('active_reminders', 0)}
已触发提醒: {stats.get('triggered_reminders', 0)}
调度器任务: {scheduler_jobs}
恢复状态: {'正常' if scheduler_jobs == stats.get('active_reminders', 0) else '异常'}"""
                    chat.SendMsg(remove_brackets_content(stats_text))
                else:
                    chat.SendMsg(remove_brackets_content("定时提醒功能未启用"))
                return True

            elif command.startswith("reminder_list"):
                # 查看用户的提醒列表 /reminder_list 用户名
                if getattr(config, 'ENABLE_REMINDER', False):
                    if len(parts) >= 2:
                        user_id = parts[1]
                        reminders = self.reminder_manager.get_user_reminders(user_id)
                        if reminders:
                            reply_lines = [f"用户 {user_id} 的提醒列表："]
                            for reminder in reminders:
                                time_str = reminder.reminder_time.strftime('%m-%d %H:%M')
                                reply_lines.append(f"ID:{reminder.id} {time_str} - {reminder.original_message[:30]}...")
                            chat.SendMsg(remove_brackets_content("\n".join(reply_lines)))
                        else:
                            chat.SendMsg(remove_brackets_content(f"用户 {user_id} 暂无活跃提醒"))
                    else:
                        chat.SendMsg(remove_brackets_content("用法: /reminder_list 用户名"))
                else:
                    chat.SendMsg(remove_brackets_content("定时提醒功能未启用"))
                return True

            elif command.startswith("reminder_cancel"):
                # 取消提醒 /reminder_cancel 提醒ID
                if getattr(config, 'ENABLE_REMINDER', False):
                    if len(parts) >= 2:
                        try:
                            reminder_id = int(parts[1])
                            if self.reminder_manager.cancel_reminder(reminder_id):
                                chat.SendMsg(remove_brackets_content(f"提醒 {reminder_id} 已取消"))
                            else:
                                chat.SendMsg(remove_brackets_content(f"取消提醒 {reminder_id} 失败"))
                        except ValueError:
                            chat.SendMsg(remove_brackets_content("提醒ID必须是数字"))
                    else:
                        chat.SendMsg(remove_brackets_content("用法: /reminder_cancel 提醒ID"))
                else:
                    chat.SendMsg(remove_brackets_content("定时提醒功能未启用"))
                return True

            elif command == "reminder_cleanup":
                # 清理旧提醒
                if getattr(config, 'ENABLE_REMINDER', False):
                    self.reminder_manager.cleanup_old_reminders(config.REMINDER_CLEANUP_DAYS)
                    chat.SendMsg(remove_brackets_content("旧提醒记录清理完成"))
                else:
                    chat.SendMsg(remove_brackets_content("定时提醒功能未启用"))
                return True

            elif command == "reminder_recover":
                # 手动恢复提醒任务
                if getattr(config, 'ENABLE_REMINDER', False):
                    recovered_count = self.reminder_manager.recover_reminders_on_startup()
                    chat.SendMsg(remove_brackets_content(f"已恢复 {recovered_count} 个提醒任务"))
                else:
                    chat.SendMsg(remove_brackets_content("定时提醒功能未启用"))
                return True

            elif command.startswith("group_cache_status"):
                # 查看群聊缓存状态 /group_cache_status 群名
                if getattr(config, 'ENABLE_GROUP_CONTENT_CACHE', True):
                    if len(parts) >= 2:
                        group_name = parts[1]
                        cache_data = self.memory_manager.get_group_content_cache(group_name, limit=5)
                        if cache_data:
                            reply_lines = [f"群聊 {group_name} 的缓存状态："]
                            reply_lines.append(f"缓存条数: {len(cache_data)}")
                            reply_lines.append("最近5条消息:")
                            for entry in cache_data[-5:]:
                                reply_lines.append(f"{entry['message_time']} {entry['sender_name']}: {entry['message_content'][:30]}...")
                            chat.SendMsg(remove_brackets_content("\n".join(reply_lines)))
                        else:
                            chat.SendMsg(remove_brackets_content(f"群聊 {group_name} 暂无缓存内容"))
                    else:
                        chat.SendMsg(remove_brackets_content("用法: /group_cache_status 群名"))
                else:
                    chat.SendMsg(remove_brackets_content("群聊内容缓存功能未启用"))
                return True

            elif command.startswith("clear_group_cache"):
                # 清除群聊缓存 /clear_group_cache 群名
                if getattr(config, 'ENABLE_GROUP_CONTENT_CACHE', True):
                    if len(parts) >= 2:
                        group_name = parts[1]
                        if self.memory_manager.clear_group_content_cache(group_name):
                            chat.SendMsg(remove_brackets_content(f"已清除群聊 {group_name} 的内容缓存"))
                        else:
                            chat.SendMsg(remove_brackets_content(f"清除群聊 {group_name} 的内容缓存失败"))
                    else:
                        chat.SendMsg(remove_brackets_content("用法: /clear_group_cache 群名"))
                else:
                    chat.SendMsg(remove_brackets_content("群聊内容缓存功能未启用"))
                return True

            elif command == "sentiment_test":
                # 测试情感分析功能 /sentiment_test 测试文本
                if len(parts) >= 2:
                    test_text = " ".join(parts[1:])
                    result = sentiment_analyzer.analyze_sentiment_intensity(test_text)
                    emotion_prompt = sentiment_analyzer.get_emotion_prompt(result)

                    test_result = f"""情感分析测试结果：
测试文本: {test_text}
情感类型: {result['label']}
情感强度: {result['intensity']}
置信度: {result['score']:.2f}
分析详情: {result['details']}
回复指导: {emotion_prompt}"""
                    chat.SendMsg(remove_brackets_content(test_result))
                else:
                    chat.SendMsg(remove_brackets_content("用法: /sentiment_test 要分析的文本"))
                return True

            elif command == "image_status":
                # 查看图片生成功能状态
                if getattr(config, 'ENABLE_IMAGE_GENERATION', False):
                    if self.image_generator:
                        auto_delete_status = "开启" if getattr(config, 'AUTO_DELETE_SENT_IMAGES', True) else "关闭"
                        watermark_status = "开启" if getattr(config, 'WATERMARK', False) else "关闭"
                        status_text = f"""图片生成功能状态：
功能状态: 已启用
生成服务: {getattr(config, 'IMAGE_GENERATION_SERVICE', 'auto')}
输出目录: {getattr(config, 'IMAGE_OUTPUT_DIR', 'generated_images')}
自动删除: {auto_delete_status}
水印设置: {watermark_status}
清理天数: {getattr(config, 'IMAGE_CLEANUP_DAYS', 7)}天"""
                    else:
                        status_text = "图片生成功能已启用，但初始化失败"
                else:
                    status_text = "图片生成功能未启用"
                chat.SendMsg(remove_brackets_content(status_text))
                return True

            elif command == "queue_status":
                # 查看消息队列状态
                if self.message_queue_manager:
                    status = self.message_queue_manager.get_status()
                    current_msg_info = ""
                    if status['current_message']:
                        current_msg_info = f"""
当前处理消息:
  发送者: {status['current_message']['sender']}
  内容: {status['current_message']['content']}
  时间: {time.strftime('%H:%M:%S', time.localtime(status['current_message']['timestamp']))}"""

                    status_text = f"""消息队列状态：
运行状态: {'运行中' if status['is_running'] else '已停止'}
处理状态: {'处理中' if status['is_processing'] else '空闲'}
队列大小: {status['queue_size']}
已处理: {status['total_processed']}
已入队: {status['total_queued']}
队列满次数: {status['queue_full_count']}{current_msg_info}"""
                else:
                    status_text = "消息队列功能未启用"
                chat.SendMsg(remove_brackets_content(status_text))
                return True

            elif command == "queue_clear":
                # 清空消息队列
                if self.message_queue_manager:
                    self.message_queue_manager.clear_queue()
                    chat.SendMsg(remove_brackets_content("消息队列已清空"))
                else:
                    chat.SendMsg(remove_brackets_content("消息队列功能未启用"))
                return True

            elif command == "queue_start":
                # 启动消息队列
                if self.message_queue_manager:
                    if not self.message_queue_manager.is_running:
                        self.message_queue_manager.start()
                        chat.SendMsg(remove_brackets_content("消息队列已启动"))
                    else:
                        chat.SendMsg(remove_brackets_content("消息队列已在运行中"))
                else:
                    chat.SendMsg(remove_brackets_content("消息队列功能未启用"))
                return True

            elif command == "queue_stop":
                # 停止消息队列
                if self.message_queue_manager:
                    if self.message_queue_manager.is_running:
                        self.message_queue_manager.stop()
                        chat.SendMsg(remove_brackets_content("消息队列已停止"))
                    else:
                        chat.SendMsg(remove_brackets_content("消息队列未在运行"))
                else:
                    chat.SendMsg(remove_brackets_content("消息队列功能未启用"))
                return True

            elif command.startswith("test_image"):
                # 测试图片生成 /test_image 提示词
                if getattr(config, 'ENABLE_IMAGE_GENERATION', False) and self.image_generator:
                    if len(parts) >= 2:
                        prompt = " ".join(parts[1:])
                        chat.SendMsg(remove_brackets_content("开始测试图片生成..."))

                        service = getattr(config, 'IMAGE_GENERATION_SERVICE', 'auto')
                        image_path = self.image_generator.generate_image(prompt, service)

                        if image_path and os.path.exists(image_path):
                            try:
                                chat.SendFiles(filepath=image_path)

                                # 测试发送成功后根据配置决定是否删除本地文件
                                if getattr(config, 'AUTO_DELETE_SENT_IMAGES', True):
                                    try:
                                        os.remove(image_path)
                                        log(f"测试图片文件已删除: {image_path}")
                                    except Exception as delete_e:
                                        log(f"删除测试图片文件失败: {str(delete_e)}", "WARNING")

                                chat.SendMsg(remove_brackets_content("图片生成测试成功"))
                            except Exception as e:
                                # 发送失败时根据配置决定是否删除本地文件
                                if getattr(config, 'AUTO_DELETE_SENT_IMAGES', True):
                                    try:
                                        os.remove(image_path)
                                        log(f"测试发送失败，图片文件已删除: {image_path}")
                                    except Exception as delete_e:
                                        log(f"删除测试图片文件失败: {str(delete_e)}", "WARNING")

                                chat.SendMsg(remove_brackets_content(f"图片发送失败: {str(e)}"))
                        else:
                            chat.SendMsg(remove_brackets_content("图片生成测试失败"))
                    else:
                        chat.SendMsg(remove_brackets_content("用法: /test_image 提示词"))
                else:
                    chat.SendMsg(remove_brackets_content("图片生成功能未启用或未初始化"))
                return True

            elif command == "cleanup_images":
                # 清理旧图片
                if getattr(config, 'ENABLE_IMAGE_GENERATION', False) and self.image_generator:
                    cleanup_days = getattr(config, 'IMAGE_CLEANUP_DAYS', 7)
                    self.image_generator.cleanup_old_images(cleanup_days)
                    chat.SendMsg(remove_brackets_content(f"已清理 {cleanup_days} 天前的旧图片"))
                else:
                    chat.SendMsg(remove_brackets_content("图片生成功能未启用"))
                return True

            elif command == "toggle_watermark":
                # 切换水印设置
                if getattr(config, 'ENABLE_IMAGE_GENERATION', False):
                    current_watermark = getattr(config, 'WATERMARK', False)
                    new_watermark = not current_watermark
                    config.WATERMARK = new_watermark
                    status = "开启" if new_watermark else "关闭"
                    chat.SendMsg(remove_brackets_content(f"图片水印已{status}"))
                else:
                    chat.SendMsg(remove_brackets_content("图片生成功能未启用"))
                return True

            else:
                chat.SendMsg(remove_brackets_content(f"未知命令: {command}\n发送 /help 查看可用命令"))
                return True

        except Exception as e:
            log(f"处理管理员命令异常: {str(e)}", "ERROR")
            chat.SendMsg(remove_brackets_content(f"命令执行失败: {str(e)}"))
            return True

        return False

    def generate_and_send_reply(self, content, chat_id, sender, chat, is_group=False, is_smart_participation=False):
        """生成并发送AI回复"""
        try:
            # 清理群聊消息格式
            if is_group:
                content = clean_group_message(content)

            # 生成记忆键
            memory_key = self.memory_manager.get_memory_key(chat_id, is_group)

            # 构建格式化消息用于重复检查
            if is_smart_participation:
                # 智能插话使用简洁格式进行重复检查
                formatted_message_for_duplicate_check = f"[群聊中{sender}说]: {content}"
            else:
                # 正常对话使用完整格式进行重复检查
                from datetime import datetime
                now = datetime.now().strftime("%H:%M")

                # 获取天气提示信息
                weather_prompt = self.get_cached_weather_prompt()
                if weather_prompt:
                    weather_prompt = f"\n{weather_prompt}\n"
                else:
                    weather_prompt = "\n"

                # 构建完整的格式化用户消息（用于重复检查）
                formatted_message_for_duplicate_check = f"[发送消息的用户: {sender}]\n[当前系统时间: {now}]{weather_prompt}[用户消息: {content}]"

            # 检查重复消息
            if self.memory_manager.is_duplicate_message(formatted_message_for_duplicate_check, memory_key):
                log(f"拦截重复消息: {content[:20]}...")
                return

            # 进行情感强度分析
            sentiment_result = sentiment_analyzer.analyze_sentiment_intensity(content)
            log(f"情感分析结果: {sentiment_result['label']}-{sentiment_result['intensity']} (分数:{sentiment_result['score']:.2f}) - {sentiment_result['details']}")

            # 更新AI情感状态
            if self.emotion_manager and getattr(config, 'ENABLE_EMOTION_STATE', True):
                try:
                    # 根据用户消息的情感分析结果更新AI的情感状态
                    threshold = getattr(config, 'EMOTION_TRIGGER_CONFIG', {}).get('sentiment_threshold', 0.3)
                    if sentiment_result['score'] >= threshold:
                        self.emotion_manager.process_sentiment_analysis(sentiment_result, content)

                        # 记录情感状态变化
                        if getattr(config, 'EMOTION_RESPONSE_CONFIG', {}).get('show_emotion_in_debug', True):
                            dominant_emotion, intensity = self.emotion_manager.get_dominant_emotion()
                            log(f"AI情感状态更新: {dominant_emotion.value} (强度: {intensity:.2f})")
                except Exception as e:
                    log(f"更新AI情感状态失败: {str(e)}", "ERROR")

            # 生成AI回复
            reply = self.generate_ai_reply(content, memory_key, sender, chat_id if is_group else None, is_group, chat_id, sentiment_result, is_smart_participation)

            if not reply:
                log("AI回复为空", "WARNING")
                return

            # 处理思考标签
            if has_think_tags(reply):
                reply = remove_think_tags(reply)

            # 保存聊天记录到数据库
            self.memory_manager.save_message(
                sender_id=sender,
                sender_name=sender,
                message=content,
                reply=reply,
                is_group=is_group,
                group_id=chat_id if is_group else None
            )

            # 处理回复内容
            self.process_and_send_reply(reply, chat, sender, is_group)

        except Exception as e:
            log(f"生成并发送回复异常: {str(e)}", "ERROR")

    def process_and_send_reply(self, reply, chat, sender, is_group=False):
        """处理并发送回复"""
        try:
            # 检查是否包含图片标签
            clean_text, images = extract_image_tags(reply)

            # 如果有图片，先发送图片
            if images:
                for image_path in images:
                    if os.path.exists(image_path):
                        try:
                            chat.SendFiles(filepath=image_path)
                            log(f"图片发送成功: {image_path}")

                            # 根据配置决定是否删除本地文件
                            if getattr(config, 'AUTO_DELETE_SENT_IMAGES', True):
                                try:
                                    os.remove(image_path)
                                    log(f"本地图片文件已删除: {image_path}")
                                except Exception as delete_e:
                                    log(f"删除本地图片文件失败: {str(delete_e)}", "WARNING")

                        except Exception as e:
                            log(f"图片发送失败: {str(e)}", "ERROR")

                            # 发送失败时根据配置决定是否删除本地文件（避免文件积累）
                            if getattr(config, 'AUTO_DELETE_SENT_IMAGES', True):
                                try:
                                    os.remove(image_path)
                                    log(f"发送失败，本地图片文件已删除: {image_path}")
                                except Exception as delete_e:
                                    log(f"删除本地图片文件失败: {str(delete_e)}", "WARNING")
                    else:
                        log(f"图片文件不存在: {image_path}", "WARNING")

            # 处理文本回复
            if clean_text:
                # 移除括号内容
                cleaned_reply = remove_brackets_content(clean_text)

                # 格式化回复（添加分隔符）
                formatted_reply = format_response(cleaned_reply)

                # 分割句子
                sentences = split_sentences(formatted_reply)

                # 发送每个句子
                for i, sentence in enumerate(sentences):
                    if sentence.strip():
                        # 使用统一的延迟函数
                        apply_message_delay(i, len(sentences))

                        # 在群聊中为第一条消息@发送者
                        if is_group and i == 0:
                            result = chat.SendMsg(msg=sentence, at=sender)
                        else:
                            result = chat.SendMsg(msg=sentence)

                        if result:
                            log(f"句子发送成功: {sentence[:30]}...")
                        else:
                            log(f"句子发送失败: {sentence[:30]}...", "ERROR")

            # 发送完所有消息后，随机发送自定义表情
            if getattr(config, 'ENABLE_CUSTOM_EMOTION', False):
                import random
                # 检查是否满足发送概率
                if random.random() <= getattr(config, 'EMOTION_SEND_PROBABILITY', 0.5):
                    # 获取自定义表情索引列表
                    emotion_indexes = getattr(config, 'CUSTOM_EMOTION_INDEXES', [0])
                    if emotion_indexes:
                        # 随机选择一个表情索引
                        selected_index = random.choice(emotion_indexes)
                        try:
                            # 发送自定义表情
                            chat.SendEmotion(emotion_index=selected_index)
                            log(f"自定义表情发送成功，索引: {selected_index}")
                        except Exception as emotion_e:
                            log(f"自定义表情发送失败: {str(emotion_e)}", "ERROR")

        except Exception as e:
            log(f"处理并发送回复异常: {str(e)}", "ERROR")
            # 发送原始回复作为备用
            try:
                # 移除括号内容
                cleaned_backup_reply = remove_brackets_content(reply)
                if is_group:
                    chat.SendMsg(msg=cleaned_backup_reply, at=sender)
                else:
                    chat.SendMsg(msg=cleaned_backup_reply)
            except Exception as backup_e:
                log(f"备用发送也失败: {str(backup_e)}", "ERROR")

    def send_weather_broadcast(self):
        """发送天气播报给所有监听用户、管理员和群聊"""
        try:
            # 获取多个城市的天气信息
            weather_reports = self.weather_manager.get_multiple_weather(config.WEATHER_CITIES)

            if not weather_reports:
                log("没有获取到天气信息", "WARNING")
                return

            # 构建播报消息
            broadcast_msg = "🌤️ 小葵天气播报 🌤️\n\n" + "\n\n".join(weather_reports)

            # 移除括号内容
            cleaned_broadcast_msg = remove_brackets_content(broadcast_msg)

            # 收集所有需要发送的目标（去重）
            all_targets = set()

            # 添加监听用户
            all_targets.update(config.LISTEN_LIST)

            # 添加管理员
            all_targets.update(config.ADMIN_LIST)

            # 添加群聊
            all_targets.update(config.GROUP_LIST)

            log(f"准备发送天气播报给 {len(all_targets)} 个目标")

            # 发送统计
            sent_count = 0
            failed_targets = []

            for target in all_targets:
                try:
                    # 先检查目标是否存在（通过尝试切换到聊天窗口）
                    try:
                        # 尝试切换到聊天窗口
                        chat_result = self.wx.ChatWith(target)
                        if not chat_result:
                            log(f"无法找到聊天窗口: {target}", "WARNING")
                            failed_targets.append(target)
                            continue
                    except Exception as switch_e:
                        log(f"切换到 {target} 聊天窗口失败: {str(switch_e)}", "WARNING")
                        failed_targets.append(target)
                        continue

                    # 使用wxautox的SendMsg方法发送消息
                    result = self.wx.SendMsg(msg=cleaned_broadcast_msg, who=target, clear=True)

                    # 检查发送结果
                    if result:
                        sent_count += 1
                        # 判断目标类型并记录
                        if target in config.GROUP_LIST:
                            log(f"天气播报已发送给群聊: {target}")
                        elif target in config.ADMIN_LIST:
                            log(f"天气播报已发送给管理员: {target}")
                        else:
                            log(f"天气播报已发送给用户: {target}")
                    else:
                        log(f"发送天气播报给 {target} 失败: SendMsg返回False", "WARNING")
                        failed_targets.append(target)

                    time.sleep(2)  # 增加延迟，防止发送过快

                except Exception as e:
                    log(f"发送天气播报给 {target} 失败: {str(e)}", "ERROR")
                    failed_targets.append(target)

            # 记录发送结果
            if sent_count > 0:
                log(f"天气播报完成，成功发送给 {sent_count} 个目标")
            if failed_targets:
                log(f"发送失败的目标: {', '.join(failed_targets)}", "WARNING")

        except Exception as e:
            log(f"天气播报异常: {str(e)}", "ERROR")

    def fetch_and_cache_xuzhou_weather(self):
        """获取并缓存徐州天气信息"""
        try:
            # 使用天气管理器获取徐州天气
            weather_data = self.weather_manager.get_weather("徐州")

            if weather_data and "徐州" in weather_data:
                # 解析天气信息
                weather_lines = weather_data.split('\n')
                weather_info = {}

                for line in weather_lines:
                    if '实时温度：' in line:
                        temp = line.split('实时温度：')[1].replace('℃', '').strip()
                        weather_info['temperature'] = temp
                    elif '天气状况：' in line:
                        weather_condition = line.split('天气状况：')[1].strip()
                        weather_info['weather'] = weather_condition

                # 缓存天气信息
                self.cached_weather['徐州'] = {
                    'data': weather_info,
                    'timestamp': datetime.now(),
                    'raw_data': weather_data
                }

                log(f"成功获取并缓存徐州天气信息: {weather_info.get('weather', '未知')} {weather_info.get('temperature', '未知')}°C")
                return True
            else:
                log("获取徐州天气信息失败", "WARNING")
                return False

        except Exception as e:
            log(f"获取徐州天气失败: {str(e)}", "ERROR")
            return False

    def get_cached_weather_prompt(self):
        """获取缓存的天气提示信息"""
        try:
            weather_info = self.cached_weather.get('徐州')
            if weather_info:
                time_diff = datetime.now() - weather_info['timestamp']
                # 3小时内的缓存有效
                if time_diff.total_seconds() < 3 * 3600:
                    weather_data = weather_info['data']
                    weather_prompt = f"[当前系统天气: {weather_data.get('weather', '未知')} {weather_data.get('temperature', '未知')}°C]"
                    return weather_prompt
                else:
                    log("天气缓存已过期，需要更新", "DEBUG")
            return ""
        except Exception as e:
            log(f"获取天气提示信息失败: {str(e)}", "ERROR")
            return ""

    def setup_weather_schedule(self):
        """设置天气播报定时任务"""
        try:
            # 每天早上8点发送天气播报
            self.scheduler.add_job(
                func=self.send_weather_broadcast,
                trigger=CronTrigger(hour=config.MORNING_SEND_HOUR, minute=0),
                id='daily_weather',
                name='每日天气播报',
                replace_existing=True
            )

            # 启动后5分钟发送一次天气播报
            self.scheduler.add_job(
                func=self.send_weather_broadcast,
                trigger='date',
                run_date=datetime.now() + timedelta(seconds=config.STARTUP_WEATHER_DELAY),
                id='startup_weather',
                name='启动天气播报'
            )

            log("天气播报定时任务已设置")

        except Exception as e:
            log(f"设置天气定时任务失败: {str(e)}", "ERROR")

    def setup_xuzhou_weather_cache(self):
        """设置徐州天气缓存更新定时任务"""
        try:
            # 每3小时更新一次徐州天气缓存
            self.scheduler.add_job(
                func=self.fetch_and_cache_xuzhou_weather,
                trigger='interval',
                hours=3,
                id='xuzhou_weather_cache',
                name='徐州天气缓存更新',
                replace_existing=True
            )

            log("徐州天气缓存定时任务已设置（每3小时更新）")

        except Exception as e:
            log(f"设置徐州天气缓存定时任务失败: {str(e)}", "ERROR")

    def generate_ai_reply(self, user_message, memory_key, sender_name=None, group_id=None, is_group=False, chat_id=None, sentiment_result=None, is_smart_participation=False):
        """生成AI回复"""
        if not self.api_client:
            log("API客户端未初始化", "ERROR")
            return "抱歉，AI服务暂时不可用"

        try:
            # 加载记忆
            memory = self.memory_manager.load_memory(memory_key)

            # 加载跨场景记忆
            cross_scene_memories = []
            if sender_name:
                if is_group:
                    # 如果是群聊，加载该用户的私聊记忆
                    cross_scene_memories = self.memory_manager.load_user_private_memories(sender_name)
                else:
                    # 如果是私聊，加载该用户的群聊记忆
                    cross_scene_memories = self.memory_manager.load_user_group_memories(sender_name)

            # 构建消息
            messages = self.build_messages(user_message, memory, sender_name, group_id, cross_scene_memories, is_group, sentiment_result, is_smart_participation)

            log(f"发送消息到AI: {len(messages)}条消息")

            # 调用API
            response = self.api_client.chat.completions.create(
                model=config.MODEL,
                messages=messages,
                temperature=config.TEMPERATURE,
                max_tokens=config.MAX_TOKEN,
                stream=False
            )

            if response.choices and len(response.choices) > 0:
                reply = response.choices[0].message.content

                # 解析并更新好感度
                if sender_name:
                    favor_change = FavorabilityManager.parse_favorability_change(reply)
                    if favor_change != 0:
                        new_favorability = FavorabilityManager.update_favorability(sender_name, favor_change)
                        log(f"用户 {sender_name} 好感度更新: 变化 {favor_change}, 当前 {new_favorability:.1f}")

                # 构建格式化的用户消息用于保存记忆
                if is_smart_participation:
                    # 智能插话使用简洁格式保存记忆
                    formatted_user_message_for_memory = f"[群聊中{sender_name}说]: {user_message}"
                else:
                    # 正常对话使用完整格式保存记忆
                    from datetime import datetime
                    now = datetime.now().strftime("%H:%M")

                    # 获取天气提示信息
                    weather_prompt = self.get_cached_weather_prompt()
                    if weather_prompt:
                        weather_prompt = f"\n{weather_prompt}\n"
                    else:
                        weather_prompt = "\n"

                    # 构建完整的格式化用户消息
                    formatted_user_message_for_memory = f"[历史对话的用户叫: {sender_name}]\n[当前系统时间: {now}]{weather_prompt}[他对你说: {user_message}]"

                # 构建对话记录用于保存记忆
                conversation = [
                    {"role": "user", "content": formatted_user_message_for_memory, "sender": sender_name},
                    {"role": "assistant", "content": reply}
                ]

                # 异步保存记忆
                threading.Thread(
                    target=self.memory_manager.save_memory,
                    args=(memory_key, conversation)
                ).start()

                log(f"AI回复生成成功: {reply}")
                return reply
            else:
                log("AI API返回空响应", "ERROR")
                return "抱歉，我现在有点累，请稍后再试"

        except Exception as e:
            log(f"生成AI回复失败: {str(e)}", "ERROR")
            return "抱歉，我遇到了一些问题，请稍后再试"

    def build_messages(self, user_message, memory, sender_name=None, group_id=None, cross_scene_memories=None, is_group=False, sentiment_result=None, is_smart_participation=False):
        """构建消息列表，包含记忆信息和好感度信息"""
        messages = []

        # 构建系统提示词 - 使用更结构化的方式
        system_prompt_parts = [
            # 基础人设提示
            f"{self.prompt_content}",
        ]

        # 添加用户信息
        if sender_name:
            system_prompt_parts.append(f"\n[对话用户信息]\n姓名: {sender_name}")

        # 添加时间和天气信息使用说明
        system_prompt_parts.append(
            "\n[环境信息使用规则]\n"\
            "用户消息中包含的时间和天气信息是真实准确的，你可以直接使用这些信息回答相关问题。\n"\
            "- [当前系统时间: XX:XX] 表示现在的准确时间\n"\
            "- [当前系统天气: 天气状况 温度°C] 表示当前的真实天气情况\n"\
            "- [今日安排: [[\"时间\",\"内容\"],...]] 包含今日的时间安排，格式为时间和内容的数组，你可以根据当前时间和用户询问灵活地介绍、提醒或讨论相关安排"
        )

        # 群聊上下文说明
        if group_id and getattr(config, 'INCLUDE_GROUP_CACHE_ON_AT', True):
            system_prompt_parts.append(
                "\n[群聊环境说明]\n"\
                "当前是群聊环境，用户消息前会包含最近的群聊对话内容供你参考。你可以根据这些上下文信息更好地理解当前对话的背景和语境。"
            )

        # 用户好感度状态信息
        if sender_name:
            favorability = FavorabilityManager.get_favorability(sender_name)
            # 创建临时对象来获取等级描述
            from database import User
            temp_user = type('TempUser', (), {'favorability': favorability})()
            level = User.get_favorability_level(temp_user)

            # 检查是否达到最高好感度，并加载特殊提示词
            special_prompt = ""
            if favorability >= 100:
                special_prompt_path = os.path.join("prompts", config.SPECIAL_PROMPT_NAME)
                if os.path.exists(special_prompt_path):
                    try:
                        with open(special_prompt_path, "r", encoding="utf-8") as file:
                            special_prompt = file.read().strip()
                    except Exception as e:
                        log(f"加载特殊提示词失败: {str(e)}", "ERROR")
                else:
                    log(f"特殊提示词文件不存在: {config.SPECIAL_PROMPT_NAME}", "WARNING")

            system_prompt_parts.append(
                f"\n[用户好感度状态]\n"\
                f"用户: {sender_name}\n"\
                f"好感度: {favorability:.1f}/100\n"\
                f"关系等级: {level}\n"\
                f"回复指导: 根据好感度等级调整语气和内容，保持一致性"
            )

            # 添加特殊提示词（如果有）
            if special_prompt:
                system_prompt_parts.append(f"\n[特殊状态提示]\n{special_prompt}")

        # 情感分析结果
        if sentiment_result:
            emotion_prompt = sentiment_analyzer.get_emotion_prompt(sentiment_result)
            system_prompt_parts.append(
                f"\n[用户情感状态分析]\n"\
                f"情感类型: {sentiment_result['label']}\n"\
                f"情感强度: {sentiment_result['intensity']}\n"\
                f"置信度: {sentiment_result['score']:.2f}\n"\
                f"回复指导: {emotion_prompt}"
            )

        # AI情感状态信息
        if self.emotion_manager and getattr(config, 'EMOTION_RESPONSE_CONFIG', {}).get('include_emotion_prompt', True):
            emotion_state_prompt = self.emotion_manager.get_emotion_prompt()
            system_prompt_parts.append(f"\n[AI情感状态]\n{emotion_state_prompt}")

        # 记忆信息 - 结构化呈现
        memory_parts = []
        if memory:
            if memory.get('last_summary'):
                memory_parts.append(f"[历史对话摘要]\n{memory['last_summary']}")

            if memory.get('keywords'):
                keywords_str = ', '.join(memory['keywords'][:5])  # 只显示前5个关键词
                memory_parts.append(f"[核心关键词]\n{keywords_str}")

            # 记忆说明
            history_count = len(memory.get('history', []))
            if history_count > 0:
                memory_parts.append(
                    f"[记忆使用指导]\n"\
                    f"以下对话历史包含了与该用户的所有{history_count}条历史记录，\n"\
                    f"请充分利用这些记忆信息来提供更个性化和连贯的回复，保持对话一致性。"
                )

        # 跨场景记忆说明
        if cross_scene_memories and len(cross_scene_memories) > 0:
            total_cross_messages = sum(len(cm.get('history', [])) for cm in cross_scene_memories)
            if is_group:
                # 群聊中显示私聊记忆
                memory_parts.append(
                    f"[私聊记忆补充]\n"\
                    f"以下还包含了该用户的私聊记忆，共{total_cross_messages}条记录。\n"\
                    f"这些私聊记忆可以帮助你了解用户的个人情况，在群聊中可以适当引用这些私人对话。"
                )
            else:
                # 私聊中显示群聊记忆
                memory_parts.append(
                    f"[群聊记忆补充]\n"\
                    f"以下还包含了该用户在{len(cross_scene_memories)}个群聊中的相关记忆，共{total_cross_messages}条记录。\n"\
                    f"这些群聊记忆可以帮助你了解用户在群聊中的互动情况，在私聊中可以适当引用这些共同经历。"
                )

        # 合并记忆部分到系统提示词
        if memory_parts:
            system_prompt_parts.append("\n" + "\n".join(memory_parts))

        # 组合完整的系统提示词
        system_prompt = "".join(system_prompt_parts)
        messages.append({
            "role": "system",
            "content": system_prompt
        })

        # 添加历史对话记忆 - 优化呈现方式
        if memory and memory.get('history'):
            all_history = memory['history']  # 获取所有历史记录
            log(f"发送给AI的当前场景历史记忆数量: {len(all_history)}")

            # 添加历史对话时标明时间顺序
            for i, msg in enumerate(all_history):
                if msg["role"] in ["user", "assistant"]:
                    # 为历史消息添加序号，帮助AI理解时间顺序
                    content = f"{msg['content']}"
                    messages.append({
                        "role": msg["role"],
                        "content": content
                    })

        # 添加跨场景记忆 - 增强标识
        if cross_scene_memories:
            total_cross_messages = 0
            for cross_memory in cross_scene_memories:
                cross_history = cross_memory.get('history', [])
                for i, msg in enumerate(cross_history):
                    if msg.get("role") in ["user", "assistant"]:
                        # 增强跨场景消息标识，添加序号和更清晰的来源
                        content = msg["content"]
                        scene_identifier = "私聊" if is_group else "群聊"
                        source_name = sender_name if is_group else (msg.get("sender") or "未知用户")

                        if msg.get("role") == "user":
                            content = f"[{scene_identifier}记忆-{source_name}]: {content}"
                        elif msg.get("role") == "assistant":
                            content = f"{content}"

                        messages.append({
                            "role": msg["role"],
                            "content": content
                        })
                        total_cross_messages += 1

            if total_cross_messages > 0:
                scene_type = "私聊" if is_group else "群聊"
                log(f"发送给AI的{scene_type}记忆数量: {total_cross_messages}")

        # 记录总记忆数量
        total_history_count = len(memory.get('history', [])) if memory else 0
        total_cross_count = sum(len(cm.get('history', [])) for cm in cross_scene_memories) if cross_scene_memories else 0
        scene_type = "群聊" if is_group else "私聊"
        cross_scene_type = "私聊" if is_group else "群聊"
        log(f"发送给AI的总记忆数量: {scene_type}{total_history_count}条 + {cross_scene_type}{total_cross_count}条")

        # 构建格式化的用户消息 - 更清晰的结构
        from datetime import datetime
        now = datetime.now().strftime("%H:%M")

        # 环境信息部分
        env_info = []
        env_info.append(f"[当前系统时间: {now}]")

        # 天气提示信息
        weather_prompt = self.get_cached_weather_prompt()
        if weather_prompt:
            env_info.append(f"[当前系统天气: {weather_prompt}]")

        # 日程信息
        if getattr(config, 'ENABLE_SCHEDULE_IN_MESSAGE', False) and hasattr(self, 'schedule_manager'):
            schedule_summary = self.schedule_manager.get_schedule_summary()
            if schedule_summary:
                env_info.append(f"[今日安排: {schedule_summary}]")

        # 群聊缓存内容
        group_cache_content = ""
        if group_id and getattr(config, 'INCLUDE_GROUP_CACHE_ON_AT', True):
            group_cache_content = self.memory_manager.format_group_cache_for_ai(group_id)
            if group_cache_content:
                env_info.append(f"[群聊上下文]: {group_cache_content}")

        # 根据是否为智能插话构建不同的用户消息格式
        if is_smart_participation:
            # 智能插话使用简洁格式
            formatted_user_message = f"[群聊用户{sender_name}在群聊中发言]: {user_message}"
            log(f"智能插话消息格式:\n{formatted_user_message}")
        else:
            # 构建更详细的用户身份信息
            user_identity_info = self._build_user_identity_info(sender_name, group_id)

            # 组合环境信息
            env_info_str = "\n".join(env_info)

            # 构建完整用户消息
            # 获取当前日期和星期
            import datetime
            now = datetime.datetime.now()
            date_str = now.strftime("%Y年%m月%d日 %A")
            
            formatted_user_message = (
                f"[发送消息的用户: {sender_name}]{user_identity_info}\n"\
                f"[当前系统日期: {date_str}]\n"\
                f"{env_info_str}\n"\
                f"[{sender_name}的消息]: {user_message}"\
            )
            log(f"发送给AI的用户消息格式:\n{formatted_user_message}")

        # 添加格式化的用户消息
        messages.append({
            "role": "user",
            "content": formatted_user_message
        })

        return messages



    def test_api_connection(self):
        """测试API连接"""
        if not self.api_client:
            return False

        try:
            response = self.api_client.chat.completions.create(
                model=config.MODEL,
                messages=[
                    {"role": "system", "content": "你是一个AI助手"},
                    {"role": "user", "content": "你好"}
                ],
                max_tokens=10,
                temperature=0.1
            )

            if response.choices and len(response.choices) > 0:
                log("API连接测试成功")
                return True
            else:
                log("API连接测试失败：无响应", "ERROR")
                return False

        except Exception as e:
            log(f"API连接测试失败: {str(e)}", "ERROR")
            return False

    def get_help_text(self):
        """获取帮助文本"""
        return """可用命令：
/help - 显示帮助信息
/status - 显示机器人状态
/add_user 用户名 - 添加用户监听
/remove_user 用户名 - 移除用户监听
/add_group 群名 - 添加群组监听
/remove_group 群名 - 移除群组监听
/test_ai - 测试AI连接
/memory_stats - 显示记忆统计信息
/clear_memory 用户名/群名 - 清除指定的记忆
/cleanup_memory - 清理旧记忆
/test_text 文本内容 - 测试文本处理功能
/weather 城市名 - 查询指定城市天气
/send_weather - 手动发送天气播报给所有用户和群聊
/update_weather - 更新天气缓存
/update_xuzhou_weather - 更新徐州天气缓存
/favor_info 用户名 - 查看用户好感度信息
/favor_set 用户名 数值 - 设置用户好感度
/favor_stats - 显示好感度统计信息
/favor_list - 显示用户好感度排行榜
/proactive_status - 查看主动发消息功能状态
/proactive_start - 启动主动发消息功能
/proactive_stop - 停止主动发消息功能
/proactive_test - 测试执行一次主动发消息
/schedule_status - 查看今日日程
/schedule_generate - 手动生成今日日程
/schedule_tomorrow - 手动生成明日日程
/reminder_stats - 显示定时提醒统计信息
/reminder_list 用户名 - 查看用户的提醒列表
/reminder_cancel 提醒ID - 取消指定的提醒
/reminder_cleanup - 清理旧提醒记录
/reminder_recover - 手动恢复提醒任务
/prompt_status - 查看提示词加载状态
/reload_prompts - 重新加载提示词文件
/group_cache_status 群名 - 查看群聊内容缓存状态
/clear_group_cache 群名 - 清除群聊内容缓存
/group_stats 群名 - 查看群聊参与统计信息
/participation_mode 模式 - 切换群聊参与模式(smart/at_only/all)

情感状态命令：
/emotion_status - 查看AI当前情感状态
/reset_emotion - 重置AI情感状态
/set_emotion 类型 强度 [时间] [原因] - 手动设置AI情感
/emotion_help - 查看情感状态系统详细帮助

群聊管理命令：
/group_members 群名 - 获取群聊成员列表
/add_group_member 群名 成员名 [申请理由] - 添加群聊成员
/remove_group_member 群名 成员名 - 移除群聊成员
/group_info 群名 - 获取群聊信息
/at_all 群名 消息内容 - @所有人发送消息
/recent_groups - 获取最近群聊列表
/contact_groups - 获取通讯录群聊列表
/group_settings 群名 key=value - 修改群聊设置

签到管理命令：
/enable_checkin 群名 [配置参数] - 启用群聊签到功能
/disable_checkin 群名 - 禁用群聊签到功能
/checkin_stats 群名 [日期] - 查看签到统计
/checkin_reminder 群名 - 手动发送签到提醒
/kick_absent 群名 - 手动踢出缺勤用户
/checkin_status - 查看签到功能状态
/reload_checkin_config - 重新加载签到配置
/cleanup_checkin_records - 清理无效签到记录
/scheduler_status - 查看定时任务状态
/reload_scheduler - 重新加载定时任务配置

其他功能：
/image_status - 查看图片生成功能状态
/test_image 提示词 - 测试图片生成功能
/cleanup_images - 清理旧的生成图片
/toggle_watermark - 切换图片水印开关
/queue_status - 查看消息队列状态
/queue_clear - 清空消息队列
/queue_start - 启动消息队列
/queue_stop - 停止消息队列"""

    def get_status_text(self):
        """获取状态文本"""
        reminder_status = "开启" if getattr(config, 'ENABLE_REMINDER', False) else "关闭"
        group_cache_status = "开启" if getattr(config, 'ENABLE_GROUP_CONTENT_CACHE', True) else "关闭"
        image_generation_status = "开启" if getattr(config, 'ENABLE_IMAGE_GENERATION', False) else "关闭"
        queue_status = "关闭"
        if self.message_queue_manager:
            if self.message_queue_manager.is_running:
                queue_status = "运行中"
            else:
                queue_status = "已停止"
        participation_mode = getattr(config, 'GROUP_PARTICIPATION_MODE', 'smart')
        participation_mode_desc = {
            'smart': '智能模式',
            'at_only': '仅@回复',
            'all': '全部回复'
        }.get(participation_mode, participation_mode)

        return f"""机器人状态：
昵称: {config.ROBOT_WX_NAME}
监听用户: {len(config.LISTEN_LIST)}个
监听群组: {len(config.GROUP_LIST)}个
群聊功能: {'开启' if config.GROUP_SWITCH else '关闭'}
群聊参与模式: {participation_mode_desc}
群聊内容缓存: {group_cache_status}
定时提醒: {reminder_status}
图片生成: {image_generation_status}
消息队列: {queue_status}
AI模型: {config.MODEL}"""

    def run(self):
        """运行机器人"""
        log("=" * 50)
        log("微信角色扮演机器人启动中...")
        log("=" * 50)

        # 初始化微信
        if not self.init_wechat():
            log("微信初始化失败，程序退出", "ERROR")
            return

        # 添加监听
        self.add_listeners()

        # 测试API连接
        if not self.test_api_connection():
            log("API连接测试失败，请检查配置", "ERROR")
            return

        # 初始获取徐州天气并缓存
        log("正在获取徐州天气信息...")
        self.fetch_and_cache_xuzhou_weather()

        # 设置天气播报定时任务
        self.setup_weather_schedule()

        # 设置徐州天气缓存更新定时任务
        self.setup_xuzhou_weather_cache()

        # 设置每日日程功能
        if getattr(config, 'ENABLE_DAILY_SCHEDULE', False):
            self.schedule_manager.set_scheduler(self.scheduler)
            self.schedule_manager.setup_schedule_tasks()
            log("每日日程功能已启用")

        # 设置定时提醒功能
        if getattr(config, 'ENABLE_REMINDER', False):
            self.reminder_manager.set_scheduler(self.scheduler)
            # 设置定时清理任务
            self.scheduler.add_job(
                func=self.reminder_manager.cleanup_old_reminders,
                trigger='cron',
                hour=2,  # 每天凌晨2点清理
                minute=0,
                args=[config.REMINDER_CLEANUP_DAYS],
                id='reminder_cleanup',
                name='定时提醒清理',
                replace_existing=True
            )
            log("定时提醒功能已启用")

        # 启动调度器

        # 添加自动处理好友请求任务
        if getattr(config, 'ENABLE_AUTO_ACCEPT_FRIEND_REQUEST', False):
            interval = getattr(config, 'AUTO_ACCEPT_FRIEND_REQUEST_INTERVAL', 1)
            self.scheduler.add_job(
                func=self.handle_friend_requests,
                trigger='interval',
                minutes=interval,
                id='handle_friend_requests',
                replace_existing=True
            )
            log(f"自动处理好友请求任务已启动，间隔: {interval}分钟")

        self.scheduler.start()
        log("定时任务调度器已启动")

        # 恢复未触发的提醒任务
        if getattr(config, 'ENABLE_REMINDER', False):
            log("🔄 正在检查并恢复未触发的提醒任务...")
            recovered_count = self.reminder_manager.recover_reminders_on_startup()
            if recovered_count > 0:
                log(f"✅ 提醒恢复成功: 已恢复 {recovered_count} 个未触发的提醒任务")
            else:
                log("📭 提醒恢复完成: 没有需要恢复的提醒任务")

        # 启动消息队列
        if self.message_queue_manager:
            self.message_queue_manager.start()
            # log("消息队列已启动")

        # 初始化并启动主动发消息功能
        if getattr(config, 'ENABLE_PROACTIVE_MESSAGE', False):
            self.proactive_message_manager = ProactiveMessageManager(self)
            self.proactive_message_manager.start()
            log("主动发消息功能已启动")
        else:
            log("主动发消息功能已禁用")

        self.running = True
        log("机器人开始运行...")
        log(f"管理员: {config.ADMIN_LIST}")
        log(f"监听用户: {len(config.LISTEN_LIST)}个")
        log(f"监听群组: {len(config.GROUP_LIST)}个")
        log("发送 /help 给管理员查看可用命令")

        try:
            # 主循环
            while self.running:
                time.sleep(1)

        except KeyboardInterrupt:
            log("收到中断信号，正在停止...")
        except Exception as e:
            log(f"运行时异常: {str(e)}", "ERROR")
        finally:
            self.stop()

    def stop(self):
        """停止机器人"""
        if not self.running:
            return

        self.running = False
        log("正在停止机器人...")

        # 停止消息队列
        if self.message_queue_manager:
            try:
                self.message_queue_manager.stop()
                # log("消息队列已停止")
            except Exception as e:
                log(f"停止消息队列异常: {str(e)}", "ERROR")

        # 停止主动发消息功能
        if self.proactive_message_manager:
            try:
                self.proactive_message_manager.stop()
                log("主动发消息功能已停止")
            except Exception as e:
                log(f"停止主动发消息功能异常: {str(e)}", "ERROR")

        # 停止调度器
        if self.scheduler and self.scheduler.running:
            try:
                self.scheduler.shutdown()
                log("定时任务调度器已停止")
            except Exception as e:
                log(f"停止调度器异常: {str(e)}", "ERROR")

        if self.wx:
            try:
                self.wx.StopListening()
                log("微信消息监听已停止")
            except Exception as e:
                log(f"停止监听异常: {str(e)}", "ERROR")

        log("机器人已停止")

    def handle_friend_requests(self):
        """处理新的好友请求"""
        try:
            if not self.wx or not self.wx.IsOnline():
                return

            new_friends = self.wx.GetNewFriends(acceptable=True)
            if not new_friends:
                return

            log(f"发现 {len(new_friends)} 个新的好友请求")
            for friend in new_friends:
                if self.friend_request_manager.has_reached_daily_limit():
                    break

                try:
                    friend.accept(
                        remark=config.AUTO_ACCEPT_FRIEND_REQUEST_REMARK,
                        tags=config.AUTO_ACCEPT_FRIEND_REQUEST_TAGS
                    )
                    log(f"已自动接受好友请求: {friend.name}")
                    self.friend_request_manager.increment_today_accepted_count()

                    # 自动邀请入群
                    if getattr(config, 'ENABLE_AUTO_INVITE_TO_GROUP', False):
                        group_name = getattr(config, 'AUTO_INVITE_TO_GROUP_NAME', None)
                        if group_name:
                            time.sleep(2)  # 等待2秒，确保好友添加成功
                            if self.group_manager.add_group_members(group_name, friend.name):
                                log(f"已成功邀请 {friend.name} 加入群聊 {group_name}")
                            else:
                                log(f"邀请 {friend.name} 加入群聊 {group_name} 失败", "ERROR")
                except Exception as e:
                    log(f"接受好友请求失败: {friend.name}, {str(e)}", "ERROR")
        except Exception as e:
            log(f"处理好友请求异常: {str(e)}", "ERROR")

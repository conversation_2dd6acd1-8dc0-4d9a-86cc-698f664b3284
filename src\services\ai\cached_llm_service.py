"""
缓存LLM服务模块
提供LLM服务的缓存实现
"""

import logging
from src.services.ai.llm_service import LLMService

logger = logging.getLogger('main')

# 全局LLM服务实例
_llm_service_instance = None

def get_cached_llm_service():
    """
    获取缓存的LLM服务实例
    
    Returns:
        LLMService: LLM服务实例
    """
    global _llm_service_instance
    
    if _llm_service_instance is None:
        try:
            # 创建LLM服务实例
            _llm_service_instance = LLMService(
                api_key="test_key",  # 这里应该从配置中获取
                base_url="test_url",  # 这里应该从配置中获取
                model="test_model"   # 这里应该从配置中获取
            )
            logger.info("LLM服务实例创建成功")
        except Exception as e:
            logger.error(f"创建LLM服务实例失败: {str(e)}")
            # 返回一个模拟的服务用于测试
            _llm_service_instance = MockLLMService()
    
    return _llm_service_instance

class MockLLMService:
    """
    模拟LLM服务，用于测试和演示
    """
    
    def __init__(self):
        self.logger = logging.getLogger('main')
    
    def get_response(self, message, system_prompt="", user_id=""):
        """
        模拟LLM响应
        
        Args:
            message: 用户消息
            system_prompt: 系统提示词
            user_id: 用户ID
            
        Returns:
            str: 模拟的响应
        """
        self.logger.info(f"模拟LLM响应 - 用户: {user_id}")
        
        # 简单的模拟响应
        mock_response = '''{
    "success": true,
    "corrected_memory": "我喜欢被用户叫女王大人；用户喜欢摸我的头；用户喜欢躺在我的腿上；用户喜欢抱我；",
    "changes_made": [
        {
            "original": "我喜欢被叫女王大人",
            "corrected": "我喜欢被用户叫女王大人",
            "reason": "用户希望明确是被用户叫女王大人"
        }
    ],
    "confidence": 0.9,
    "explanation": "根据用户的修正意图，将'我喜欢被叫女王大人'修改为'我喜欢被用户叫女王大人'，使表述更加明确。"
}'''
        
        return mock_response
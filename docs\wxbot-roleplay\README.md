# 🤖 微信角色扮演机器人

基于 **wxautox** 库开发的智能微信角色扮演机器人。

## ✨ 核心特性

### 🎭 智能角色扮演
- **深度角色设定**：支持复杂的角色人格、语言风格和行为模式
- **真实化交互**：模拟人类思维模式，包括情绪起伏和口语化表达
- **个性化回应**：根据好感度和情境动态调整语气和回复内容
- **防护机制**：内置角色身份锚定和重置触发器，确保角色设定稳定
- **多角色支持**：支持自定义角色设定文件，轻松切换不同角色

### 💬 智能消息处理
- **多场景支持**：同时支持私聊和群聊，智能识别@消息和拍一拍
- **文本智能处理**：自动移除括号内容、分割长句、识别图片标签
- **重复检测**：智能识别重复消息，避免无效对话循环
- **自然发送**：模拟真实打字节奏，分句发送长消息
- **群聊智能参与**：智能判断是否参与群聊对话，避免过度活跃

### 💖 情感系统
- **动态好感度**：基于对话内容和互动频率的智能好感度算法
- **等级体系**：从萍水相逢到永恒之约的完整好感度等级系统
- **情感记忆**：持久化存储用户关系发展历程
- **个性化语气**：根据好感度等级调整回复的亲密程度和语言风格

### 🧠 智能记忆系统
- **持久化存储**：基于SQLite的对话记忆系统，支持长期记忆保持
- **智能摘要**：自动生成对话摘要，提取关键信息
- **上下文理解**：结合历史对话提供连贯的角色扮演体验
- **记忆管理**：支持记忆清理、统计和手动管理

### 🎨 AI图片生成
- **多服务支持**：支持OpenAI兼容API和DeepSeek Janus模型
- **智能识别**：自动识别图片生成请求关键词
- **提示词优化**：自动提取和优化图片生成提示词
- **水印控制**：支持开启/关闭图片水印功能

### 🌟 智能服务
- **天气服务**：集成高德地图API，支持实时天气查询和定时播报
- **日程管理**：AI智能生成每日日程，定时提醒重要事项
- **主动互动**：智能主动发送消息，维持用户关系活跃度
- **定时提醒**：支持自然语言设置定时提醒功能

## 📁 项目结构

```
wxbot-roleplay/
├── 📄 main.py                      # 主程序入口
├── 🤖 bot.py                       # 机器人核心逻辑
├── ⚙️ config.py                    # 配置文件
├── 🗄️ database.py                  # 数据库模型
├── 📋 requirements.txt             # 依赖包列表
├── 🖥️ 启动.bat                     # Windows启动脚本
├── 📝 prompts/                     # AI角色提示词
├── 🛠️ utils/                       # 核心工具模块
├── 📊 logs/                        # 运行日志
├── 📅 schedules/                   # 日程文件
├── 🖼️ generated_images/            # 生成的图片文件
└── 🗃️ chat_history.db              # SQLite数据库
```

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 微信PC版（已登录）
- wxautox库许可证

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd wxbot-roleplay
```

2. **安装依赖**
```bash
pip install -r requirements.txt
# 或使用清华源加速
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

3. **配置设置**

编辑 `config.py` 文件，设置监听用户、API密钥等基本配置：

```python
# 基础配置
LISTEN_LIST = ['用户1', '用户2']        # 监听用户列表
GROUP_LIST = ['群名1', '群名2']         # 监听群组列表
ADMIN_LIST = ['管理员昵称']             # 管理员列表

# AI API配置
API_KEY = 'your-api-key-here'          # AI API密钥
BASE_URL = 'https://api.openai.com/v1' # API地址
MODEL = 'gpt-3.5-turbo'                # 使用的模型
```

4. **自定义角色**

编辑 `prompts/试做型-林小葵.md` 来设定AI角色的性格和行为：

```markdown
# 角色设定：林小葵

你是林小葵，一个22岁的女大学生...
（详细的角色设定和性格描述）
```

5. **启动程序**

**Windows用户**：双击 `启动.bat` 或在命令行运行 `python main.py`

## 📋 核心功能详解

### 🎯 角色扮演系统
- 支持详细的角色人格设定，包括性格、背景、语言风格
- 内置防御机制，确保角色身份稳定，防止角色偏移
- 根据时间、天气和用户状态自动调整回复风格
- 支持配置多个角色设定文件，轻松切换不同角色

### 📝 智能对话系统
- 基于OpenAI兼容API生成自然、连贯的对话回复
- 结合历史记忆提供上下文连贯的对话体验
- 自动进行文本格式化、分句发送和特殊内容过滤
- 智能识别并避免重复回复，提升对话质量

### 💾 记忆管理系统
- 使用SQLite数据库持久化存储对话记录
- 自动生成对话摘要，提取关键信息和关键词
- 支持定期清理过期记忆数据，优化存储空间
- 智能缓存群聊消息，提供更好的上下文理解

### ❤️ 好感度系统
- 基于对话内容和互动频率动态计算好感度
- 完整的7级好感度等级体系（萍水相逢→永恒之约）
- 根据好感度调整回复语气和亲密程度
- 记录好感度变化历程，支持统计分析和排行榜

### 🖼️ 图片生成系统
- 支持OpenAI兼容API和DeepSeek Janus等多种图片生成服务
- 自动识别包含"画"、"生成图片"等关键词的请求
- 智能提取和优化图片生成提示词，提升生成质量
- 支持开启/关闭图片水印功能，保护知识产权

### 🌤️ 天气服务系统
- 集成高德地图API获取准确的实时天气信息
- 支持查询任意城市天气，提供多城市天气播报
- 自动定时发送天气播报，支持天气变化主动提醒
- 智能缓存天气数据，提高响应速度

### 📅 日程管理系统
- AI智能生成每日日程安排，合理规划用户时间
- 支持日程事项的定时提醒，可配置提前提醒时间
- JSON格式存储日程数据，支持手动生成和修改
- 自动提醒日程事项，帮助用户养成良好习惯

### 🔔 定时提醒系统
- 支持自然语言解析，轻松设置提醒时间
- 支持相对时间（如"15分钟后"）和绝对时间（如"下午3点"）
- 生成个性化的提醒消息，提升用户体验
- 支持查看、删除、修改提醒，定期清理已触发提醒

### 💬 群聊参与度管理
- 基于多因素算法智能判断是否参与群聊对话
- 实时计算群聊对话活跃度，动态调整参与频率
- 内置冷却机制，避免过度活跃影响用户体验
- 支持多种参与模式（智能/全部/@消息）

## ⚙️ 管理员命令系统

| 命令类别 | 命令 | 说明 |
|---------|------|------|
| 基础管理 | `/help` | 显示帮助信息 |
| | `/status` | 显示机器人状态 |
| | `/add_user 用户名` | 添加监听用户 |
| | `/remove_user 用户名` | 移除监听用户 |
| 记忆管理 | `/memory_stats` | 显示记忆统计 |
| | `/clear_memory 用户名` | 清除指定记忆 |
| | `/cleanup_memory` | 清理旧记忆 |
| 好感度管理 | `/favor_info 用户名` | 查看好感度信息 |
| | `/favor_set 用户名 数值` | 设置好感度 |
| | `/favor_list` | 好感度排行榜 |
| 图片生成管理 | `/image_status` | 查看图片生成功能状态 |
| | `/toggle_watermark` | 切换图片水印开关 |
| 天气服务管理 | `/weather 城市名` | 查询天气 |
| | `/send_weather` | 手动发送天气播报 |

## 🔧 配置优化建议

### AI模型选择
- **GPT-3.5-turbo**：性价比高，适合日常对话
- **GPT-4**：质量更高，但成本较高
- **国产模型**：如通义千问、文心一言等，成本更低
- **图片模型**：DeepSeek Janus适合图片生成

### 性能调优
- **记忆管理**：合理设置记忆保留天数，定期清理旧数据
- **缓存策略**：启用记忆缓存提升响应速度
- **并发控制**：根据硬件配置调整线程数量
- **文件管理**：定期清理生成的图片和日志文件

### 安全配置
- **API限制**：设置合理的API调用频率限制
- **内容过滤**：启用防御提示词过滤不当内容
- **权限控制**：严格控制管理员权限
- **数据保护**：定期备份数据库文件和配置文件

## ⚠️ 常见问题与故障排除

### 连接问题
- **微信连接失败**：确保微信PC版已登录并保持在线，重启微信和程序
- **AI回复失败**：检查API密钥配置是否正确，验证网络连接状态
- **消息监听失败**：确保用户/群组名称正确，检查是否有权限访问该聊天

### 功能异常
- **天气功能异常**：检查高德API密钥配置，确认网络连接正常
- **数据库问题**：确保SQLite数据库文件可读写，检查磁盘空间
- **主动消息不工作**：确认功能已启用，检查时间设置是否在静默时间内

### 调试技巧
- **查看日志**：详细的日志信息在 `logs/` 目录
- **测试命令**：使用 `/test_ai` 等命令测试各功能
- **逐步排查**：从基础功能开始逐步测试

## 📄 许可证

本项目采用 MIT 许可证，仅供学习交流使用。使用本项目时请遵守：
- 相关法律法规
- 微信平台使用条款
- wxautox库的许可协议
- 第三方API服务条款

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的角色扮演系统
- ✅ 智能记忆管理
- ✅ 动态好感度系统
- ✅ AI图片生成功能
- ✅ 智能群聊参与系统
- ✅ 定时提醒功能
- ✅ 天气服务集成
- ✅ 主动消息功能
- ✅ 日程管理系统

### 计划功能
- 🔄 多角色切换支持
- 🔄 语音消息处理
- 🔄 图片识别和回复
- 🔄 Web管理界面
- 🔄 插件系统

## 📊 功能特性总览

| 功能模块 | 功能特性 | 状态 | 说明 |
|---------|---------|------|------|
| **角色扮演** | 深度角色设定 | ✅ | 支持复杂人格设定和行为模式 |
| **智能对话** | AI对话生成 | ✅ | 基于OpenAI兼容API |
| **记忆系统** | 持久化存储 | ✅ | SQLite数据库存储 |
| **好感度系统** | 动态计算 | ✅ | 基于互动的智能算法 |
| **图片生成** | AI图片生成 | ✅ | 支持多种生成服务 |
| **群聊管理** | 智能参与 | ✅ | 基于多因素的参与判断 |
| **定时提醒** | 自然语言解析 | ✅ | 支持多种时间表达 |
| **天气服务** | 实时查询 | ✅ | 高德地图API集成 |
| **日程管理** | AI日程生成 | ✅ | 智能生成日程安排 |
| **主动消息** | 智能发送 | ✅ | 基于好感度和时机 |

## 📞 技术支持

如果遇到问题，可以通过以下方式获取帮助：
1. **查看文档**：仔细阅读本README和代码注释
2. **查看日志**：分析logs目录下的日志文件
3. **搜索Issue**：查看是否有类似问题的解决方案
4. **提交Issue**：详细描述问题和环境信息

---

**开发参考**：本项目参考了 My-Dream-Moments-WeChat-wxauto 和 SiverWxBotWebV2.2.3 等优秀项目的设计理念和代码结构。

**免责声明**：本项目仅供学习交流使用，使用者需自行承担使用风险，开发者不承担任何法律责任。

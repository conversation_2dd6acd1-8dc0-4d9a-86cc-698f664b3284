"""
LLM AI 服务模块
提供与LLM API的完整交互实现，包含以下核心功能：
- API请求管理
- 上下文对话管理
- 响应安全处理
- 智能错误恢复
"""

import logging
import re
import os
import random
import json  # 新增导入
import time  # 新增导入
import pathlib
import requests
from typing import Dict, List, Optional, Tuple, Union
from openai import OpenAI
from src.autoupdate.updater import Updater
from tenacity import (
    retry,
    stop_after_attempt,
    wait_random_exponential,
    retry_if_exception_type
)

# 导入emoji库用于处理表情符号
import emoji
from functools import lru_cache

# 修改logger获取方式，确保与main模块一致
logger = logging.getLogger('main')

class LLMService:
    def __init__(self, api_key: str, base_url: str, model: str,
                 max_token: int, temperature: float, max_groups: int):
        """
        强化版AI服务初始化

        :param api_key: API认证密钥
        :param base_url: API基础URL
        :param model: 使用的模型名称
        :param max_token: 最大token限制
        :param temperature: 创造性参数(0~2)
        :param max_groups: 最大对话轮次记忆
        :param system_prompt: 系统级提示词
        """
        # 创建 Updater 实例获取版本信息
        updater = Updater()
        version = updater.get_current_version()
        version_identifier = updater.get_version_identifier()

        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            default_headers={
                "Content-Type": "application/json",
                "User-Agent": version_identifier,
                "X-KouriChat-Version": version
            }
        )
        self.config = {
            "model": model,
            "max_token": max_token,
            "temperature": temperature,
            "max_groups": max_groups,
        }
        
        # 读取人设重置配置
        try:
            # 尝试从配置文件读取重置间隔
            import json
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config', 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    # 获取重置间隔，默认为5轮 - 修正配置路径
                    self.character_reset_interval = config_data.get('categories', {}).get('behavior_settings', {}).get('settings', {}).get('context', {}).get('character_reset', {}).get('interval', {}).get('value', 5)
                    self.character_reset_enabled = config_data.get('categories', {}).get('behavior_settings', {}).get('settings', {}).get('context', {}).get('character_reset', {}).get('enabled', {}).get('value', True)
            else:
                # 使用默认值
                self.character_reset_interval = 5
                self.character_reset_enabled = True
        except Exception as e:
            logger.warning(f"读取人设重置配置失败，使用默认值: {str(e)}")
            self.character_reset_interval = 5
            self.character_reset_enabled = True
        self.chat_contexts: Dict[str, List[Dict]] = {}

        # 安全字符白名单（可根据需要扩展）
        self.safe_pattern = re.compile(r'[\x00-\x1F\u202E\u200B]')

        # 如果是 Ollama，获取可用模型列表
        if 'localhost:11434' in base_url:
            self.available_models = self.get_ollama_models()
        else:
            self.available_models = []

    def _manage_context(self, user_id: str, message: Union[str, Dict], role: str = "user"):
        """
        上下文管理器（支持动态记忆窗口和工具调用）

        :param user_id: 用户唯一标识
        :param message: 消息内容（字符串或字典）
        :param role: 角色类型(user/assistant/tool)
        """
        if user_id not in self.chat_contexts:
            self.chat_contexts[user_id] = []

        # 添加新消息（支持工具调用格式）
        if isinstance(message, dict):
            # 处理工具调用或工具结果格式
            if role == "tool":
                self.chat_contexts[user_id].append(message)
            elif role == "assistant" and "tool_calls" in message:
                # 助手的工具调用消息
                self.chat_contexts[user_id].append({
                    "role": "assistant",
                    "content": message.get("content"),
                    "tool_calls": message.get("tool_calls")
                })
            else:
                # 其他字典格式消息
                self.chat_contexts[user_id].append({"role": role, "content": str(message)})
        else:
            # 普通字符串消息
            self.chat_contexts[user_id].append({"role": role, "content": message})

        # 维护上下文窗口
        while len(self.chat_contexts[user_id]) > self.config["max_groups"] * 2:
            # 优先保留最近的对话组
            self.chat_contexts[user_id] = self.chat_contexts[user_id][-self.config["max_groups"]*2:]

    def _should_reset_character(self, user_id: str) -> bool:
        """
        判断是否需要喂人设
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否需要喂人设
        """
        try:
            # 检查是否启用重置功能
            if not getattr(self, 'character_reset_enabled', True):
                return False
            
            # 获取用户对话轮数
            user_message_count = len(self.chat_contexts.get(user_id, [])) // 2
            
            # 从配置中获取重置间隔，默认为5轮
            reset_interval = getattr(self, 'character_reset_interval', 5)
            
            # 每N轮对话喂一次人设
            return user_message_count > 0 and user_message_count % reset_interval == 0
            
        except Exception as e:
            logger.error(f"判断人设重置失败: {str(e)}")
            return False

  
    def _sanitize_response(self, raw_text: str) -> str:
        """
        响应安全处理器
        1. 移除控制字符
        2. 标准化换行符
        3. 防止字符串截断异常
        4. 处理emoji表情符号，确保跨平台兼容性
        """
        try:
            # 移除控制字符
            cleaned = re.sub(self.safe_pattern, '', raw_text)

            # 标准化换行符
            cleaned = cleaned.replace('\r\n', '\n').replace('\r', '\n')

            # 处理emoji表情符号
            cleaned = self._process_emojis(cleaned)

            return cleaned
        except Exception as e:
            logger.error(f"Response sanitization failed: {str(e)}")
            return "响应处理异常，请重新尝试"

    def _process_emojis(self, text: str) -> str:
        """处理文本中的emoji表情符号，确保跨平台兼容性"""
        try:
            # 先将Unicode表情符号转换为别名再转回，确保标准化
            return emoji.emojize(emoji.demojize(text))
        except Exception:
            return text  # 如果处理失败，返回原始文本

    def _filter_thinking_content(self, content: str) -> str:
        """
        过滤思考内容，支持不同模型的返回格式
        1. R1格式: 思考过程...\n\n\n最终回复
        2. Gemini格式: <think>思考过程</think>\n\n最终回复
        """
        try:
            # 使用分割替代正则表达式处理 Gemini 格式
            if '<think>' in content and '</think>' in content:
                parts = content.split('</think>')
                # 只保留最后一个</think>后的内容
                filtered_content = parts[-1].strip()
            else:
                filtered_content = content

            # 过滤 R1 格式 (思考过程...\n\n\n最终回复)
            # 查找三个连续换行符
            triple_newline_match = re.search(r'\n\n\n', filtered_content)
            if triple_newline_match:
                # 只保留三个连续换行符后面的内容（最终回复）
                filtered_content = filtered_content[triple_newline_match.end():]

            return filtered_content.strip()
        except Exception as e:
            logger.error(f"过滤思考内容失败: {str(e)}")
            return content  # 如果处理失败，返回原始内容

    def _validate_response(self, response: dict) -> bool:
        """
        放宽检验
        API响应校验
        只要能获取到有效的回复内容就返回True
        """
        try:
            # 调试：打印完整响应结构
            logger.debug(f"API响应结构: {json.dumps(response, default=str, indent=2)}")

            # 尝试获取回复内容
            if isinstance(response, dict):
                choices = response.get("choices", [])
                if choices and isinstance(choices, list):
                    first_choice = choices[0]
                    if isinstance(first_choice, dict):
                        # 尝试不同的响应格式
                        # 格式1: choices[0].message.content
                        if isinstance(first_choice.get("message"), dict):
                            content = first_choice["message"].get("content")
                            if content and isinstance(content, str):
                                return True

                        # 格式2: choices[0].content
                        content = first_choice.get("content")
                        if content and isinstance(content, str):
                            return True

                        # 格式3: choices[0].text
                        text = first_choice.get("text")
                        if text and isinstance(text, str):
                            return True

            logger.warning("无法从响应中获取有效内容，完整响应: %s", json.dumps(response, default=str))
            return False

        except Exception as e:
            logger.error(f"验证响应时发生错误: {str(e)}")
            return False

    def get_response(self, message: str, user_id: str, system_prompt: str, previous_context: List[Dict] = None, core_memory: str = None, tools: List[Dict] = None, tool_executor = None, context: List[Dict] = None) -> str:
        """
        完整请求处理流程
        Args:
            message: 用户消息
            user_id: 用户ID
            system_prompt: 系统提示词（人设）
            previous_context: 历史上下文（可选）
            core_memory: 核心记忆（可选）
            tools: 可用工具列表（可选）
            tool_executor: 工具执行器（可选）
            context: 已弃用，请使用 previous_context（仅向后兼容）
        """
        # —— 阶段0：处理向后兼容的参数 ——
        if context is not None:
            logger.warning("检测到已弃用的 'context' 参数，请使用 'previous_context' 替代")
            if previous_context is None:
                previous_context = context
            else:
                logger.warning("同时提供了 'context' 和 'previous_context' 参数，将使用 'previous_context'")
        
        # —— 阶段1：输入验证 ——
        if not message.strip():
            return "Error: Empty message received"

        # —— 阶段2：上下文更新 ——
        # 只在程序刚启动时（上下文为空时）加载外部历史上下文
        if previous_context and user_id not in self.chat_contexts:
            logger.info(f"程序启动初始化：为用户 {user_id} 加载历史上下文，共 {len(previous_context)} 条消息")
            # 确保上下文只包含当前用户的历史信息
            self.chat_contexts[user_id] = previous_context.copy()

        # 添加当前消息到上下文
        self._manage_context(user_id, message)

        # —— 阶段3：构建请求参数 ——
        # 读取基础Prompt
        try:
            base_content = self._read_base_prompt()
        except Exception as e:
            logger.error(f"基础Prompt文件读取失败: {str(e)}")
            base_content = ""

        # 构建完整提示词: base + 核心记忆 + 人设
        if core_memory:
            final_prompt = f"{base_content}\n\n\n人设的核心记忆为：\n{core_memory}\n\n\n{system_prompt}"
            logger.debug("提示词顺序：base.md + 核心记忆 + 人设")
        else:
            final_prompt = f"{base_content}\n\n{system_prompt}"
            logger.debug("提示词顺序：base.md + 人设")

        # 检查是否需要喂人设
        if self._should_reset_character(user_id):
            # 每5轮：上下文 + 完整人设 + 用户消息
            messages = [
                {"role": "system", "content": base_content},  # 基础内容
                *self.chat_contexts.get(user_id, [])[-self.config["max_groups"] * 2:],  # 上下文
                {"role": "system", "content": system_prompt},  # 完整人设
                {"role": "user", "content": message}
            ]
            logger.info(f"为用户 {user_id} 触发喂人设，当前对话轮数: {len(self.chat_contexts.get(user_id, [])) // 2}")
        else:
            # 平时：完整人设 + 上下文 + 用户消息
            messages = [
                {"role": "system", "content": final_prompt},  # 完整人设
                *self.chat_contexts.get(user_id, [])[-self.config["max_groups"] * 2:],  # 上下文
                {"role": "user", "content": message}
            ]

        # 为 Ollama 构建消息内容
        chat_history = self.chat_contexts.get(user_id, [])[-self.config["max_groups"] * 2:]
        history_text = "\n".join([
            f"{msg['role']}: {msg['content']}"
            for msg in chat_history
        ])
        
        # 为Ollama构建消息内容
        if self._should_reset_character(user_id):
            # 每5轮：上下文 + 完整人设 + 用户消息
            ollama_content = f"{base_content}\n\n对话历史：\n{history_text}\n\n{system_prompt}\n\n用户问题：{message}"
        else:
            # 平时：完整人设 + 上下文 + 用户消息
            ollama_content = f"{final_prompt}\n\n对话历史：\n{history_text}\n\n用户问题：{message}"
        
        # 检查prompt长度，防止过长导致API错误
        if len(ollama_content) > 100000:  # 设置一个合理的上限
            logger.warning(f"Prompt过长 ({len(ollama_content)} 字符)，可能超过API限制")
            # 如果是喂人设模式，使用简化版本
            if self._should_reset_character(user_id):
                ollama_content = f"{system_prompt}\n\n用户问题：{message}"
            else:
                ollama_content = f"{final_prompt}\n\n用户问题：{message}"
        
        ollama_message = {
            "role": "user",
            "content": ollama_content
        }

        # 检查是否是 Ollama API
        is_ollama = 'localhost:11434' in str(self.client.base_url)

        # —— 阶段4：执行API请求（带重试机制）——
        max_retries = 3
        last_error = None

        for attempt in range(max_retries):
            try:
                if is_ollama:
                    # Ollama API 格式
                    request_config = {
                        "model": self.config["model"].split('/')[-1],  # 移除路径前缀
                        "messages": [ollama_message],  # 将消息包装在列表中
                        "stream": False,
                        "options": {
                            "temperature": self.config["temperature"],
                            "max_tokens": self.config["max_token"]
                        }
                    }

                    # 使用 requests 库向 Ollama API 发送 POST 请求
                    # 创建 Updater 实例获取版本信息
                    updater = Updater()
                    version = updater.get_current_version()
                    version_identifier = updater.get_version_identifier()

                    # 构建 Ollama API 的完整 URL
                    ollama_api_url = f"{str(self.client.base_url).rstrip('/')}/api/chat"
                    logger.debug(f"Ollama API URL: {ollama_api_url}")
                    
                    response = requests.post(
                        ollama_api_url,
                        json=request_config,
                        headers={
                            "Content-Type": "application/json",
                            "User-Agent": version_identifier,
                            "X-KouriChat-Version": version
                        }
                    )
                    logger.debug(f"请求URL: {ollama_api_url}")
                    logger.debug(f"请求数据: {json.dumps(request_config, ensure_ascii=False, indent=2)}")
                    
                    response = requests.post(
                        ollama_api_url,
                        json=request_config,
                        headers={
                            "Content-Type": "application/json",
                            "User-Agent": version_identifier,
                            "X-KouriChat-Version": version
                        },
                        timeout=30  # 添加超时设置
                    )
                    
                    logger.debug(f"响应状态码: {response.status_code}")
                    logger.debug(f"响应头: {dict(response.headers)}")
                    
                    response.raise_for_status()
                    
                    # 检查响应内容是否为空或不是有效的JSON
                    response_text = response.text.strip()
                    logger.debug(f"API原始响应: {response_text[:500]}...")
                    
                    if not response_text:
                        raise ValueError("API返回空响应")
                    
                    # 检查是否是HTML错误页面
                    if response_text.startswith('<') or response_text.startswith('HTTP'):
                        raise ValueError(f"API返回HTML错误页面: {response_text[:200]}...")
                    
                    # 尝试解析JSON
                    try:
                        response_data = response.json()
                        logger.debug(f"解析后的JSON数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        logger.error(f"响应内容: {response_text}")
                        logger.error(f"响应状态码: {response.status_code}")
                        logger.error(f"响应头: {dict(response.headers)}")
                        raise

                    # 检查响应中是否包含 message 字段
                    if response_data and "message" in response_data:
                        raw_content = response_data["message"]["content"]

                        # 处理 R1 特殊格式，可能包含 reasoning_content 字段
                        if isinstance(response_data["message"], dict) and "reasoning_content" in response_data["message"]:
                            logger.debug("检测到 R1 格式响应，将分离思考内容")
                            # 只使用 content 字段内容，忽略 reasoning_content
                            raw_content = response_data["message"]["content"]
                    else:
                        raise ValueError(f"错误的API响应结构: {json.dumps(response_data, default=str)}")

                else:
                    # 标准 OpenAI 格式
                    request_config = {
                        "model": self.config["model"],  # 模型名称
                        "messages": messages,  # 消息列表
                        "temperature": self.config["temperature"],  # 温度参数
                        "max_tokens": self.config["max_token"],  # 最大 token 数
                        "top_p": 0.95,  # top_p 参数
                        "frequency_penalty": 0.2  # 频率惩罚参数
                    }
                    
                    # 如果提供了工具，添加到请求配置中
                    if tools and tool_executor:
                        request_config["tools"] = tools
                        request_config["tool_choice"] = "auto"

                    # 使用 OpenAI 客户端发送请求
                    response = self.client.chat.completions.create(**request_config)

                    # 验证 API 响应结构
                    if not self._validate_response(response.model_dump()):
                        raise ValueError(f"错误的API响应结构: {json.dumps(response.model_dump(), default=str)}")

                    # 检查是否有工具调用
                    choice = response.choices[0]
                    if choice.message.tool_calls and tool_executor:
                        # 处理工具调用
                        logger.info(f"检测到工具调用: {len(choice.message.tool_calls)} 个工具")
                        
                        # 将工具调用消息添加到上下文
                        self._manage_context(user_id, choice.message.model_dump(), "assistant")
                        
                        # 执行所有工具调用
                        tool_results = []
                        for tool_call in choice.message.tool_calls:
                            function_name = tool_call.function.name
                            function_args = json.loads(tool_call.function.arguments)
                            
                            logger.debug(f"执行工具: {function_name}, 参数: {function_args}")
                            
                            import concurrent.futures
                            
                            def execute_tool_with_timeout():
                                return tool_executor.execute_tool(function_name, function_args)
                            
                            try:
                                # 使用线程池执行，设置20秒超时
                                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                                    future = executor.submit(execute_tool_with_timeout)
                                    result = future.result(timeout=20)
                                
                                tool_results.append({
                                    "tool_call_id": tool_call.id,
                                    "role": "tool", 
                                    "name": function_name,
                                    "content": result or "工具执行完成，但未返回内容"
                                })
                            except concurrent.futures.TimeoutError:
                                logger.error(f"工具调用超时: {function_name}")
                                # 工具调用超时时，直接跳过该工具
                                tool_results.append({
                                    "tool_call_id": tool_call.id,
                                    "role": "tool",
                                    "name": function_name, 
                                    "content": "工具调用超时，已跳过"
                                })
                            except Exception as e:
                                logger.error(f"工具调用失败: {function_name}, 错误: {e}")
                                # 工具调用失败时，记录错误但不中断流程，继续正常聊天
                                tool_results.append({
                                    "tool_call_id": tool_call.id,
                                    "role": "tool",
                                    "name": function_name, 
                                    "content": f"工具调用失败: {str(e)}"
                                })
                                # 设置错误标志，用于后续处理
                                if not hasattr(self, '_memory_tool_error'):
                                    self._memory_tool_error = str(e)
                        
                        # 将工具结果添加到上下文
                        for tool_result in tool_results:
                            self._manage_context(user_id, tool_result, "tool")
                        
                        # 再次调用AI以生成基于工具结果的最终回复
                        final_messages = [
                            {"role": "system", "content": final_prompt},
                            *self.chat_contexts.get(user_id, [])[-self.config["max_groups"] * 2:]
                        ]
                        
                        final_response = self.client.chat.completions.create(
                            model=self.config["model"],
                            messages=final_messages,
                            temperature=self.config["temperature"],
                            max_tokens=self.config["max_token"]
                        )
                        
                        raw_content = final_response.choices[0].message.content
                    else:
                        # 获取原始内容（无工具调用）
                        raw_content = response.choices[0].message.content

                # 清理响应内容
                clean_content = self._sanitize_response(raw_content)
                # 过滤思考内容
                filtered_content = self._filter_thinking_content(clean_content)

                # 检查响应内容是否为错误消息
                if filtered_content.strip().lower().startswith("error"):
                    raise ValueError(f"错误响应: {filtered_content}")

                # 成功获取有效响应，更新上下文并返回
                self._manage_context(user_id, filtered_content, "assistant")
                
                # 如果有智能记忆工具错误，返回包含错误信息的结构
                memory_error = getattr(self, '_memory_tool_error', None)
                if memory_error:
                    # 清除错误标志，避免影响下次调用
                    if hasattr(self, '_memory_tool_error'):
                        delattr(self, '_memory_tool_error')
                    return {
                        "content": filtered_content or "",
                        "memory_error": memory_error
                    }
                
                return filtered_content or ""

            except Exception as e:
                last_error = f"Error: {str(e)}"
                logger.warning(f"API请求失败 (尝试 {attempt+1}/{max_retries}): {str(e)}")

                # 如果这不是最后一次尝试，则继续
                if attempt < max_retries - 1:
                    continue

        # 所有重试都失败后，记录最终错误并返回
        logger.error(f"所有重试尝试均失败: {last_error}")
        return last_error

    def clear_history(self, user_id: str) -> bool:
        """
        清空指定用户的对话历史
        """
        if user_id in self.chat_contexts:
            del self.chat_contexts[user_id]
            logger.info("已清除用户 %s 的对话历史", user_id)
            return True
        return False

    def analyze_usage(self, response: dict) -> Dict:
        """
        用量分析工具
        """
        usage = response.get("usage", {})
        return {
            "prompt_tokens": usage.get("prompt_tokens", 0),
            "completion_tokens": usage.get("completion_tokens", 0),
            "total_tokens": usage.get("total_tokens", 0),
            "estimated_cost": (usage.get("total_tokens", 0) / 1000) * 0.02  # 示例计价
        }

    def chat(self, messages: list, **kwargs) -> str:
        """
        发送聊天请求并获取回复

        Args:
            messages: 消息列表，每个消息是包含 role 和 content 的字典
            **kwargs: 额外的参数配置，包括 model、temperature 等

        Returns:
            str: AI的回复内容
        """
        try:
            # 使用传入的model参数，如果没有则使用默认模型
            model = kwargs.get('model', self.config["model"])
            logger.info(f"使用模型: {model} 发送聊天请求")

            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=kwargs.get('temperature', self.config["temperature"]),
                max_tokens=self.config["max_token"]
            )

            if not self._validate_response(response.model_dump()):
                error_msg = f"错误的API响应结构: {json.dumps(response.model_dump(), default=str)}"
                logger.error(error_msg)
                return f"Error: {error_msg}"

            raw_content = response.choices[0].message.content
            # 清理和过滤响应内容
            clean_content = self._sanitize_response(raw_content)
            filtered_content = self._filter_thinking_content(clean_content)

            return filtered_content or ""

        except Exception as e:
            logger.error(f"Chat completion failed: {str(e)}")
            return f"Error: {str(e)}"

    def get_ollama_models(self) -> List[Dict]:
        """获取本地 Ollama 可用的模型列表"""
        try:
            response = requests.get('http://localhost:11434/api/tags')
            if response.status_code == 200:
                models = response.json().get('models', [])
                return [
                    {
                        "id": model['name'],
                        "name": model['name'],
                        "status": "active",
                        "type": "chat",
                        "context_length": 16000  # 默认上下文长度
                    }
                    for model in models
                ]
            return []
        except Exception as e:
            logger.error(f"获取Ollama模型列表失败: {str(e)}")
            return []

    def get_config(self) -> Dict:
        """
        获取当前LLM服务的配置参数
        方便外部服务（如记忆服务）获取最新配置

        Returns:
            Dict: 包含当前配置的字典
        """
        return self.config.copy()  # 返回配置的副本以防止外部修改

    @staticmethod
    @lru_cache(maxsize=1)
    def _read_base_prompt() -> str:
        """读取 data/base/base.md 并缓存结果"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        base_prompt_path = os.path.join(project_root, "data", "base", "base.md")
        with open(base_prompt_path, "r", encoding="utf-8") as f:
            return f.read()

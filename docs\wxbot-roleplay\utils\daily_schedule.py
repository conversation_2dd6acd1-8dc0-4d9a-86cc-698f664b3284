#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日日程管理模块
基于My-Dream-Moments-WeChat-wxauto项目的日程功能
"""

import os
import json
import random
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger

import config
from utils.logger import log
from utils.text_processor import format_response, split_sentences, apply_message_delay


class DailyScheduleManager:
    """每日日程管理器"""

    def __init__(self, bot=None):
        """
        初始化日程管理器

        Args:
            bot: 机器人实例
        """
        self.bot = bot
        self.scheduler = None
        self.schedule_dir = config.SCHEDULE_DIR
        self.timezone = timezone(timedelta(hours=8))  # 东八区

        # 确保日程目录存在
        if not os.path.exists(self.schedule_dir):
            os.makedirs(self.schedule_dir)
            log(f"创建日程目录: {self.schedule_dir}")

    def set_bot(self, bot):
        """设置机器人实例"""
        self.bot = bot

    def set_scheduler(self, scheduler):
        """设置调度器实例"""
        self.scheduler = scheduler

    def generate_daily_schedule(self, date_offset: int = 0) -> bool:
        """
        生成指定日期的日程

        Args:
            date_offset: 日期偏移量（0=当天，1=次日）

        Returns:
            是否生成成功
        """
        try:
            # 计算目标日期
            target_date = datetime.now(self.timezone) + timedelta(days=date_offset)
            date_str = target_date.strftime("%Y-%m-%d")

            # 检查是否已存在
            file_path = os.path.join(self.schedule_dir, f"{date_str}.json")
            if os.path.exists(file_path):
                log(f"{date_str}日程已存在，跳过生成")
                return True

            # 动态填充提示词
            prompt = config.SCHEDULE_PROMPT.format(date=date_str)

            # 调用AI生成日程
            if not self.bot or not hasattr(self.bot, 'api_client') or not self.bot.api_client:
                log("AI客户端未初始化，使用默认日程", "WARNING")
                return self._create_fallback_schedule(target_date)

            try:
                # 使用机器人的完整提示词构建系统消息
                system_prompt = getattr(self.bot, 'prompt_content', "你是林小葵，一个贴心的AI助手。")
                system_prompt += "\n\n[特殊任务]: 负责生成每日日程安排。请严格按照JSON格式返回，保持林小葵的人格特征。"

                # 构建消息格式
                messages = [
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]

                # 调用AI API
                response = self.bot.api_client.chat.completions.create(
                    model=self.bot.model,
                    messages=messages,
                    max_tokens=500,
                    temperature=0.7
                )

                ai_response = response.choices[0].message.content.strip()

                # 清理响应内容
                if not ai_response.startswith("{"):
                    # 尝试提取JSON部分
                    start_idx = ai_response.find("{")
                    end_idx = ai_response.rfind("}") + 1
                    if start_idx != -1 and end_idx != 0:
                        ai_response = ai_response[start_idx:end_idx]
                    else:
                        raise ValueError("AI响应不是有效的JSON格式")

                # 解析JSON
                schedule_data = json.loads(ai_response)

                # 验证数据格式
                if not self._validate_schedule_data(schedule_data):
                    raise ValueError("日程数据格式不正确")

                # 保存日程
                self._save_schedule(schedule_data)
                log(f"成功生成{date_str}的日程")
                return True

            except Exception as ai_e:
                log(f"AI生成日程失败: {str(ai_e)}", "ERROR")
                return self._create_fallback_schedule(target_date)

        except Exception as e:
            log(f"生成日程失败: {str(e)}", "ERROR")
            return False

    def _validate_schedule_data(self, data: Dict) -> bool:
        """验证日程数据格式"""
        try:
            if not isinstance(data, dict):
                return False

            if 'date' not in data or 'schedule' not in data:
                return False

            if not isinstance(data['schedule'], list):
                return False

            for item in data['schedule']:
                if not isinstance(item, dict):
                    return False
                if 'time' not in item or 'activity' not in item:
                    return False

                # 验证时间格式
                try:
                    datetime.strptime(item['time'], "%H:%M")
                except ValueError:
                    return False

            return True

        except Exception:
            return False

    def _create_fallback_schedule(self, target_date: datetime) -> bool:
        """创建应急日程"""
        try:
            date_str = target_date.strftime("%Y-%m-%d")
            log(f"创建应急日程: {date_str}", "WARNING")

            fallback = {
                "date": date_str,
                "schedule": [
                    {"time": "09:00", "activity": "晨练"},
                    {"time": "12:00", "activity": "午餐时间"},
                    {"time": "15:00", "activity": "下午茶"},
                    {"time": "18:00", "activity": "晚餐"},
                    {"time": "21:00", "activity": "睡前阅读"}
                ]
            }

            self._save_schedule(fallback)
            return True

        except Exception as e:
            log(f"创建应急日程失败: {str(e)}", "ERROR")
            return False

    def _save_schedule(self, schedule_data: Dict):
        """保存日程到文件"""
        try:
            file_path = os.path.join(self.schedule_dir, f"{schedule_data['date']}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(schedule_data, f, ensure_ascii=False, indent=2)
            log(f"日程已保存: {file_path}")

        except Exception as e:
            log(f"保存日程失败: {str(e)}", "ERROR")
            raise

    def get_today_schedule(self) -> Optional[Dict]:
        """获取当日日程"""
        try:
            today = datetime.now(self.timezone).strftime("%Y-%m-%d")
            file_path = os.path.join(self.schedule_dir, f"{today}.json")

            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                log(f"今日日程不存在: {today}")
                return None

        except Exception as e:
            log(f"获取今日日程失败: {str(e)}", "ERROR")
            return None

    def get_schedule_summary(self) -> str:
        """获取今日日程摘要（用于附加到消息中）"""
        try:
            schedule = self.get_today_schedule()
            if not schedule:
                return ""

            # 简化格式：只保留时间和内容的数组，去掉字段名
            import json
            simplified_schedule = []
            for item in schedule['schedule']:
                simplified_schedule.append([item['time'], item['activity']])

            schedule_json = json.dumps(simplified_schedule, ensure_ascii=False, separators=(',', ':'))

            return f"[今日安排: {schedule_json}]"

        except Exception as e:
            log(f"获取日程摘要失败: {str(e)}", "ERROR")
            return ""

    def setup_schedule_tasks(self):
        """设置日程相关的定时任务"""
        try:
            if not self.scheduler:
                log("调度器未设置，无法设置日程任务", "ERROR")
                return

            # 每天晚上生成次日日程
            self.scheduler.add_job(
                func=self.generate_daily_schedule,
                trigger=CronTrigger(
                    hour=config.SCHEDULE_GENERATE_HOUR,
                    minute=config.SCHEDULE_GENERATE_MINUTE
                ),
                kwargs={'date_offset': 1},
                id='generate_daily_schedule',
                name='生成每日日程',
                replace_existing=True
            )

            # 每天凌晨设置当日提醒
            self.scheduler.add_job(
                func=self.setup_daily_reminders,
                trigger=CronTrigger(hour=0, minute=10),
                id='setup_daily_reminders',
                name='设置每日提醒',
                replace_existing=True
            )

            # 启动时立即检查并生成今日日程
            today_schedule = self.get_today_schedule()
            if not today_schedule:
                log("今日日程不存在，立即生成")
                self.generate_daily_schedule(date_offset=0)

            # 设置今日的提醒任务
            self.setup_daily_reminders()

            log("日程定时任务已设置")

        except Exception as e:
            log(f"设置日程定时任务失败: {str(e)}", "ERROR")

    def setup_daily_reminders(self):
        """设置当日的提醒任务"""
        try:
            schedule = self.get_today_schedule()
            if not schedule:
                log("今日日程不存在，无法设置提醒")
                return

            # 获取当前时间
            now = datetime.now(self.timezone)
            today = now.date()

            for item in schedule['schedule']:
                try:
                    # 解析时间
                    activity_time = datetime.strptime(item['time'], "%H:%M").time()
                    activity_dt = datetime.combine(today, activity_time)
                    activity_dt = activity_dt.replace(tzinfo=self.timezone)

                    # 计算提醒时间（提前多少分钟）
                    remind_time = activity_dt - timedelta(minutes=config.SCHEDULE_REMINDER_ADVANCE)

                    # 只安排未来的提醒
                    if remind_time > now:
                        # 安排定时发送
                        self.scheduler.add_job(
                            func=self.send_schedule_reminder,
                            trigger='date',
                            run_date=remind_time,
                            args=[item],
                            id=f"schedule_reminder_{item['time']}",
                            name=f"日程提醒_{item['time']}",
                            replace_existing=True
                        )
                        log(f"已安排日程提醒：{item['time']} - {item['activity']}")

                except Exception as item_e:
                    log(f"设置单个提醒失败 {item}: {str(item_e)}", "ERROR")
                    continue

        except Exception as e:
            log(f"设置每日提醒失败: {str(e)}", "ERROR")

    def send_schedule_reminder(self, schedule_item: Dict):
        """发送日程提醒"""
        try:
            if not self.bot:
                log("机器人实例未设置，无法发送提醒", "ERROR")
                return

            # 生成提醒内容
            reminder_text = f"⏰ {schedule_item['time']} 现在开始：{schedule_item['activity']}"

            # 使用AI生成温馨提醒
            ai_reminder = self._generate_ai_reminder(reminder_text)

            # 随机选择发送目标
            target = self._get_random_target()
            if not target:
                log("没有可用的发送目标", "WARNING")
                return

            # 发送提醒
            self._send_reminder_to_target(ai_reminder, target)

        except Exception as e:
            log(f"发送日程提醒失败: {str(e)}", "ERROR")

    def _generate_ai_reminder(self, reminder_text: str) -> str:
        """使用AI生成温馨提醒"""
        try:
            if not self.bot or not hasattr(self.bot, 'api_client') or not self.bot.api_client:
                return reminder_text

            prompt = f"请你用可爱温馨的语气提醒用户：{reminder_text}。要求简短亲切，不超过20字。"

            # 使用机器人的完整提示词构建系统消息
            system_prompt = getattr(self.bot, 'prompt_content', "你是林小葵，一个可爱贴心的AI助手。")
            system_prompt += "\n\n[特殊任务]: 请用温馨的语气生成提醒消息。要保持林小葵的人格特征和语言风格。"

            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]

            response = self.bot.api_client.chat.completions.create(
                model=self.bot.model,
                messages=messages,
                max_tokens=100,
                temperature=0.8
            )

            ai_reminder = response.choices[0].message.content.strip()

            # 清理AI生成的内容
            ai_reminder = ai_reminder.replace('"', '').replace("'", "")
            if ai_reminder.startswith('林小葵：') or ai_reminder.startswith('林小葵:'):
                ai_reminder = ai_reminder.split('：', 1)[-1].split(':', 1)[-1].strip()

            return ai_reminder

        except Exception as e:
            log(f"AI生成提醒失败: {str(e)}", "ERROR")
            return reminder_text

    def _get_random_target(self) -> Optional[Dict[str, str]]:
        """随机获取发送目标（监听用户、管理员、群聊）"""
        try:
            targets = []

            # 添加监听用户
            for user in config.LISTEN_LIST:
                targets.append({"type": "user", "name": user})

            # 添加管理员
            for admin in config.ADMIN_LIST:
                if admin not in config.LISTEN_LIST:  # 避免重复
                    targets.append({"type": "admin", "name": admin})

            # 添加群聊
            if config.GROUP_SWITCH:
                for group in config.GROUP_LIST:
                    targets.append({"type": "group", "name": group})

            if targets:
                return random.choice(targets)
            else:
                return None

        except Exception as e:
            log(f"获取随机目标失败: {str(e)}", "ERROR")
            return None

    def _send_reminder_to_target(self, message: str, target: Dict[str, str]):
        """向指定目标发送提醒"""
        try:
            if not self.bot or not hasattr(self.bot, 'wx'):
                log("微信客户端未初始化", "ERROR")
                return

            # 尝试切换到目标聊天窗口
            try:
                chat_result = self.bot.wx.ChatWith(target['name'])
                if not chat_result:
                    log(f"无法找到聊天窗口: {target['name']}", "WARNING")
                    return
            except Exception as switch_e:
                log(f"切换到 {target['name']} 聊天窗口失败: {str(switch_e)}", "WARNING")
                return

            # 处理消息文本：移除括号内容、格式化并分割句子
            from utils.text_processor import remove_brackets_content
            cleaned_message = remove_brackets_content(message)
            formatted_message = format_response(cleaned_message)
            sentences = split_sentences(formatted_message)

            if not sentences:
                sentences = [message]

            # 逐句发送消息
            sent_messages = []
            for i, sentence in enumerate(sentences):
                if not sentence.strip():
                    continue

                # 添加2秒延迟，模拟人类发送
                # 使用统一的延迟函数
                apply_message_delay(i, len(sentences))

                # 发送消息
                result = self.bot.wx.SendMsg(msg=sentence.strip(), who=target['name'], clear=True)

                if result:
                    log(f"日程提醒发送成功 -> {target['type']}:{target['name']}: {sentence[:30]}...")
                    sent_messages.append(sentence.strip())
                else:
                    log(f"日程提醒发送失败 -> {target['type']}:{target['name']}: {sentence[:30]}...", "ERROR")

            # 保存发送的消息到记忆系统
            if sent_messages and hasattr(self.bot, 'memory_manager'):
                try:
                    # 合并所有发送的句子
                    full_message = " ".join(sent_messages)

                    # 判断是否为群聊
                    is_group = target['type'] == 'group'

                    # 1. 保存AI发送的消息到chat_messages表
                    self.bot.memory_manager.save_ai_message(
                        sender="林小葵",  # AI的名字
                        message=full_message,
                        chat_name=target['name'],
                        is_group=is_group
                    )

                    # 2. 构建对话格式并保存到Memory表的history字段
                    conversation = [
                        {"role": "assistant", "content": full_message}
                    ]

                    # 构建记忆键
                    if is_group:
                        memory_key = f"group_{target['name']}"
                    else:
                        memory_key = f"user_{target['name']}"

                    # 保存到Memory表，让AI能够获取
                    self.bot.memory_manager.save_memory(memory_key, conversation)

                    log(f"日程提醒消息已保存到记忆系统: {target['name']}")

                except Exception as memory_e:
                    log(f"保存日程提醒消息到记忆系统失败: {str(memory_e)}", "ERROR")

        except Exception as e:
            log(f"发送提醒到目标失败: {str(e)}", "ERROR")
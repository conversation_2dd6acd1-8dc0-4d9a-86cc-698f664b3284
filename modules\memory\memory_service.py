import os
import json
import logging
import glob
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from src.services.ai.llm_service import LLMService

# 获取日志记录器
logger = logging.getLogger('memory')

# APScheduler导入（带异常处理）
try:
    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.triggers.cron import CronTrigger
    import atexit
    SCHEDULER_AVAILABLE = True
except ImportError:
    logger.warning("APScheduler未安装，传统系统定时任务功能将被禁用")
    SCHEDULER_AVAILABLE = False
    BackgroundScheduler = None
    CronTrigger = None
    atexit = None

class MemoryService:
    """
    新版记忆服务模块，包含两种记忆类型:
    1. 短期记忆：用于保存最近对话，在程序重启后加载到上下文
    2. 核心记忆：精简的用户核心信息摘要(50-100字)
    每个用户拥有独立的记忆存储空间
    """
    def __init__(self, root_dir: str, api_key: str, base_url: str, model: str, max_token: int, temperature: float, max_groups: int = 10):
        self.root_dir = root_dir
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.max_token = max_token
        self.temperature = temperature
        self.max_groups = max_groups  # 保存上下文组数设置
        self.llm_client = None
        self.conversation_count = {}  # 记录每个角色与用户组合的对话计数: {avatar_name_user_id: count}
        self.deepseek = LLMService(
            api_key=api_key,
            base_url=base_url,
            model=model,
            max_token=max_token,
            temperature=temperature,
            max_groups=max_groups
        )
        
        # 初始化定时任务调度器（传统系统也支持）
        if SCHEDULER_AVAILABLE and BackgroundScheduler is not None:
            self.scheduler = BackgroundScheduler()
            self._setup_daily_summary_task()
            self.scheduler.start()
            
            # 注册关闭时的清理函数
            if atexit is not None:
                atexit.register(self._cleanup_scheduler)
        else:
            self.scheduler = None
            logger.warning("传统系统定时任务功能不可用")

    def initialize_memory_files(self, avatar_name: str, user_id: str):
        """初始化角色的记忆文件，确保文件存在"""
        try:
            # 确保记忆目录存在
            memory_dir = self._get_avatar_memory_dir(avatar_name, user_id)
            short_memory_path = self._get_short_memory_path(avatar_name, user_id)
            core_memory_path = self._get_core_memory_path(avatar_name, user_id)
            
            # 初始化短期记忆文件（如果不存在）
            if not os.path.exists(short_memory_path):
                with open(short_memory_path, "w", encoding="utf-8") as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                logger.info(f"创建短期记忆文件: {short_memory_path}")
            
            # 初始化核心记忆文件（如果不存在）
            if not os.path.exists(core_memory_path):
                initial_core_data = {
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "content": ""  # 初始为空字符串
                }
                with open(core_memory_path, "w", encoding="utf-8") as f:
                    json.dump(initial_core_data, f, ensure_ascii=False, indent=2)
                logger.info(f"创建核心记忆文件: {core_memory_path}")
        
        except Exception as e:
            logger.error(f"初始化记忆文件失败: {str(e)}")

    def _get_llm_client(self):
        """获取或创建LLM客户端"""
        if not self.llm_client:
            self.llm_client = LLMService(
                api_key=self.api_key,
                base_url=self.base_url,
                model=self.model,
                max_token=self.max_token,
                temperature=self.temperature,
                max_groups=self.max_groups  # 使用初始化时传入的值
            )
            logger.info(f"创建LLM客户端，上下文大小设置为: {self.max_groups}轮对话")
        return self.llm_client
        
    def _get_avatar_memory_dir(self, avatar_name: str, user_id: str) -> str:
        """获取角色记忆目录，如果不存在则创建"""
        avatar_memory_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "oldmemory", user_id)
        os.makedirs(avatar_memory_dir, exist_ok=True)
        return avatar_memory_dir
    
    def _get_short_memory_path(self, avatar_name: str, user_id: str) -> str:
        """获取短期记忆文件路径"""
        memory_dir = self._get_avatar_memory_dir(avatar_name, user_id)
        return os.path.join(memory_dir, "short_memory.json")
    
    def _get_core_memory_path(self, avatar_name: str, user_id: str) -> str:
        """获取核心记忆文件路径"""
        memory_dir = self._get_avatar_memory_dir(avatar_name, user_id)
        return os.path.join(memory_dir, "core_memory.json")
    
    def _get_core_memory_backup_path(self, avatar_name: str, user_id: str) -> str:
        """获取核心记忆备份文件路径"""
        memory_dir = self._get_avatar_memory_dir(avatar_name, user_id)
        backup_dir = os.path.join(memory_dir, "backup")
        os.makedirs(backup_dir, exist_ok=True)
        return os.path.join(backup_dir, "core_memory_backup.json")
    
    def add_conversation(self, avatar_name: str, user_message: str, bot_reply: str, user_id: str, is_system_message: bool = False):
        """
        添加对话到短期记忆，并更新对话计数。
        每达到10轮对话，自动更新核心记忆。
        
        Args:
            avatar_name: 角色名称
            user_message: 用户消息
            bot_reply: 机器人回复
            user_id: 用户ID，用于隔离不同用户的记忆
            is_system_message: 是否为系统消息，如果是则不记录
        """
        # 确保对话计数器已初始化
        conversation_key = f"{avatar_name}_{user_id}"
        if conversation_key not in self.conversation_count:
            self.conversation_count[conversation_key] = 0
            
        # 如果是系统消息或错误消息则跳过记录
        if is_system_message or bot_reply.startswith("Error:"):
            logger.debug(f"跳过记录消息: {user_message[:30]}...")
            return
            
        try:
            # 确保记忆目录存在
            memory_dir = self._get_avatar_memory_dir(avatar_name, user_id)
            short_memory_path = self._get_short_memory_path(avatar_name, user_id)
            
            logger.info(f"保存对话到用户记忆: 角色={avatar_name}, 用户ID={user_id}")
            logger.debug(f"记忆存储路径: {short_memory_path}")
            
            # 读取现有短期记忆
            short_memory = []
            if os.path.exists(short_memory_path):
                try:
                    with open(short_memory_path, "r", encoding="utf-8") as f:
                        short_memory = json.load(f)
                except json.JSONDecodeError:
                    logger.warning(f"短期记忆文件损坏，重置为空列表: {short_memory_path}")
            
            # 添加新对话（新增日期信息）
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            date_key = datetime.now().strftime("%Y-%m-%d")  # 日期键
            new_conversation = {
                "timestamp": timestamp,
                "date_key": date_key,  # 新增日期字段
                "user": user_message,
                "bot": bot_reply
            }
            short_memory.append(new_conversation)
            
            # 保留当天所有对话，但限制总数为200条以防止文件过大
            if len(short_memory) > 200:
                # 保留最近200条记录
                short_memory = short_memory[-200:]
            
            # 保存更新后的短期记忆
            with open(short_memory_path, "w", encoding="utf-8") as f:
                json.dump(short_memory, f, ensure_ascii=False, indent=2)
            
            # 更新对话计数
            self.conversation_count[conversation_key] += 1
            current_count = self.conversation_count[conversation_key]
            logger.debug(f"当前对话计数: {current_count}/10 (角色={avatar_name}, 用户ID={user_id})")
            
            # 每10轮对话更新一次核心记忆
            if self.conversation_count[conversation_key] >= 10:
                logger.info(f"角色 {avatar_name} 为用户 {user_id} 达到10轮对话，开始更新核心记忆")
                context = self.get_recent_context(avatar_name, user_id)
                self.update_core_memory(avatar_name, user_id, context)
                self.conversation_count[conversation_key] = 0
                
        except Exception as e:
            logger.error(f"添加对话到短期记忆失败: {str(e)}")
    
    def _build_memory_prompt(self, filepath: str) -> str:
        """
        从指定目录读取 md 文件并只替换时间变量。

        Args:
            filepath: md 文件的路径。

        Returns:
            一个包含 md 文件内容的字符串，其中只有时间变量已被替换。
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                prompt_template = f.read()

            # 获取当前时间信息
            now = datetime.now()
            current_date = now.strftime("%m月%d日")  # 简短格式：8月16日
            current_time = now.strftime("%H:%M")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(now.strftime("%A"), now.strftime("%A"))

            # 只替换时间变量，保留其他占位符供后续使用
            try:
                # 创建一个只包含时间变量的替换字典
                time_vars = {
                    'current_date': current_date,
                    'current_time': current_time,
                    'weekday': weekday
                }
                
                # 使用字符串替换而不是format，避免其他占位符报错
                prompt = prompt_template
                for var_name, var_value in time_vars.items():
                    prompt = prompt.replace(f'{{{var_name}}}', str(var_value))
                    
            except Exception as e:
                logger.warning(f"时间变量替换失败: {e}，使用原始模板")
                prompt = prompt_template

            return prompt
        except FileNotFoundError:
            logger.error(f"核心记忆提示词模板 {filepath} 未找到。")
            return ""
        except UnicodeDecodeError:
            logger.error(f"记忆提示词文件编码错误: {filepath}")
            return ""
        except Exception as e:
            logger.error(f"读取核心提示词模板 {filepath} 时出错: {e}")
            return ""

    def _generate_core_memory(self, prompt: str, existing_core_memory: str, context: list, user_id: str) -> str:
        """生成核心记忆"""
        try:
            # 格式化对话上下文
            formatted_context = self._format_context_for_memory(context)
            
            # 替换提示词模板中的占位符
            try:
                # 获取中文星期几
                weekday_map = {
                    'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                    'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
                }
                chinese_weekday = weekday_map.get(datetime.now().strftime("%A"), datetime.now().strftime("%A"))
                
                formatted_prompt = prompt.format(
                    existing_core_memory=existing_core_memory,
                    context=formatted_context,
                    current_date=datetime.now().strftime("%m月%d日"),
                    current_time=datetime.now().strftime("%H:%M"),
                    weekday=chinese_weekday
                )
            except Exception as format_error:
                logger.warning(f"提示词模板格式化失败: {format_error}，使用原始模板")
                formatted_prompt = prompt
            
            # 构建用户消息
            user_message = f"""请根据现有核心记忆和对话上下文，生成新的核心记忆。

现有核心记忆：
{existing_core_memory}

对话上下文：
{formatted_context}

请严格按照提示词要求生成新的核心记忆，字数控制在50-100字内。"""

            response = self.deepseek.get_response(
                message=user_message,
                user_id=user_id,
                system_prompt=formatted_prompt,
                core_memory=existing_core_memory,
                previous_context=context
            )
            
            return response
            
        except Exception as e:
            logger.error(f"生成核心记忆失败: {e}")
            return ""

    
    def _format_context_for_memory(self, context: list) -> str:
        """
        格式化对话上下文，便于记忆生成

        Args:
            context: 对话上下文列表

        Returns:
            str: 格式化后的对话内容
        """
        if not context:
            return "无对话上下文"

        formatted_lines = []
        for i in range(0, len(context), 2):
            if i + 1 < len(context):
                user_msg = context[i].get("content", "")
                bot_msg = context[i + 1].get("content", "")
                formatted_lines.append(f"我：{bot_msg}")
                formatted_lines.append(f"用户：{user_msg}")
                formatted_lines.append("---")

        return "\n".join(formatted_lines)

    def update_core_memory(self, avatar_name: str, user_id: str, context: list) -> bool:
        """
        更新角色的核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            message: 用户消息
            response: 机器人响应
        
        Returns:
            bool: 是否成功更新
        """
        # 初始化变量以避免作用域问题
        core_memory_path = ""
        existing_core_data = None
        
        try:
            # 获取核心记忆文件路径
            core_memory_path = self._get_core_memory_path(avatar_name, user_id)
            
            # 读取现有核心记忆
            existing_core_memory = ""
            existing_core_data = None
            
            if os.path.exists(core_memory_path):
                try:
                    with open(core_memory_path, "r", encoding="utf-8") as f:
                        core_data = json.load(f)
                        # 处理数组格式（旧格式）
                        if isinstance(core_data, list) and len(core_data) > 0:
                            if isinstance(core_data[0], dict):
                                existing_core_memory = core_data[0].get("content", "")
                        else:
                            # 新格式（单个对象）
                            if isinstance(core_data, dict):
                                existing_core_memory = core_data.get("content", "")
                        existing_core_data = core_data
                except Exception as e:
                    logger.error(f"读取核心记忆失败: {str(e)}")
                    # 创建空的核心记忆
                    existing_core_memory = ""
                    existing_core_data = None
            
            # 如果没有现有记忆，创建一个空的对象（新格式）
            if not existing_core_data:
                existing_core_data = {
                    "timestamp": self._get_timestamp(),
                    "content": ""
                }
            
            # 构建提示词
            prompt = self._build_memory_prompt('data/base/memory.md')

            # 调用LLM生成新的核心记忆
            new_core_memory = self._generate_core_memory(prompt, existing_core_memory, context, user_id)
            
            # 如果生成失败，保留原有记忆
            if not new_core_memory or 'Error' in new_core_memory or 'error' in new_core_memory or '错误' in new_core_memory:
                logger.warning("生成核心记忆失败，保留原有记忆")
                return False
            
                    
            # 更新核心记忆文件（使用新格式：单个对象）
            updated_core_data = {
                "timestamp": self._get_timestamp(),
                "content": new_core_memory
            }
            
            with open(core_memory_path, "w", encoding="utf-8") as f:
                json.dump(updated_core_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已更新角色 {avatar_name} 用户 {user_id} 的核心记忆")
            return True
            
        except Exception as e:
            logger.error(f"更新核心记忆失败: {str(e)}")
            
            # 如果在处理过程中发生错误，确保不会丢失现有记忆
            try:
                if os.path.exists(core_memory_path) and existing_core_data:
                    with open(core_memory_path, "w", encoding="utf-8") as f:
                        json.dump(existing_core_data, f, ensure_ascii=False, indent=2)
            except Exception as recovery_error:
                logger.error(f"恢复核心记忆失败: {str(recovery_error)}")
                
            return False

    
    def update_core_memory_content(self, avatar_name: str, user_id: str, memory_content: str) -> bool:
        """
        直接更新核心记忆内容
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            memory_content: 新的记忆内容
            
        Returns:
            bool: 更新是否成功
        """
        # 初始化变量以避免作用域问题
        core_memory_path = ""
        
        try:
            logger.info(f"旧记忆系统：直接更新核心记忆 - 角色: {avatar_name}, 用户: {user_id}")
            
            # 获取核心记忆文件路径
            core_memory_path = self._get_core_memory_path(avatar_name, user_id)
            
            # 更新核心记忆文件（使用新格式：单个对象）
            updated_core_data = {
                "timestamp": self._get_timestamp(),
                "content": memory_content
            }
            
            with open(core_memory_path, "w", encoding="utf-8") as f:
                json.dump(updated_core_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已直接更新角色 {avatar_name} 用户 {user_id} 的核心记忆")
            logger.info(f"更新后记忆: {memory_content}")
            
            return True
            
        except Exception as e:
            logger.error(f"直接更新核心记忆失败: {str(e)}")
            return False

    def correct_memory(self, avatar_name: str, user_id: str, wrong_memory: str, correct_memory: str) -> bool:
        """
        修正核心记忆中的错误信息
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            wrong_memory: 错误的记忆内容
            correct_memory: 正确的记忆内容
            
        Returns:
            bool: 修正是否成功
        """
        # 初始化变量以避免作用域问题
        core_memory_path = ""
        existing_core_data = None
        
        try:
            logger.info(f"旧记忆系统：开始修正记忆 - 角色: {avatar_name}, 用户: {user_id}")
            logger.info(f"错误记忆: {wrong_memory}")
            logger.info(f"正确记忆: {correct_memory}")
            
            # 获取核心记忆文件路径
            core_memory_path = self._get_core_memory_path(avatar_name, user_id)
            
            # 读取现有核心记忆
            existing_core_memory = ""
            existing_core_data = None
            
            if os.path.exists(core_memory_path):
                try:
                    with open(core_memory_path, "r", encoding="utf-8") as f:
                        existing_core_data = json.load(f)
                        
                    # 处理不同格式的核心记忆
                    if isinstance(existing_core_data, list) and len(existing_core_data) > 0:
                        # 数组格式（旧格式）
                        existing_core_memory = existing_core_data[0].get("content", "")
                    elif isinstance(existing_core_data, dict):
                        # 对象格式（新格式）
                        existing_core_memory = existing_core_data.get("content", "")
                except Exception as e:
                    logger.error(f"读取核心记忆文件失败: {str(e)}")
                    return False
            
            if not existing_core_memory:
                logger.warning(f"未找到用户 {user_id} 的核心记忆")
                return False
            
            # 检查错误记忆是否存在于现有记忆中
            if wrong_memory not in existing_core_memory:
                logger.warning(f"错误记忆 '{wrong_memory}' 未在现有核心记忆中找到")
                logger.info(f"现有核心记忆: {existing_core_memory}")
                return False
            
            # 替换错误记忆为正确记忆
            new_core_memory = existing_core_memory.replace(wrong_memory, correct_memory)
            
            # 更新核心记忆文件（使用新格式：单个对象）
            updated_core_data = {
                "timestamp": self._get_timestamp(),
                "content": new_core_memory
            }
            
            with open(core_memory_path, "w", encoding="utf-8") as f:
                json.dump(updated_core_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已修正角色 {avatar_name} 用户 {user_id} 的核心记忆")
            logger.info(f"修正后记忆: {new_core_memory}")
            return True
            
        except Exception as e:
            logger.error(f"修正核心记忆失败: {str(e)}")
            
            # 如果在处理过程中发生错误，确保不会丢失现有记忆
            try:
                if os.path.exists(core_memory_path) and existing_core_data:
                    with open(core_memory_path, "w", encoding="utf-8") as f:
                        json.dump(existing_core_data, f, ensure_ascii=False, indent=2)
            except Exception as recovery_error:
                logger.error(f"恢复核心记忆失败: {str(recovery_error)}")
                
            return False

    def get_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        获取角色的核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
        
        Returns:
            str: 核心记忆内容
        """
        try:
            # 获取核心记忆文件路径
            core_memory_path = self._get_core_memory_path(avatar_name, user_id)
            
            # 如果文件不存在，返回空字符串
            if not os.path.exists(core_memory_path):
                return ""
            
            # 读取核心记忆文件
            with open(core_memory_path, "r", encoding="utf-8") as f:
                core_data = json.load(f)
                
                # 处理数组格式
                if isinstance(core_data, list) and len(core_data) > 0:
                    if isinstance(core_data[0], dict):
                        return core_data[0].get("content", "")
                    return ""  # 列表不为空但首个元素不是dict
                else:
                    # 兼容旧格式
                    if isinstance(core_data, dict):
                        return core_data.get("content", "")
                    return ""  # 不是列表也不是dict
        except Exception as e:
            logger.error(f"获取核心记忆失败: {str(e)}")
            return ""
    
    def get_recent_context(self, avatar_name: str, user_id: str, context_size: Optional[int] = None) -> List[Dict]:
        """
        获取最近的对话上下文，用于重启后恢复对话连续性
        直接使用LLM服务配置的max_groups作为上下文大小
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID，用于获取特定用户的记忆
            context_size: 已废弃参数，保留仅为兼容性，实际使用LLM配置
        """
        try:
            # 获取LLM客户端的配置值
            llm_client = self._get_llm_client()
            max_groups = llm_client.config["max_groups"]
            logger.info(f"使用LLM配置的对话轮数: {max_groups}")
            
            short_memory_path = self._get_short_memory_path(avatar_name, user_id)
            
            if not os.path.exists(short_memory_path):
                logger.info(f"短期记忆不存在: {avatar_name} 用户: {user_id}")
                return []
            
            with open(short_memory_path, "r", encoding="utf-8") as f:
                short_memory = json.load(f)
            
            # 转换为LLM接口要求的消息格式
            context = []
            for conv in short_memory[-max_groups:]:  # 使用max_groups轮对话
                context.append({"role": "user", "content": conv["user"]})
                context.append({"role": "assistant", "content": conv["bot"]})
            
            logger.info(f"已加载 {len(context)//2} 轮对话作为上下文")
            return context
            
        except Exception as e:
            logger.error(f"获取最近上下文失败: {str(e)}")
            return []

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def has_user_memory(self, avatar_name: str, user_id: str) -> bool:
        """
        检查是否存在该用户的私聊记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            
        Returns:
            bool: 如果存在私聊记忆返回True，否则返回False
        """
        try:
            # 检查短期记忆是否存在且非空
            short_memory_path = self._get_short_memory_path(avatar_name, user_id)
            if os.path.exists(short_memory_path):
                with open(short_memory_path, "r", encoding="utf-8") as f:
                    short_memory = json.load(f)
                    if short_memory:  # 如果列表不为空
                        logger.debug(f"用户 {user_id} 与角色 {avatar_name} 有私聊记忆，条数: {len(short_memory)}")
                        return True
            
            # 检查核心记忆是否存在且非空
            core_memory_path = self._get_core_memory_path(avatar_name, user_id)
            if os.path.exists(core_memory_path):
                with open(core_memory_path, "r", encoding="utf-8") as f:
                    core_memory = json.load(f)
                    # 处理数组格式（旧格式）
                    if isinstance(core_memory, list) and len(core_memory) > 0:
                        if isinstance(core_memory[0], dict) and core_memory[0].get("content", "").strip():  # 如果内容不为空
                            logger.debug(f"用户 {user_id} 与角色 {avatar_name} 有核心记忆")
                            return True
                    else:
                        # 新格式（单个对象）
                        if isinstance(core_memory, dict) and core_memory.get("content", "").strip():  # 如枟内容不为空
                            logger.debug(f"用户 {user_id} 与角色 {avatar_name} 有核心记忆")
                            return True
            
            logger.debug(f"用户 {user_id} 与角色 {avatar_name} 没有私聊记忆")
            return False
            
        except Exception as e:
            logger.error(f"检查用户记忆失败: {str(e)}")
            return False 

    def _setup_daily_summary_task(self):
        """设置每日总结任务（传统系统版本）"""
        try:
            if not SCHEDULER_AVAILABLE or CronTrigger is None or self.scheduler is None:
                logger.warning("无法设置传统系统定时任务：APScheduler不可用")
                return
                
            # 每晚12点触发总结
            trigger = CronTrigger(hour=0, minute=0, second=0)
            self.scheduler.add_job(
                func=self._daily_summary_job,
                trigger=trigger,
                id='daily_memory_summary_traditional',
                name='传统系统每日记忆总结任务',
                replace_existing=True
            )
            logger.info("传统记忆系统每日总结任务已设置")
            
        except Exception as e:
            logger.error(f"设置传统系统每日总结任务失败: {e}")

    def _daily_summary_job(self):
        """传统系统的每日总结任务"""
        try:
            logger.info("开始执行传统系统每日记忆总结任务...")
            
            # 获取所有角色
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")
            if not os.path.exists(avatars_dir):
                return
            
            summary_count = 0
            for avatar_name in os.listdir(avatars_dir):
                avatar_path = os.path.join(avatars_dir, avatar_name)
                if os.path.isdir(avatar_path):
                    oldmemory_dir = os.path.join(avatar_path, "oldmemory")
                    if os.path.exists(oldmemory_dir):
                        count = self._process_avatar_daily_summary_traditional(avatar_name)
                        summary_count += count
            
            logger.info(f"传统系统每日记忆总结任务完成，处理了 {summary_count} 个用户的记忆")
            
        except Exception as e:
            logger.error(f"执行传统系统每日记忆总结任务失败: {e}")

    def _process_avatar_daily_summary_traditional(self, avatar_name: str) -> int:
        """处理指定角色的每日总结（传统系统版本）"""
        try:
            oldmemory_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "oldmemory")
            if not os.path.exists(oldmemory_dir):
                return 0
            
            summary_count = 0
            today = datetime.now().strftime("%Y-%m-%d")
            
            # 遍历所有用户目录
            for user_id in os.listdir(oldmemory_dir):
                user_dir = os.path.join(oldmemory_dir, user_id)
                if os.path.isdir(user_dir):
                    if self._generate_daily_summary_traditional(avatar_name, user_id, today):
                        summary_count += 1
            
            return summary_count
            
        except Exception as e:
            logger.error(f"处理传统系统角色 {avatar_name} 每日总结失败: {e}")
            return 0

    def _generate_daily_summary_traditional(self, avatar_name: str, user_id: str, date_key: str) -> bool:
        """为指定用户生成当天的详细核心记忆总结（传统系统版本）"""
        try:
            # 获取当天的所有对话
            daily_conversations = self._get_daily_conversations_traditional(avatar_name, user_id, date_key)
            
            if not daily_conversations:
                return False
            
            # 获取现有核心记忆
            existing_memory = self.get_core_memory(avatar_name, user_id)
            
            # 生成新的详细核心记忆
            new_memory = self._generate_detailed_core_memory_traditional(
                existing_memory, daily_conversations, user_id, date_key
            )
            
            if not new_memory or 'Error' in new_memory:
                return False
            
            # 保存到文件
            if self._save_detailed_core_memory_traditional(avatar_name, user_id, new_memory):
                logger.info(f"传统系统用户 {user_id} 在 {date_key} 的详细记忆总结完成")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"传统系统生成用户 {user_id} 每日总结失败: {e}")
            return False

    def _get_daily_conversations_traditional(self, avatar_name: str, user_id: str, date_key: str) -> List[Dict]:
        """获取指定日期的所有对话（传统系统版本）"""
        try:
            short_memory_path = self._get_short_memory_path(avatar_name, user_id)
            if not os.path.exists(short_memory_path):
                return []
            
            with open(short_memory_path, "r", encoding="utf-8") as f:
                short_memory = json.load(f)
            
            # 筛选当天的对话
            daily_conversations = []
            for conv in short_memory:
                # 检查是否有 date_key 字段
                if 'date_key' in conv and conv['date_key'] == date_key:
                    daily_conversations.extend([
                        {"role": "user", "content": conv["user"], "timestamp": conv["timestamp"]},
                        {"role": "assistant", "content": conv["bot"], "timestamp": conv["timestamp"]}
                    ])
                elif 'timestamp' in conv:
                    # 兼容旧格式，从 timestamp 中提取日期
                    conv_date = conv['timestamp'].split(' ')[0] if ' ' in conv['timestamp'] else conv['timestamp'][:10]
                    if conv_date == date_key:
                        daily_conversations.extend([
                            {"role": "user", "content": conv["user"], "timestamp": conv["timestamp"]},
                            {"role": "assistant", "content": conv["bot"], "timestamp": conv["timestamp"]}
                        ])
            
            return daily_conversations
            
        except Exception as e:
            logger.error(f"传统系统获取每日对话失败: {e}")
            return []

    def _generate_detailed_core_memory_traditional(self, existing_memory: str, daily_conversations: List[Dict], user_id: str, date_key: str) -> str:
        """生成详细的核心记忆（传统系统版本）"""
        try:
            # 构建提示词
            prompt = self._build_memory_prompt('data/base/memory.md')
            
            # 构建详细消息
            parsed_date = datetime.strptime(date_key, "%Y-%m-%d")
            formatted_date = parsed_date.strftime("%m月%d日")
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            weekday = weekday_map.get(parsed_date.strftime('%A'), parsed_date.strftime('%A'))
            
            detailed_message = f"""请基于 {formatted_date} {weekday} 的全天对话，在现有核心记忆的基础上生成更加详细的新核心记忆。

现有核心记忆：
{existing_memory}

{formatted_date} {weekday} 的完整对话记录：
{self._format_daily_conversations_for_summary_traditional(daily_conversations)}

要求：
1. 保留原有核心记忆中的重要信息
2. 添加今天对话中的重要细节和新信息
3. 整理和扩展用户个性、喜好、关系发展等信息
4. 记录具体日期和重要事件（禁止使用相对时间词）
5. 字数控制在50-100字内，保持核心记忆的精炼性
6. 使用第一人称视角，仅保留关键信息"""
            
            response = self.deepseek.get_response(
                message=detailed_message,
                user_id=user_id,
                system_prompt=prompt + "特别指示：这是每日记忆模式，字数限制50-100字。",
                core_memory=existing_memory,
                previous_context=daily_conversations[-20:] if len(daily_conversations) > 20 else daily_conversations
            )
            
            return response
            
        except Exception as e:
            logger.error(f"传统系统生成详细核心记忆失败: {e}")
            return ""

    def _format_daily_conversations_for_summary_traditional(self, conversations: List[Dict]) -> str:
        """格式化全天对话供总结使用（传统系统版本）"""
        formatted_lines = []
        
        for i in range(0, len(conversations), 2):
            if i + 1 < len(conversations):
                user_msg = conversations[i].get('content', '')
                bot_msg = conversations[i + 1].get('content', '')
                timestamp = conversations[i].get('timestamp', '')
                
                time_str = timestamp.split(' ')[1] if ' ' in timestamp else timestamp
                
                formatted_lines.append(f"[{time_str}]")
                formatted_lines.append(f"我：{bot_msg}")
                formatted_lines.append(f"用户：{user_msg}")
                formatted_lines.append("---")
        
        return "\n".join(formatted_lines)

    def _save_detailed_core_memory_traditional(self, avatar_name: str, user_id: str, memory_content: str) -> bool:
        """保存详细核心记忆到文件（传统系统版本）"""
        try:
            core_memory_path = self._get_core_memory_path(avatar_name, user_id)
            
            # 更新核心记忆文件
            updated_core_data = {
                "timestamp": self._get_timestamp(),
                "content": memory_content
            }
            
            with open(core_memory_path, "w", encoding="utf-8") as f:
                json.dump(updated_core_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"传统系统保存详细核心记忆失败: {e}")
            return False

    def _cleanup_scheduler(self):
        """清理调度器资源（传统系统）"""
        try:
            if hasattr(self, 'scheduler') and self.scheduler:
                self.scheduler.shutdown(wait=False)
                logger.info("传统记忆系统调度器已关闭")
        except Exception as e:
            logger.error(f"传统系统关闭调度器失败: {e}")

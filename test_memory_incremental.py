#!/usr/bin/env python3
"""
模拟测试核心记忆增量更新功能
"""

import sys
import os
sys.path.append('F:/test/fork夜间 - q/Cherry Memory')

from modules.newmemory.database_memory_service import DatabaseMemoryService
import json

def load_config():
    """加载配置文件"""
    config_path = 'F:/test/fork夜间 - q/Cherry Memory/src/config/config.json'
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config

def main():
    """主测试函数"""
    print("=== 核心记忆增量更新模拟测试 ===\n")
    
    # 加载配置
    config = load_config()
    llm_config = config['categories']['llm_settings']['settings']
    
    print("配置信息:")
    print(f"API Key: {llm_config['api_key']['value'][:10]}...")
    print(f"Base URL: {llm_config['base_url']['value']}")
    print(f"Model: {llm_config['model']['value']}")
    print(f"Max Tokens: {llm_config['max_tokens']['value']}")
    print(f"Temperature: {llm_config['temperature']['value']}")
    print()
    
    # 创建记忆服务实例
    service = DatabaseMemoryService(
        root_dir='F:/test/fork夜间 - q/Cherry Memory',
        api_key=llm_config['api_key']['value'],
        base_url=llm_config['base_url']['value'],
        model=llm_config['model']['value'],
        max_token=llm_config['max_tokens']['value'],
        temperature=llm_config['temperature']['value']
    )
    
    # 模拟测试对话数据
    test_conversations = [
        {
            "role": "user",
            "content": "今天天气真好，我们一起去公园散步吧！"
        },
        {
            "role": "assistant", 
            "content": "好呀！今天阳光确实很明媚，我们去公园走走，顺便可以看看湖边的风景。你想几点去？"
        },
        {
            "role": "user",
            "content": "下午3点怎么样？我可以带点水果过去"
        },
        {
            "role": "assistant",
            "content": "下午3点正好，不会太热。你带水果的话，我来准备点饮料，我们可以野餐一下！"
        },
        {
            "role": "user",
            "content": "对了，我昨天看了一部很好看的电影，讲的是时光旅行的故事"
        },
        {
            "role": "assistant",
            "content": "哦？时光旅行题材的电影！我最近也想看类似的电影，叫什么名字呢？值得推荐吗？"
        },
        {
            "role": "user",
            "content": "叫《时空恋人》，剧情很感人，推荐你也看看"
        },
        {
            "role": "assistant",
            "content": "听起来不错，我记下了。等我们哪天有空可以一起看，时光旅行的故事总是很吸引人。"
        },
        {
            "role": "user",
            "content": "下周我生日，你有什么安排吗？"
        },
        {
            "role": "assistant",
            "content": "下周你生日！我已经在准备了，想给你一个惊喜。你想要什么礼物呢？"
        }
    ]
    
    print("模拟对话数据:")
    for i, msg in enumerate(test_conversations, 1):
        role = "用户" if msg['role'] == 'user' else "助手"
        print(f"{i}. {role}: {msg['content']}")
    print()
    
    # 检查当前记忆状态
    print("=== 当前记忆状态 ===")
    current_memory = service.get_core_memory('林绫-优化版', '1')
    print(f"当前记忆长度: {len(current_memory)} 字符")
    print(f"记忆内容预览: {current_memory[:200]}...")
    print()
    
    # 执行增量更新
    print("=== 开始增量更新测试 ===")
    result = service.update_core_memory('林绫-优化版', '1', test_conversations)
    print(f"更新结果: {result}")
    print()
    
    # 检查更新后的记忆
    print("=== 更新后的记忆状态 ===")
    updated_memory = service.get_core_memory('林绫-优化版', '1')
    print(f"更新后记忆长度: {len(updated_memory)} 字符")
    print(f"记忆内容预览: {updated_memory[:300]}...")
    print()
    
    # 显示变化
    if len(updated_memory) > len(current_memory):
        print("[成功] 记忆已成功增量更新！")
        print(f"增加了 {len(updated_memory) - len(current_memory)} 字符")
    elif len(updated_memory) == len(current_memory):
        print("[信息] 记忆没有变化，可能是因为没有新的重要信息")
    else:
        print("[警告] 记忆长度减少了，可能进行了优化压缩")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
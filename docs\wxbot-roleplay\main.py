#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信角色扮演机器人主程序入口
参考My-Dream-Moments-WeChat-wxauto项目的结构
"""

import sys
import signal
from bot import WXRolePlayBot
from utils.logger import setup_logger, log
import config


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
                         微信角色扮演机器人                          
                        WX RolePlay Bot v1.0                       
                                                                   
       基于 wxautox 库的简化版微信角色扮演机器人                      
       专注于接收消息并使用AI生成角色扮演回复                         
                                                                   
       使用前请确保：                                               
       1. 已安装 wxautox 库并获得有效许可证                          
       2. 微信已登录并保持在线                                       
       3. 已正确配置 config.py 文件                                 
                                                                 
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_config():
    """检查配置"""
    if config.API_KEY == 'your-api-key-here':
        print("❌ 请先配置API密钥")
        return False

    if not config.ADMIN_LIST:
        print("❌ 请先配置管理员列表")
        return False

    print("✅ 配置检查通过")
    return True


def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到中断信号，正在停止...")
    sys.exit(0)


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 显示横幅
    print_banner()

    # 设置日志
    setup_logger()

    # 检查配置
    if not check_config():
        return 1

    # 创建并运行机器人
    try:
        bot = WXRolePlayBot()
        bot.run()
        return 0

    except KeyboardInterrupt:
        log("用户中断，程序退出")
        return 0
    except Exception as e:
        log(f"程序异常退出: {str(e)}", "ERROR")
        return 1


if __name__ == "__main__":
    sys.exit(main())
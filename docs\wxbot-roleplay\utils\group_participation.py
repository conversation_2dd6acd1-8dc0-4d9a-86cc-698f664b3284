# -*- coding: utf-8 -*-
"""
群聊智能参与管理器
实现机器人在群聊中的智能插话功能，让机器人能够自然地参与对话而不是对每条消息都回复
"""

import time
import random
import re
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from utils.logger import log


class GroupParticipationManager:
    """群聊智能参与管理器"""
    
    def __init__(self):
        # 群聊状态跟踪
        self.group_states = {}  # 存储每个群的状态信息
        
        # 触发关键词配置
        self.trigger_keywords = {
            'high_priority': [  # 高优先级关键词（更容易触发回复）
                '小葵', '林小葵', '机器人', 'AI', '人工智能',
                '你好', '在吗', '怎么样', '什么', '为什么', '如何',
                '帮忙', '帮助', '问题', '求助'
            ],
            'medium_priority': [  # 中优先级关键词
                '天气', '时间', '今天', '明天', '最近',
                '开心', '难过', '生气', '累', '忙',
                '吃饭', '睡觉', '工作', '学习', '游戏'
            ],
            'low_priority': [  # 低优先级关键词
                '哈哈', '呵呵', '嗯', '好的', '是的', '不是',
                '早上', '中午', '晚上', '周末'
            ]
        }
        
        # 情感词汇
        self.emotion_keywords = {
            'positive': ['开心', '高兴', '快乐', '兴奋', '满意', '棒', '好', '赞'],
            'negative': ['难过', '伤心', '生气', '愤怒', '失望', '累', '烦', '糟糕'],
            'question': ['吗', '呢', '？', '?', '什么', '怎么', '为什么', '如何']
        }
    
    def get_group_state(self, group_id: str) -> Dict:
        """获取群聊状态"""
        if group_id not in self.group_states:
            self.group_states[group_id] = {
                'last_reply_time': 0,  # 上次回复时间
                'last_message_time': 0,  # 上次收到消息时间
                'message_count': 0,  # 消息计数
                'recent_messages': [],  # 最近的消息
                'active_users': set(),  # 活跃用户
                'conversation_topic': '',  # 对话主题
                'last_participation_score': 0  # 上次参与评分
            }
        return self.group_states[group_id]
    
    def update_group_state(self, group_id: str, sender: str, content: str):
        """更新群聊状态"""
        state = self.get_group_state(group_id)
        current_time = time.time()
        
        # 更新基本信息
        state['last_message_time'] = current_time
        state['message_count'] += 1
        state['active_users'].add(sender)
        
        # 维护最近消息列表（保留最近10条）
        state['recent_messages'].append({
            'sender': sender,
            'content': content,
            'time': current_time
        })
        if len(state['recent_messages']) > 10:
            state['recent_messages'].pop(0)
        
        # 清理过期的活跃用户（超过1小时未发言）
        if current_time - state['last_message_time'] > 3600:
            state['active_users'].clear()
    
    def analyze_message_relevance(self, content: str) -> Tuple[float, str]:
        """
        分析消息相关性
        返回: (相关性评分, 触发原因)
        """
        score = 0.0
        reasons = []
        
        # 检查高优先级关键词
        for keyword in self.trigger_keywords['high_priority']:
            if keyword in content:
                score += 0.8
                reasons.append(f"高优先级关键词: {keyword}")
        
        # 检查中优先级关键词
        for keyword in self.trigger_keywords['medium_priority']:
            if keyword in content:
                score += 0.5
                reasons.append(f"中优先级关键词: {keyword}")
        
        # 检查低优先级关键词
        for keyword in self.trigger_keywords['low_priority']:
            if keyword in content:
                score += 0.2
                reasons.append(f"低优先级关键词: {keyword}")
        
        # 检查情感表达
        for emotion_type, keywords in self.emotion_keywords.items():
            for keyword in keywords:
                if keyword in content:
                    if emotion_type == 'question':
                        score += 0.6
                        reasons.append("包含疑问")
                    elif emotion_type in ['positive', 'negative']:
                        score += 0.4
                        reasons.append(f"情感表达: {emotion_type}")
        
        # 检查是否为问句
        if any(char in content for char in ['？', '?', '吗', '呢']):
            score += 0.5
            reasons.append("疑问句")
        
        # 检查消息长度（较长的消息可能更有讨论价值）
        if len(content) > 20:
            score += 0.3
            reasons.append("较长消息")
        elif len(content) < 5:
            score -= 0.2
            reasons.append("过短消息")
        
        return min(score, 1.0), "; ".join(reasons)
    
    def calculate_conversation_activity(self, group_id: str) -> float:
        """计算对话活跃度"""
        state = self.get_group_state(group_id)
        current_time = time.time()
        
        # 检查最近5分钟内的消息数量
        recent_messages = [
            msg for msg in state['recent_messages']
            if current_time - msg['time'] < 300  # 5分钟
        ]
        
        # 活跃度基于消息数量和参与人数
        message_count = len(recent_messages)
        unique_senders = len(set(msg['sender'] for msg in recent_messages))
        
        # 计算活跃度评分
        activity_score = min((message_count * 0.1) + (unique_senders * 0.2), 1.0)
        
        return activity_score
    
    def check_cooldown(self, group_id: str, min_interval: int = 300) -> bool:
        """
        检查冷却时间
        min_interval: 最小间隔时间（秒），默认5分钟
        """
        state = self.get_group_state(group_id)
        current_time = time.time()
        
        return current_time - state['last_reply_time'] >= min_interval
    
    def should_participate(self, group_id: str, sender: str, content: str, 
                          config: object) -> Tuple[bool, str, float]:
        """
        判断是否应该参与对话
        返回: (是否参与, 原因, 参与评分)
        """
        # 更新群聊状态
        self.update_group_state(group_id, sender, content)
        
        # 获取配置参数
        participation_mode = getattr(config, 'GROUP_PARTICIPATION_MODE', 'smart')
        min_interval = getattr(config, 'GROUP_PARTICIPATION_MIN_INTERVAL', 300)
        base_probability = getattr(config, 'GROUP_PARTICIPATION_BASE_PROBABILITY', 0.3)
        
        # 如果是传统模式，使用原有逻辑
        if participation_mode == 'at_only':
            return False, "仅回复@消息模式", 0.0
        elif participation_mode == 'all':
            return True, "回复所有消息模式", 1.0
        
        # 智能模式判断
        reasons = []
        total_score = 0.0
        
        # 1. 检查冷却时间
        if not self.check_cooldown(group_id, min_interval):
            return False, "冷却时间未到", 0.0
        
        # 2. 分析消息相关性
        relevance_score, relevance_reason = self.analyze_message_relevance(content)
        total_score += relevance_score * 0.4  # 相关性占40%权重
        if relevance_reason:
            reasons.append(f"相关性: {relevance_reason}")
        
        # 3. 计算对话活跃度
        activity_score = self.calculate_conversation_activity(group_id)
        total_score += activity_score * 0.3  # 活跃度占30%权重
        reasons.append(f"活跃度: {activity_score:.2f}")
        
        # 4. 随机因子
        random_factor = random.random()
        total_score += random_factor * 0.3  # 随机性占30%权重
        reasons.append(f"随机因子: {random_factor:.2f}")
        
        # 5. 应用基础概率
        final_probability = total_score * base_probability
        
        # 6. 特殊情况加成
        state = self.get_group_state(group_id)
        
        # 如果很久没有参与对话，增加参与概率
        time_since_last_reply = time.time() - state['last_reply_time']
        if time_since_last_reply > 3600:  # 超过1小时
            final_probability += 0.2
            reasons.append("长时间未参与加成")
        
        # 如果是连续对话，降低参与概率
        if len(state['recent_messages']) >= 3:
            recent_senders = [msg['sender'] for msg in state['recent_messages'][-3:]]
            if len(set(recent_senders)) <= 2:  # 只有1-2个人在对话
                final_probability *= 0.7
                reasons.append("连续对话降权")
        
        # 最终决策
        should_reply = random.random() < final_probability
        
        reason_text = f"评分: {total_score:.2f}, 概率: {final_probability:.2f}, " + "; ".join(reasons)
        
        # 如果决定参与，更新状态
        if should_reply:
            state['last_reply_time'] = time.time()
            state['last_participation_score'] = total_score
        
        return should_reply, reason_text, total_score
    
    def get_participation_stats(self, group_id: str) -> Dict:
        """获取群聊参与统计信息"""
        state = self.get_group_state(group_id)
        current_time = time.time()
        
        return {
            'group_id': group_id,
            'total_messages': state['message_count'],
            'active_users_count': len(state['active_users']),
            'last_reply_ago': current_time - state['last_reply_time'],
            'last_message_ago': current_time - state['last_message_time'],
            'recent_messages_count': len(state['recent_messages']),
            'last_participation_score': state['last_participation_score']
        }

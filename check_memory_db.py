import sqlite3
import os
import json
from datetime import datetime

def check_memory_database():
    """检查短期记忆数据库的内容和配置"""
    
    # 1. 检查数据库文件
    db_path = "data/avatars/林绫-优化版/memory/db/short_memory.db"
    print(f"=== 检查数据库文件 ===")
    print(f"数据库路径: {db_path}")
    print(f"文件是否存在: {os.path.exists(db_path)}")
    
    if os.path.exists(db_path):
        print(f"文件大小: {os.path.getsize(db_path)} 字节")
        
        # 连接数据库查看内容
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看表结构
        print(f"\n=== 数据库表结构 ===")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        for table in tables:
            print(table[0])
        
        # 查看记录总数
        print(f"\n=== 记录统计 ===")
        cursor.execute("SELECT COUNT(*) FROM short_memory;")
        total_count = cursor.fetchone()[0]
        print(f"总记录数: {total_count}")
        
        # 查看最近的记录
        print(f"\n=== 最近的记录 ===")
        cursor.execute("SELECT user_id, user_message, bot_reply, timestamp FROM short_memory ORDER BY rowid DESC LIMIT 5;")
        recent_records = cursor.fetchall()
        for i, record in enumerate(recent_records, 1):
            print(f"记录 {i}:")
            print(f"  用户ID: {record[0]}")
            print(f"  用户消息: {record[1][:50]}...")
            print(f"  机器人回复: {record[2][:50]}...")
            print(f"  时间戳: {record[3]} (类型: {type(record[3])})")
            print()
        
        # 检查时间戳格式
        print(f"=== 时间戳分析 ===")
        cursor.execute("SELECT DISTINCT typeof(timestamp) FROM short_memory;")
        timestamp_types = cursor.fetchall()
        print(f"时间戳数据类型: {[t[0] for t in timestamp_types]}")
        
        # 检查最新和最旧的记录时间
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM short_memory;")
        min_time, max_time = cursor.fetchone()
        print(f"最旧记录时间: {min_time}")
        print(f"最新记录时间: {max_time}")
        
        conn.close()
    
    # 2. 检查配置文件
    print(f"\n=== 检查配置文件 ===")
    config_path = "src/config/config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 查找记忆相关配置
        memory_config = config.get('memory_and_story', {})
        print(f"记忆系统类型: {memory_config.get('memory_system_type', '未设置')}")
        print(f"归档天数: {memory_config.get('memory_archive_days', '未设置')}")
        print(f"记忆配置: {json.dumps(memory_config, ensure_ascii=False, indent=2)}")
    else:
        print(f"配置文件不存在: {config_path}")
    
    # 3. 检查MD文件
    print(f"\n=== 检查MD文件 ===")
    md_path = "data/avatars/林绫-优化版/memory/md/short_memory.md"
    print(f"MD文件路径: {md_path}")
    print(f"文件是否存在: {os.path.exists(md_path)}")
    
    if os.path.exists(md_path):
        print(f"文件大小: {os.path.getsize(md_path)} 字节")
        with open(md_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"文件内容长度: {len(content)} 字符")
        print(f"文件内容预览:\n{content[:200]}...")

if __name__ == "__main__":
    check_memory_database()
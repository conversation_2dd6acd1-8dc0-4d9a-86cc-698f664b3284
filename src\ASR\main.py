import json
import gzip
import struct
import threading
import uuid
import queue
import signal
import sys
from typing import Optional
import time

import websocket  # websocket-client
import pyaudio

# ===================== 配置区域 =====================
# 请在此处填入控制台获取的密钥
APP_KEY = "4365273046"  # X-Api-App-Key
ACCESS_KEY = "_tPctAlXuvz-8SubOAiJQ18V_4vCfe59"  # X-Api-Access-Key
# 如果未来接口需要 Secret Key，可在此使用
SECRET_KEY = "lDFuAh1nTAV9DQpMVO75bdsRpQHjH_EE"
# 资源 ID: 时长版(volc.bigasr.sauc.duration) / 并发版(volc.bigasr.sauc.concurrent)
RESOURCE_ID = "volc.bigasr.sauc.duration"
# ===================== 配置结束 =====================

# 获取ASR设备关键词配置
def get_asr_device_keywords():
    """获取ASR设备关键词，支持防回音配置"""
    try:
        from src.config import VOICE_CALL_ANTI_ECHO_ENABLED, VOICE_CALL_ASR_DEVICE_KEYWORD
        if VOICE_CALL_ANTI_ECHO_ENABLED:
            # 使用配置的ASR专用设备
            return [VOICE_CALL_ASR_DEVICE_KEYWORD]
        else:
            # 使用默认设备关键词
            return ["VB-Audio", "VB Virtual Audio", "CABLE", "VOICEMEETER"]
    except ImportError:
        # 配置未加载时使用默认值
        return ["VB-Audio", "VB Virtual Audio", "CABLE", "VOICEMEETER"]

# VB-Audio 虚拟麦克风在 Windows 下常见的名称关键字，可根据实际情况修改
VB_DEVICE_KEYWORDS = get_asr_device_keywords()

# 音频参数 (需与 full client request 保持一致)
SAMPLE_RATE = 16000  # 采样率
CHANNELS = 1         # 单声道
SAMPLE_WIDTH = 2     # 16bit = 2 bytes
CHUNK_DURATION_MS = 200  # 每包音频时长(毫秒)
CHUNK_SIZE = SAMPLE_RATE * CHUNK_DURATION_MS // 1000  # 3200 samples ≈ 200 ms

WS_URL = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel"  # 双向流式


def build_header(message_type: int,
                 flags: int,
                 serialization: int,
                 compression: int,
                 version: int = 0b0001,
                 header_size: int = 0b0001) -> bytes:
    """构造 4 字节协议头"""
    byte0 = ((version & 0x0F) << 4) | (header_size & 0x0F)
    byte1 = ((message_type & 0x0F) << 4) | (flags & 0x0F)
    byte2 = ((serialization & 0x0F) << 4) | (compression & 0x0F)
    byte3 = 0x00  # reserved
    return bytes([byte0, byte1, byte2, byte3])


def build_message(header: bytes, payload: bytes) -> bytes:
    """header + payload_size(4B) + payload"""
    payload_size = struct.pack('>I', len(payload))  # uint32 big-endian
    return header + payload_size + payload


class AudioCapture(threading.Thread):
    """后台线程: 采集 VB 虚拟麦克风音频"""

    def __init__(self):
        super().__init__(daemon=True)
        self._q: "queue.Queue[bytes]" = queue.Queue()
        self._stopped = threading.Event()
        self._pa = pyaudio.PyAudio()
        self._stream = None

    def _find_vb_device(self) -> Optional[int]:
        """查找包含关键字的音频输入设备索引，优先匹配最相似的设备"""
        import difflib

        devices = []
        for i in range(self._pa.get_device_count()):
            info = self._pa.get_device_info_by_index(i)
            if info.get('maxInputChannels', 0) > 0:
                devices.append((i, info.get('name', '')))

        # 首先尝试精确匹配
        for keyword in VB_DEVICE_KEYWORDS:
            for device_idx, device_name in devices:
                if keyword.lower() in device_name.lower():
                    return device_idx

        # 如果没有精确匹配，尝试找最相似的设备
        if VB_DEVICE_KEYWORDS:
            primary_keyword = VB_DEVICE_KEYWORDS[0]  # 使用第一个关键词作为主要匹配目标
            device_names = [name for _, name in devices]
            closest_matches = difflib.get_close_matches(primary_keyword, device_names, n=1, cutoff=0.3)

            if closest_matches:
                closest_name = closest_matches[0]
                for device_idx, device_name in devices:
                    if device_name == closest_name:
                        print(f"使用相似匹配的设备: {device_name} (匹配关键词: {primary_keyword})")
                        return device_idx

        return None

    def run(self):
        device_index = self._find_vb_device()
        if device_index is None:
            print("未找到 VB 虚拟麦克风，改用系统默认输入设备。")
            device_index = None
        else:
            print(f"使用输入设备 #{device_index}: {self._pa.get_device_info_by_index(device_index)['name']}")

        self._stream = self._pa.open(format=self._pa.get_format_from_width(SAMPLE_WIDTH),
                                     channels=CHANNELS,
                                     rate=SAMPLE_RATE,
                                     input=True,
                                     frames_per_buffer=CHUNK_SIZE,
                                     input_device_index=device_index)

        while not self._stopped.is_set():
            data = self._stream.read(CHUNK_SIZE, exception_on_overflow=False)
            self._q.put(data)

    def read(self) -> bytes:
        """阻塞直到获取一个音频块"""
        return self._q.get()

    def stop(self):
        self._stopped.set()
        if self._stream is not None:
            self._stream.stop_stream()
            self._stream.close()
            self._stream = None
        # 注意：不调用 self._pa.terminate()，避免影响其他音频功能
        # PyAudio 实例会在对象销毁时自动清理


class BigASRClient:
    def __init__(self):
        self.connect_id = str(uuid.uuid4())
        self._audio_capture = AudioCapture()
        self.ws: Optional[websocket.WebSocketApp] = None
        self._send_thread = None
        self._seq = 1  # 自增 sequence，可选
        # 记录上一次打印内容与时间，用于去重 + 空闲换段
        self._last_printed: str = ""
        self._last_print_time: float = time.time()
        self._latest_full_text: str = ""  # 最新完整识别文本
        self._summary_timer: Optional[threading.Timer] = None  # 2 秒后输出总结
        self._summary_index: int = 0  # 已总结到的字符下标

    # ---------------- WebSocket 事件 -----------------
    def _on_open(self, ws):
        print("[WebSocket] 连接已建立，发送 full client request …")
        # full client request payload
        request_payload = {
            "user": {
                "uid": self.connect_id   # 可替换为业务侧用户唯一 ID
            },
            "audio": {
                "format": "pcm",
                "rate": SAMPLE_RATE,
                "bits": 16,
                "channel": CHANNELS,
                "language": "zh-CN"
            },
            "request": {
                "model_name": "bigmodel",
                "enable_itn": True,
                "enable_punc": True,
                "enable_ddc": False,
                "result_type": "full"  # 使用全量返回，便于 2 秒后输出总结
            }
        }
        payload_bytes = json.dumps(request_payload).encode('utf-8')
        header = build_header(message_type=0b0001,  # full client request
                              flags=0b0000,
                              serialization=0b0001,  # JSON
                              compression=0b0000)    # 无压缩
        ws.send(build_message(header, payload_bytes), opcode=websocket.ABNF.OPCODE_BINARY)
        # 启动音频发送线程
        self._send_thread = threading.Thread(target=self._audio_sender, args=(ws,), daemon=True)
        self._send_thread.start()

    def _on_message(self, ws, message: bytes):
        """处理服务端返回的二进制消息"""
        if len(message) < 8:
            return
        # 解析 header
        header = message[:4]
        byte1 = header[1]
        msg_type = (byte1 >> 4) & 0x0F
        # msg_type == 0b1111 error, 0b1001 full server response
        if msg_type == 0b1111:
            # error frame
            error_code = struct.unpack('>I', message[4:8])[0]
            error_msg = message[12:].decode('utf-8', errors='ignore')
            print(f"[ServerError] code={error_code}, message={error_msg}")
            return

        if msg_type != 0b1001:
            return  # 忽略非响应帧

        # full server response: header(4) + seq(4) + payload_size(4) + payload
        if len(message) < 12:
            return
        payload_size = struct.unpack('>I', message[8:12])[0]
        payload = message[12:12 + payload_size]
        # 判断是否 gzip 压缩
        compression_flag = header[2] & 0x0F
        try:
            if compression_flag == 0b0001:
                payload = gzip.decompress(payload)
            data = json.loads(payload.decode('utf-8'))
            if 'result' in data and 'text' in data['result']:
                text = data['result']['text']

                # 保存最新完整文本
                self._latest_full_text = text

                # -------- 去重 + 分段逻辑 (增量输出) --------
                # 1. 计算增量文本
                incr = text
                if text.startswith(self._last_printed):
                    incr = text[len(self._last_printed):]

                # 若增量为空则忽略
                if not incr:
                    return

                now = time.time()

                # 2. 若距离上次输出超过 1.5 秒 → 认为新段落，先换行
                if now - self._last_print_time > 1.5:
                    print()
                    # 重置已打印文本，避免新一段仍与旧句比较导致重复
                    self._last_printed = ""

                # 3. 打印增量（不换行，实时续写）
                print(incr, end='', flush=True)

                # 4. 更新状态
                self._last_printed = text
                self._last_print_time = now

                # 5. 重置 2 秒总结计时器
                self._reset_summary_timer()
        except Exception as e:
            print("[DecodeError]", e)

    def _on_error(self, ws, error):
        print("[WebSocketError]", error)

    def _on_close(self, ws, status_code, msg):
        print(f"[WebSocket] 连接关闭: {status_code} {msg}")
        self._audio_capture.stop()
        if self._summary_timer:
            self._summary_timer.cancel()

    # ---------------- 音频发送循环 -----------------
    def _audio_sender(self, ws: websocket.WebSocketApp):
        print("[Audio] 开始采集并发送音频 … 按 Ctrl+C 结束")
        self._audio_capture.start()
        try:
            while True:
                pcm = self._audio_capture.read()
                header = build_header(message_type=0b0010,  # audio only request
                                      flags=0b0000,        # 非最后一包
                                      serialization=0b0000,  # none
                                      compression=0b0000)    # 无压缩
                ws.send(build_message(header, pcm), opcode=websocket.ABNF.OPCODE_BINARY)
        except Exception as e:
            print("[AudioSender]", e)

    # ---------------- 外部接口 -----------------
    def start(self):
        headers = {
            "X-Api-App-Key": APP_KEY,
            "X-Api-Access-Key": ACCESS_KEY,
            "X-Api-Resource-Id": RESOURCE_ID,
            "X-Api-Connect-Id": self.connect_id
        }
        self.ws = websocket.WebSocketApp(WS_URL,
                                         header=headers,
                                         on_open=self._on_open,
                                         on_message=self._on_message,
                                         on_error=self._on_error,
                                         on_close=self._on_close)
        # 仅在主线程中注册 Ctrl+C 信号处理，避免在后台线程触发 "signal only works in main thread" 异常
        if threading.current_thread() is threading.main_thread():
            # 捕获 Ctrl+C
            def _signal_handler(sig, frame):
                print("\n收到中断信号，正在发送最后一包音频 …")
                self._send_last_audio()
                self.ws.close()

            try:
                signal.signal(signal.SIGINT, _signal_handler)
            except Exception:
                pass
        self.ws.run_forever(ping_interval=20, ping_timeout=5)

    def _send_last_audio(self):
        try:
            pcm = self._audio_capture.read()
        except queue.Empty:
            pcm = b""
        header = build_header(message_type=0b0010,  # audio only request
                              flags=0b0010,        # 最后一包
                              serialization=0b0000,
                              compression=0b0000)
        if self.ws:
            self.ws.send(build_message(header, pcm), opcode=websocket.ABNF.OPCODE_BINARY)



    # ---------------- 总结计时 -----------------
    def _print_summary(self):
        """在 2 秒无增量后输出新增的完整句子片段，避免重复"""
        if not self._latest_full_text:
            return

        # 计算未总结的片段
        if len(self._latest_full_text) >= self._summary_index:
            segment = self._latest_full_text[self._summary_index:]
        else:
            # 长度变短：服务端重置，新句子
            segment = self._latest_full_text
        if segment.strip():
            print(f"\n{segment}")
        # 更新索引
        self._summary_index = len(self._latest_full_text)
        # 同步 _last_printed，防止后续增量重复
        self._last_printed = self._latest_full_text

    def _reset_summary_timer(self):
        if self._summary_timer:
            self._summary_timer.cancel()
        self._summary_timer = threading.Timer(2.0, self._print_summary)
        self._summary_timer.daemon = True
        self._summary_timer.start()


if __name__ == "__main__":
    if not APP_KEY or not ACCESS_KEY:
        print("请先在 main.py 中填写 APP_KEY 和 ACCESS_KEY！")
        sys.exit(1)
    client = BigASRClient()
    client.start() 
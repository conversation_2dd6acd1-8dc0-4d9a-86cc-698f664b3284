#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
好友请求管理器
负责处理好友请求的每日数量限制
"""

from datetime import datetime
from sqlalchemy.orm import Session
from database import DailyStats, Session as DbSession
import config
from utils.logger import log

class FriendRequestManager:
    """好友请求管理器"""

    def __init__(self, session: Session):
        self.session = session

    def get_today_accepted_count(self) -> int:
        """获取今天已接受的好友请求数量"""
        today_str = datetime.now().strftime("%Y-%m-%d")
        today_stats = self.session.query(DailyStats).filter_by(date=today_str).first()
        return today_stats.accepted_friends_count if today_stats else 0

    def increment_today_accepted_count(self):
        """增加今天已接受的好友请求数量"""
        today_str = datetime.now().strftime("%Y-%m-%d")
        today_stats = self.session.query(DailyStats).filter_by(date=today_str).first()

        if today_stats:
            today_stats.accepted_friends_count += 1
        else:
            today_stats = DailyStats(date=today_str, accepted_friends_count=1)
            self.session.add(today_stats)
        
        self.session.commit()

    def has_reached_daily_limit(self) -> bool:
        """检查是否已达到每日上限"""
        limit = getattr(config, 'MAX_AUTO_ACCEPT_FRIENDS_PER_DAY', 15)
        count = self.get_today_accepted_count()
        if count >= limit:
            log(f"今日自动接受好友请求已达上限 ({count}/{limit})", "INFO")
            return True
        return False


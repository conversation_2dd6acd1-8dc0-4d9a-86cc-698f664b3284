@echo off
chcp 65001 >nul
title 微信角色扮演机器人

echo ========================================
echo 微信角色扮演机器人启动脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

:: 检查配置文件
if not exist "config.py" (
    echo 错误：配置文件 config.py 不存在
    echo 请先配置 config.py 文件
    pause
    exit /b 1
)

:: 启动机器人
echo.
echo 启动机器人...
echo 按 Ctrl+C 停止机器人
echo.
python main.py

:: 如果程序异常退出，暂停以查看错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，请检查错误信息
    pause
)

echo.
echo 机器人已停止
pause
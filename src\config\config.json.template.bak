{"categories": {"user_settings": {"title": "用户设置", "settings": {"listen_list": {"value": [""], "type": "array", "description": "要监听的用户列表（请使用微信昵称，不要使用备注名）"}, "group_chat_config": {"value": [], "type": "array", "description": "群聊配置列表（为不同群聊配置专用人设和触发词）"}}}, "llm_settings": {"title": "大语言模型配置", "settings": {"api_key": {"value": "", "type": "string", "description": "DeepSeek API密钥", "is_secret": true}, "base_url": {"value": "https://api.kourichat.com/v1", "type": "string", "description": "DeepSeek API基础URL"}, "model": {"value": "kourichat-v3", "type": "string", "description": "使用的AI模型名称", "options": ["kourichat-v3", "gemini-2.5-pro", "grok-3-latest", "claude-3-5-sonnet-20240620"]}, "max_tokens": {"value": 2000, "type": "number", "description": "回复最大token数量"}, "temperature": {"value": 1.1, "type": "number", "description": "AI回复的温度值", "min": 0.0, "max": 1.7}}}, "media_settings": {"title": "媒体设置", "settings": {"image_recognition": {"api_key": {"value": "", "type": "string", "description": "图像识别API密钥", "is_secret": true}, "base_url": {"value": "https://api.kourichat.com/v1", "type": "string", "description": "图像识别API基础URL"}, "temperature": {"value": 0.7, "type": "number", "description": "图像识别温度参数", "min": 0, "max": 1}, "model": {"value": "kourichat-vision", "type": "string", "description": "图像识别 AI 模型"}}, "image_generation": {"model": {"value": "deepseek-ai/Janus-Pro-7B", "type": "string", "description": "图像生成模型"}, "temp_dir": {"value": "data/images/temp", "type": "string", "description": "临时图片存储目录"}}, "text_to_speech": {"tts_api_key": {"value": "", "type": "string", "description": "TTS服务API"}, "voice_dir": {"value": "data/voices", "type": "string", "description": "语音文件存储目录"}}, "fish_api": {"api_key": {"value": "", "type": "string", "description": "Fish Audio API密钥", "is_secret": true}, "model_id": {"value": "", "type": "string", "description": "Fish Audio 模型ID（音色ID）"}, "temperature": {"value": 0.7, "type": "number", "description": "Fish Audio 温度参数", "min": 0, "max": 1}, "top_p": {"value": 0.7, "type": "number", "description": "Fish Audio Top-P参数", "min": 0, "max": 1}, "speed": {"value": 1.0, "type": "number", "description": "Fish Audio 语速", "min": 0.5, "max": 2.0}}, "voice_call": {"anti_echo_enabled": {"value": false, "type": "boolean", "description": "启用防回音功能（使用双虚拟音频设备）"}, "asr_device_keyword": {"value": "CABLE Input", "type": "string", "description": "ASR音频输入设备关键词（用于语音识别）"}, "tts_device_keyword": {"value": "VOICEMEETER", "type": "string", "description": "TTS音频输出设备关键词（用于语音播放）"}}}}, "behavior_settings": {"title": "行为设置", "settings": {"auto_message": {"content": {"value": "请你模拟系统设置的角色，根据之前的聊天内容在微信上找对方发消息想知道对方在做什么，并跟对方报备自己在做什么、什么心情，语气自然，与之前的不要重复", "type": "string", "description": "自动消息内容"}, "countdown": {"min_hours": {"value": 1.0, "type": "number", "description": "最小倒计时时间（小时）"}, "max_hours": {"value": 3.0, "type": "number", "description": "最大倒计时时间（小时）"}}}, "message_queue": {"timeout": {"value": 8, "type": "number", "description": "消息队列等待时间（秒）", "min": 0, "max": 20}}, "quiet_time": {"start": {"value": "22:00", "type": "string", "description": "安静时间开始"}, "end": {"value": "08:00", "type": "string", "description": "安静时间结束"}}, "context": {"max_groups": {"value": 15, "type": "number", "description": "最大上下文轮数"}, "avatar_dir": {"value": "data/avatars/MONO", "type": "string", "description": "人设目录（自动包含 avatar.md 和 emojis 目录）"}}}}, "auth_settings": {"title": "认证设置", "settings": {"admin_password": {"value": "", "type": "string", "description": "管理员密码", "is_secret": true}}}, "schedule_settings": {"title": "定时任务配置", "settings": {"tasks": {"value": [], "type": "array", "description": "定时任务列表"}}}, "network_search_settings": {"title": "网络搜索设置", "settings": {"search_enabled": {"value": false, "type": "boolean", "description": "启用网络搜索功能"}, "weblens_enabled": {"value": false, "type": "boolean", "description": "启用网页内容提取功能"}, "api_key": {"value": "", "type": "string", "description": "网络搜索 API 密钥（留空则使用 LLM 设置中的 API 密钥）", "is_secret": true}, "base_url": {"value": "https://api.kourichat.com/v1", "type": "string", "description": "网络搜索 API 基础 URL（留空则使用 LLM 设置中的 URL）"}}}}}
"""
同步初始化服务
在系统启动时自动执行MD文件到数据库的同步
"""

import os
import logging
from typing import List
from .md_sync_service import MDSyncService

logger = logging.getLogger('main')

class SyncInitializer:
    """
    同步初始化器
    负责在系统启动时执行所有必要的数据同步操作
    """
    
    def __init__(self, root_dir: str):
        self.root_dir = root_dir
        self.sync_service = MDSyncService(root_dir)
    
    def initialize_all_sync(self) -> bool:
        """
        初始化所有角色的数据同步
        
        Returns:
            bool: 同步是否成功
        """
        try:
            logger.info("开始初始化数据同步...")
            
            # 获取所有角色列表
            avatars = self._get_all_avatars()
            
            if not avatars:
                logger.warning("未找到任何角色，跳过数据同步")
                return True
            
            success_count = 0
            total_count = len(avatars)
            
            # 为每个角色执行同步
            for avatar_name in avatars:
                try:
                    logger.info(f"正在同步角色: {avatar_name}")
                    
                    # 同步记忆数据
                    memory_success = self.sync_service.sync_avatar_memory(avatar_name)
                    
                    # 同步剧情数据
                    story_success = self.sync_service.sync_avatar_story(avatar_name)
                    
                    if memory_success and story_success:
                        success_count += 1
                        logger.info(f"角色 {avatar_name} 同步成功")
                    else:
                        logger.warning(f"角色 {avatar_name} 同步部分失败")
                        
                except Exception as e:
                    logger.error(f"同步角色 {avatar_name} 时发生错误: {e}")
            
            # 输出同步结果
            if success_count == total_count:
                logger.info(f"数据同步完成: 所有 {total_count} 个角色同步成功")
                return True
            else:
                logger.warning(f"数据同步完成: {success_count}/{total_count} 个角色同步成功")
                return False
                
        except Exception as e:
            logger.error(f"初始化数据同步失败: {e}")
            return False
    
    def sync_specific_avatar(self, avatar_name: str) -> bool:
        """
        同步指定角色的数据
        
        Args:
            avatar_name: 角色名称
            
        Returns:
            bool: 同步是否成功
        """
        try:
            logger.info(f"开始同步指定角色: {avatar_name}")
            
            # 检查角色是否存在
            if not self._avatar_exists(avatar_name):
                logger.error(f"角色 {avatar_name} 不存在")
                return False
            
            # 同步记忆数据
            memory_success = self.sync_service.sync_avatar_memory(avatar_name)
            
            # 同步剧情数据
            story_success = self.sync_service.sync_avatar_story(avatar_name)
            
            if memory_success and story_success:
                logger.info(f"角色 {avatar_name} 同步成功")
                return True
            else:
                logger.warning(f"角色 {avatar_name} 同步部分失败")
                return False
                
        except Exception as e:
            logger.error(f"同步角色 {avatar_name} 失败: {e}")
            return False
    
    def create_default_structure(self, avatar_name: str) -> bool:
        """
        为指定角色创建默认的新记忆系统目录结构
        
        Args:
            avatar_name: 角色名称
            
        Returns:
            bool: 创建是否成功
        """
        try:
            avatar_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name)
            
            if not os.path.exists(avatar_dir):
                logger.error(f"角色目录不存在: {avatar_dir}")
                return False
            
            # 创建memory目录结构（新记忆系统）
            memory_dir = os.path.join(avatar_dir, "memory")
            os.makedirs(os.path.join(memory_dir, "db"), exist_ok=True)
            os.makedirs(os.path.join(memory_dir, "md"), exist_ok=True)
            os.makedirs(os.path.join(memory_dir, "archived_memory"), exist_ok=True)

            # 创建word目录结构（剧情系统）
            story_dir = os.path.join(avatar_dir, "word")
            os.makedirs(os.path.join(story_dir, "db"), exist_ok=True)
            os.makedirs(os.path.join(story_dir, "md"), exist_ok=True)

            # 创建默认MD文件
            self._create_default_md_files(avatar_name, memory_dir, story_dir)
            
            logger.info(f"为角色 {avatar_name} 创建默认目录结构成功")
            return True
            
        except Exception as e:
            logger.error(f"为角色 {avatar_name} 创建默认目录结构失败: {e}")
            return False
    
    def _get_all_avatars(self) -> List[str]:
        """获取所有角色列表"""
        try:
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")
            
            if not os.path.exists(avatars_dir):
                return []
            
            avatars = []
            for item in os.listdir(avatars_dir):
                item_path = os.path.join(avatars_dir, item)
                if os.path.isdir(item_path):
                    avatars.append(item)
            
            return avatars
            
        except Exception as e:
            logger.error(f"获取角色列表失败: {e}")
            return []
    
    def _avatar_exists(self, avatar_name: str) -> bool:
        """检查角色是否存在"""
        avatar_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name)
        return os.path.exists(avatar_dir) and os.path.isdir(avatar_dir)
    
    def _create_default_md_files(self, avatar_name: str, memory_dir: str, story_dir: str):
        """创建默认的MD文件"""
        try:
            # 创建默认核心记忆文件
            core_memory_path = os.path.join(memory_dir, "md", "core_memory.md")
            if not os.path.exists(core_memory_path):
                core_memory_content = f"""# {avatar_name} 核心记忆

## 用户基本信息
- 用户ID: 默认用户
- 称呼偏好: 未设置
- 基本特征: 暂无记录

## 重要事件记录
- 暂无重要事件记录

## 用户偏好
- 对话风格: 未确定
- 兴趣爱好: 暂无记录
- 特殊需求: 暂无记录

## 关系发展
- 初次见面时间: 未记录
- 关系状态: 新用户
- 互动频率: 暂无数据

---
*最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*记录状态: 初始化*
"""
                with open(core_memory_path, 'w', encoding='utf-8') as f:
                    f.write(core_memory_content)
            
            # 创建默认短期记忆文件
            short_memory_path = os.path.join(memory_dir, "md", "short_memory.md")
            if not os.path.exists(short_memory_path):
                short_memory_content = f"""# {avatar_name} 短期记忆

## 本周对话记录

### {datetime.now().strftime('%Y-%m-%d')}
暂无对话记录

---

## 对话统计
- 本周对话次数: 0
- 最后对话时间: 未记录
- 活跃时间段: 暂无数据

## 待处理事项
- 暂无待处理事项

---
*记录周期: 一周*
*自动归档: 每周日晚上*
"""
                with open(short_memory_path, 'w', encoding='utf-8') as f:
                    f.write(short_memory_content)
            
            # 创建默认剧情内容文件
            story_content_path = os.path.join(story_dir, "md", "story_content.md")
            if not os.path.exists(story_content_path):
                story_content = f"""# {avatar_name} 角色剧情内容

## 角色背景设定
{avatar_name}是一个独特的角色，拥有自己的性格特点和背景故事。

## 主要剧情线
### 角色介绍
- 基本信息待补充
- 性格特点待补充
- 背景故事待补充

### 日常生活
- 生活习惯待补充
- 兴趣爱好待补充
- 社交关系待补充

## 重要台词
- "请多指教！"
- "很高兴认识你"
- "有什么需要帮助的吗？"

## 人物关系
- 主要角色: 待补充
- 其他角色: 待补充

## 重要事件
- 重要经历待补充
- 转折点待补充
- 成长历程待补充

---
*这是用户可以自由编辑的剧情内容文件*
*AI会自动读取并分类到不同的MD文件中*
"""
                with open(story_content_path, 'w', encoding='utf-8') as f:
                    f.write(story_content)
            
            logger.debug(f"为角色 {avatar_name} 创建默认MD文件成功")
            
        except Exception as e:
            logger.error(f"创建默认MD文件失败: {e}")

# 导入datetime模块
from datetime import datetime

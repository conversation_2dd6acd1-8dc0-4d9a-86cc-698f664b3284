"""
剧情内容分类器
使用LLM自动分类剧情内容为不同类型
"""

import os
import logging
import re
from typing import Dict, List, Tuple
from src.services.ai.llm_service import LLMService

logger = logging.getLogger('main')

class StoryClassifier:
    """
    剧情内容分类器
    功能：
    1. 读取story_content.md文件
    2. 使用LLM分析和分类内容
    3. 生成分类后的MD文件
    4. 同步到数据库
    """
    
    def __init__(self, root_dir: str, api_key: str, base_url: str, model: str, max_token: int, temperature: float):
        self.root_dir = root_dir
        self.llm_service = LLMService(
            api_key=api_key,
            base_url=base_url,
            model=model,
            max_token=max_token,
            temperature=temperature,
            max_groups=5  # 分类任务不需要太多上下文
        )
    
    def classify_story_content(self, avatar_name: str) -> bool:
        """
        分类指定角色的剧情内容
        
        Args:
            avatar_name: 角色名称
            
        Returns:
            bool: 分类是否成功
        """
        try:
            # 获取剧情目录
            story_dir = os.path.join(self.root_dir, "data", "avatars", avatar_name, "word")
            md_dir = os.path.join(story_dir, "md")
            
            if not os.path.exists(story_dir):
                logger.info(f"角色 {avatar_name} 没有剧情系统，跳过分类")
                return True
            
            # 读取原始剧情内容
            story_content_path = os.path.join(md_dir, "story_content.md")
            if not os.path.exists(story_content_path):
                logger.warning(f"剧情内容文件不存在: {story_content_path}")
                return False
            
            with open(story_content_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if not content.strip():
                logger.info(f"角色 {avatar_name} 的剧情内容为空，跳过分类")
                return True
            
            # 使用LLM分类内容
            classified_content = self._classify_with_llm(content, avatar_name)
            
            if not classified_content:
                logger.error(f"LLM分类失败: {avatar_name}")
                return False
            
            # 生成分类后的MD文件
            self._generate_classified_md_files(avatar_name, classified_content, md_dir)
            
            logger.info(f"角色 {avatar_name} 剧情内容分类完成")
            return True
            
        except Exception as e:
            logger.error(f"分类角色 {avatar_name} 剧情内容失败: {e}")
            return False
    
    def _classify_with_llm(self, content: str, avatar_name: str) -> Dict[str, List[str]]:
        """
        使用LLM分类剧情内容
        
        Args:
            content: 原始剧情内容
            avatar_name: 角色名称
            
        Returns:
            Dict[str, List[str]]: 分类后的内容
        """
        try:
            # 构建分类提示词
            classification_prompt = f"""
你是一个专业的剧情内容分析师。请将以下关于角色"{avatar_name}"的剧情内容分类整理。

请按照以下格式输出，每个分类下列出相关内容：

## 台词
- [提取角色的重要台词，每行一句]

## 主要剧情
- [提取主要剧情线和故事情节]

## 重要事件
- [提取重要的事件和转折点]

## 人物关系
- [提取人物关系描述]

## 角色背景
- [提取角色背景和设定信息]

请确保输出格式严格按照上述Markdown格式，每个分类下的内容用"-"开头的列表项。

原始剧情内容：
{content}
"""
            
            # 调用LLM进行分类
            response = self.llm_service.get_response(
                message="请分析并分类以下剧情内容",
                user_id="system",
                system_prompt=classification_prompt,
                core_memory="",
                previous_context=[]
            )
            
            if not response or 'Error' in response:
                logger.error("LLM分类响应失败")
                return {}
            
            # 解析LLM响应
            classified_content = self._parse_llm_response(response)
            
            return classified_content
            
        except Exception as e:
            logger.error(f"LLM分类失败: {e}")
            return {}
    
    def _parse_llm_response(self, response: str) -> Dict[str, List[str]]:
        """
        解析LLM的分类响应

        Args:
            response: LLM响应内容

        Returns:
            Dict[str, List[str]]: 解析后的分类内容
        """
        classified_content = {
            'dialogues': [],
            'main_plots': [],
            'important_events': [],
            'relationships': [],
            'background': []
        }

        try:
            # 先尝试按换行符分割，如果没有换行符则按章节标题分割
            if '\n' in response:
                # 标准格式：有换行符
                sections = response.split('## ')

                for section in sections:
                    if not section.strip():
                        continue

                    lines = section.strip().split('\n')
                    if not lines:
                        continue

                    section_title = lines[0].strip()
                    section_content = lines[1:] if len(lines) > 1 else []

                    # 提取列表项
                    items = []
                    for line in section_content:
                        line = line.strip()
                        if line.startswith('- '):
                            items.append(line[2:].strip())

                    # 根据标题分类
                    self._classify_section(section_title, items, classified_content)
            else:
                # 紧凑格式：没有换行符，所有内容在一行中
                # 按章节标题分割
                sections = response.split('## ')

                for section in sections:
                    if not section.strip():
                        continue

                    # 查找第一个列表项的位置来分离标题和内容
                    section_text = section.strip()
                    first_dash = section_text.find('- ')

                    if first_dash == -1:
                        continue

                    section_title = section_text[:first_dash].strip()
                    content_text = section_text[first_dash:]

                    # 提取列表项（按 "- " 分割）
                    items = []
                    item_parts = content_text.split('- ')
                    for item_part in item_parts:
                        if item_part.strip():
                            # 清理项目内容，移除可能的章节标题
                            item_text = item_part.strip()
                            # 如果包含下一个章节标题，截断到该位置
                            next_section_pos = item_text.find('## ')
                            if next_section_pos != -1:
                                item_text = item_text[:next_section_pos].strip()
                            if item_text:
                                items.append(item_text)

                    # 根据标题分类
                    self._classify_section(section_title, items, classified_content)

            return classified_content

        except Exception as e:
            logger.error(f"解析LLM响应失败: {e}")
            return classified_content

    def _classify_section(self, section_title: str, items: List[str], classified_content: Dict[str, List[str]]):
        """
        根据章节标题将内容分类到对应的类别中

        Args:
            section_title: 章节标题
            items: 章节内容列表
            classified_content: 分类结果字典
        """
        title_lower = section_title.lower()
        if '台词' in title_lower or 'dialogue' in title_lower:
            classified_content['dialogues'].extend(items)
        elif '剧情' in title_lower or 'plot' in title_lower:
            classified_content['main_plots'].extend(items)
        elif '事件' in title_lower or 'event' in title_lower:
            classified_content['important_events'].extend(items)
        elif '关系' in title_lower or 'relationship' in title_lower:
            classified_content['relationships'].extend(items)
        elif '背景' in title_lower or 'background' in title_lower:
            classified_content['background'].extend(items)
    
    def _generate_classified_md_files(self, avatar_name: str, classified_content: Dict[str, List[str]], md_dir: str):
        """
        生成分类后的MD文件
        
        Args:
            avatar_name: 角色名称
            classified_content: 分类后的内容
            md_dir: MD文件目录
        """
        try:
            from datetime import datetime
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 生成台词文件
            if classified_content['dialogues']:
                dialogues_path = os.path.join(md_dir, "dialogues.md")
                dialogues_content = f"# {avatar_name} 台词集\n\n"
                for dialogue in classified_content['dialogues']:
                    dialogues_content += f"- \"{dialogue}\"\n"
                dialogues_content += f"\n---\n*自动生成时间: {current_time}*\n"
                
                with open(dialogues_path, 'w', encoding='utf-8') as f:
                    f.write(dialogues_content)
            
            # 生成主要剧情文件
            if classified_content['main_plots']:
                plots_path = os.path.join(md_dir, "main_plots.md")
                plots_content = f"# {avatar_name} 主要剧情\n\n"
                for plot in classified_content['main_plots']:
                    plots_content += f"## {plot}\n\n"
                plots_content += f"---\n*自动生成时间: {current_time}*\n"
                
                with open(plots_path, 'w', encoding='utf-8') as f:
                    f.write(plots_content)
            
            # 生成重要事件文件
            if classified_content['important_events']:
                events_path = os.path.join(md_dir, "important_events.md")
                events_content = f"# {avatar_name} 重要事件\n\n"
                for event in classified_content['important_events']:
                    events_content += f"- {event}\n"
                events_content += f"\n---\n*自动生成时间: {current_time}*\n"
                
                with open(events_path, 'w', encoding='utf-8') as f:
                    f.write(events_content)
            
            # 生成人物关系文件
            if classified_content['relationships']:
                relationships_path = os.path.join(md_dir, "relationships.md")
                relationships_content = f"# {avatar_name} 人物关系\n\n"
                for relationship in classified_content['relationships']:
                    relationships_content += f"- {relationship}\n"
                relationships_content += f"\n---\n*自动生成时间: {current_time}*\n"
                
                with open(relationships_path, 'w', encoding='utf-8') as f:
                    f.write(relationships_content)
            
            # 生成角色背景文件
            if classified_content['background']:
                background_path = os.path.join(md_dir, "background.md")
                background_content = f"# {avatar_name} 角色背景\n\n"
                for bg in classified_content['background']:
                    background_content += f"{bg}\n\n"
                background_content += f"---\n*自动生成时间: {current_time}*\n"
                
                with open(background_path, 'w', encoding='utf-8') as f:
                    f.write(background_content)
            
            logger.info(f"为角色 {avatar_name} 生成分类MD文件成功")
            
        except Exception as e:
            logger.error(f"生成分类MD文件失败: {e}")
    
    def classify_all_avatars(self) -> bool:
        """
        分类所有角色的剧情内容
        
        Returns:
            bool: 分类是否成功
        """
        try:
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")
            
            if not os.path.exists(avatars_dir):
                logger.warning("角色目录不存在")
                return False
            
            success_count = 0
            total_count = 0
            
            for avatar_name in os.listdir(avatars_dir):
                avatar_path = os.path.join(avatars_dir, avatar_name)
                if os.path.isdir(avatar_path):
                    total_count += 1
                    if self.classify_story_content(avatar_name):
                        success_count += 1
            
            logger.info(f"剧情分类完成: {success_count}/{total_count} 个角色分类成功")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"分类所有角色剧情失败: {e}")
            return False

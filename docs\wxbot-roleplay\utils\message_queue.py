#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息队列管理器
确保机器人按顺序处理和回复消息
"""

import threading
import time
from queue import Queue, Empty
from dataclasses import dataclass
from typing import Optional, Callable, Any
from utils.logger import log


@dataclass
class QueuedMessage:
    """队列中的消息对象"""
    msg: Any  # 原始消息对象
    chat: Any  # 聊天对象
    chat_name: str  # 聊天窗口名称
    sender: str  # 发送者
    content: str  # 消息内容
    timestamp: float  # 消息时间戳
    is_group: bool  # 是否为群聊
    message_type: str  # 消息类型 ('friend', 'system', 'admin_command', etc.)


class MessageQueueManager:
    """消息队列管理器"""
    
    def __init__(self, bot_instance, max_queue_size: int = 100):
        """
        初始化消息队列管理器
        
        Args:
            bot_instance: 机器人实例
            max_queue_size: 队列最大大小
        """
        self.bot = bot_instance
        self.message_queue = Queue(maxsize=max_queue_size)
        self.processing_thread = None
        self.is_running = False
        self.is_processing = False
        self.current_message = None
        self.lock = threading.Lock()
        
        # 统计信息
        self.total_processed = 0
        self.total_queued = 0
        self.queue_full_count = 0
        
    def start(self):
        """启动消息队列处理"""
        if self.is_running:
            log("消息队列已在运行中", "WARNING")
            return
            
        self.is_running = True
        self.processing_thread = threading.Thread(
            target=self._process_queue,
            name="MessageQueueProcessor",
            daemon=True
        )
        self.processing_thread.start()
        log("消息队列处理器已启动")
        
    def stop(self):
        """停止消息队列处理"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # 等待当前消息处理完成
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=10)
            
        log("消息队列处理器已停止")
        
    def add_message(self, msg, chat, chat_name: str, sender: str, content: str, 
                   is_group: bool = False, message_type: str = "friend") -> bool:
        """
        添加消息到队列
        
        Args:
            msg: 原始消息对象
            chat: 聊天对象
            chat_name: 聊天窗口名称
            sender: 发送者
            content: 消息内容
            is_group: 是否为群聊
            message_type: 消息类型
            
        Returns:
            bool: 是否成功添加到队列
        """
        if not self.is_running:
            log("消息队列未启动，直接处理消息", "WARNING")
            return False
            
        try:
            queued_msg = QueuedMessage(
                msg=msg,
                chat=chat,
                chat_name=chat_name,
                sender=sender,
                content=content,
                timestamp=time.time(),
                is_group=is_group,
                message_type=message_type
            )
            
            # 非阻塞方式添加到队列
            self.message_queue.put_nowait(queued_msg)
            self.total_queued += 1

            # log(f"消息已加入队列 - 发送者:{sender} 内容:{content[:30]}... 队列大小:{self.message_queue.qsize()}")
            return True
            
        except Exception as e:
            self.queue_full_count += 1
            log(f"消息队列已满，无法添加消息: {str(e)}", "WARNING")
            return False
            
    def _process_queue(self):
        """处理队列中的消息"""
        # log("消息队列处理线程已启动")

        while self.is_running:
            try:
                # 从队列中获取消息，超时1秒
                queued_msg = self.message_queue.get(timeout=1.0)
                
                with self.lock:
                    self.is_processing = True
                    self.current_message = queued_msg
                
                try:
                    # 处理消息
                    self._handle_queued_message(queued_msg)
                    self.total_processed += 1
                    
                except Exception as e:
                    log(f"处理队列消息异常: {str(e)}", "ERROR")
                    
                finally:
                    with self.lock:
                        self.is_processing = False
                        self.current_message = None
                    
                    # 标记任务完成
                    self.message_queue.task_done()
                    
            except Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                log(f"消息队列处理异常: {str(e)}", "ERROR")

        # log("消息队列处理线程已退出")
        
    def _handle_queued_message(self, queued_msg: QueuedMessage):
        """处理单个队列消息"""
        try:
            # log(f"开始处理队列消息 - 发送者:{queued_msg.sender} 类型:{queued_msg.message_type}")

            # 根据消息类型调用相应的处理方法
            if queued_msg.message_type == "friend":
                self.bot.handle_friend_message_direct(queued_msg.msg, queued_msg.chat)
            elif queued_msg.message_type == "system":
                self.bot.handle_system_message(queued_msg.msg, queued_msg.chat)
            elif queued_msg.message_type == "unknown":
                # 对于未知类型的消息，尝试使用通用的直接处理方法
                # log(f"处理未知类型消息，使用通用处理方法", "INFO")
                self.bot._handle_message_direct(queued_msg.msg, queued_msg.chat)
            else:
                # log(f"未知消息类型: {queued_msg.message_type}", "WARNING")
                # 即使是未知类型，也尝试处理，避免消息丢失
                self.bot._handle_message_direct(queued_msg.msg, queued_msg.chat)

        except Exception as e:
            log(f"处理队列消息异常: {str(e)}", "ERROR")
            
    def get_status(self) -> dict:
        """获取队列状态信息"""
        with self.lock:
            return {
                'is_running': self.is_running,
                'is_processing': self.is_processing,
                'queue_size': self.message_queue.qsize(),
                'total_processed': self.total_processed,
                'total_queued': self.total_queued,
                'queue_full_count': self.queue_full_count,
                'current_message': {
                    'sender': self.current_message.sender if self.current_message else None,
                    'content': self.current_message.content[:50] if self.current_message else None,
                    'timestamp': self.current_message.timestamp if self.current_message else None
                } if self.current_message else None
            }
            
    def clear_queue(self):
        """清空消息队列"""
        try:
            while not self.message_queue.empty():
                self.message_queue.get_nowait()
                self.message_queue.task_done()
            # log("消息队列已清空")
        except Exception as e:
            log(f"清空消息队列异常: {str(e)}", "ERROR")
            
    def wait_for_completion(self, timeout: Optional[float] = None):
        """等待队列中所有消息处理完成"""
        try:
            if timeout:
                self.message_queue.join()
            else:
                # 使用超时等待
                start_time = time.time()
                while not self.message_queue.empty() or self.is_processing:
                    if timeout and (time.time() - start_time) > timeout:
                        log("等待队列完成超时", "WARNING")
                        break
                    time.sleep(0.1)
        except Exception as e:
            log(f"等待队列完成异常: {str(e)}", "ERROR")

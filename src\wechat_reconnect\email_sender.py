"""
邮件发送模块
用于发送微信二维码到指定邮箱
"""

import smtplib
import os
import time
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class EmailSender:
    """邮件发送器"""
    
    def __init__(self, smtp_server: str, smtp_port: int, sender_email: str, 
                 sender_password: str, recipient_email: str):
        """
        初始化邮件发送器
        
        Args:
            smtp_server: SMTP服务器地址
            smtp_port: SMTP端口
            sender_email: 发送方邮箱
            sender_password: 发送方邮箱密码/授权码
            recipient_email: 接收方邮箱
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.sender_email = sender_email
        self.sender_password = sender_password
        self.recipient_email = recipient_email
        
    def send_qrcode(self, qr_image_path: str, retry_count: int = 0) -> bool:
        """
        发送微信登录界面截图邮件

        Args:
            qr_image_path: 微信窗口截图路径
            retry_count: 重试次数（用于邮件标题）

        Returns:
            bool: 发送是否成功
        """
        try:
            # 检查截图文件是否存在
            if not os.path.exists(qr_image_path):
                logger.error(f"微信截图文件不存在: {qr_image_path}")
                return False

            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = self.recipient_email

            # 邮件标题
            if retry_count > 0:
                msg['Subject'] = f"微信登录界面截图 - 第{retry_count + 1}次重试"
            else:
                msg['Subject'] = "微信登录界面截图 - 需要扫码重新登录"

            # 邮件正文
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            body = f"""
微信机器人检测到连接已断开，需要重新登录。

请查看附件中的微信登录界面截图，扫描其中的二维码重新登录微信。

注意事项：
1. 二维码有效期约为2分钟，请尽快扫描
2. 如果二维码已过期，系统会自动重新发送新的截图
3. 扫码成功后，机器人将自动恢复运行
4. 如果截图中没有二维码，可能需要手动点击微信登录界面

发送时间: {current_time}
重试次数: {retry_count + 1}

---
KouriChat 微信机器人
            """.strip()

            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # 添加微信截图
            with open(qr_image_path, 'rb') as f:
                img_data = f.read()
                image = MIMEImage(img_data)
                image.add_header('Content-Disposition', 'attachment',
                               filename=f'wechat_screenshot_{int(time.time())}.png')
                msg.attach(image)
            
            # 发送邮件
            logger.info(f"正在发送微信截图邮件到: {self.recipient_email}")

            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            server.send_message(msg)
            server.quit()

            logger.info("微信截图邮件发送成功")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"邮箱认证失败: {e}")
            logger.error("请检查邮箱地址和密码/授权码是否正确")
            return False
            
        except smtplib.SMTPConnectError as e:
            logger.error(f"无法连接到SMTP服务器: {e}")
            return False
            
        except smtplib.SMTPException as e:
            logger.error(f"SMTP错误: {e}")
            return False
            
        except Exception as e:
            logger.error(f"发送邮件时发生未知错误: {e}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试邮箱连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info("测试邮箱连接...")
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            server.quit()
            logger.info("邮箱连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"邮箱连接测试失败: {e}")
            return False


def get_smtp_config(email_provider: str) -> tuple:
    """
    根据邮箱提供商获取SMTP配置
    
    Args:
        email_provider: 邮箱提供商 (qq, gmail, 163, 126, outlook)
        
    Returns:
        tuple: (smtp_server, smtp_port)
    """
    smtp_configs = {
        'qq': ('smtp.qq.com', 587),
        'gmail': ('smtp.gmail.com', 587),
        '163': ('smtp.163.com', 587),
        '126': ('smtp.126.com', 587),
        'outlook': ('smtp-mail.outlook.com', 587),
        'hotmail': ('smtp-mail.outlook.com', 587),
    }
    
    # 从邮箱地址推断提供商
    if '@' in email_provider:
        domain = email_provider.split('@')[1].lower()
        if 'qq.com' in domain:
            provider = 'qq'
        elif 'gmail.com' in domain:
            provider = 'gmail'
        elif '163.com' in domain:
            provider = '163'
        elif '126.com' in domain:
            provider = '126'
        elif 'outlook.com' in domain or 'hotmail.com' in domain:
            provider = 'outlook'
        else:
            provider = 'qq'  # 默认使用QQ邮箱配置
    else:
        provider = email_provider.lower()
    
    return smtp_configs.get(provider, ('smtp.qq.com', 587))

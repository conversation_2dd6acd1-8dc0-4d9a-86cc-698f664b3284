import logging
import time
from typing import Optional

from src.services.ai.network_search_service import NetworkSearchService

logger = logging.getLogger('main')

class WeatherService:
    """简单的天气查询服务，利用现有的 NetworkSearchService 获取指定地点的天气信息。"""

    _cache = {}  # {location: (timestamp, summary)}
    _CACHE_TTL = 600  # 秒

    def __init__(self, network_search_service: NetworkSearchService, default_location: str = "北京"):
        self.network_search_service = network_search_service
        self.default_location = default_location or "北京"

    def get_weather(self, location: Optional[str] = None) -> str:
        """获取天气信息。

        Args:
            location: 查询地点，缺省使用默认地点。
        Returns:
            str: 天气描述文本。
        """
        try:
            loc = location or self.default_location

            # 缓存检查
            ts, cached = self._cache.get(loc, (0, ""))
            if time.time() - ts < self._CACHE_TTL and cached:
                return cached

            query = f"{loc} 当前天气"
            result = self.network_search_service.search_internet(query)
            summary = (result or {}).get("summary") or (result or {}).get("original") or ""
            if not summary:
                return f"{loc} 的天气信息暂时无法获取，请稍后再试。"

            self._cache[loc] = (time.time(), summary.strip())
            return summary.strip()
        except Exception as e:
            logger.error(f"获取天气信息失败: {str(e)}")
            return "暂时无法获取天气信息，请稍后再试。" 
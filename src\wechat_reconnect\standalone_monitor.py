#!/usr/bin/env python3
"""
独立的微信重连监控模块
可以作为独立进程运行，监控微信状态并自动重连
"""

import os
import sys
import time
import logging
import threading
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config import config
from src.wechat_reconnect.reconnect_manager import WeChatReconnectManager
from src.utils.logger import setup_logger

# 设置日志
logger = setup_logger("WeChatReconnectMonitor", 
                     log_file=os.path.join(project_root, "logs", "wechat_reconnect.log"))

class StandaloneWeChatMonitor:
    """独立的微信重连监控器"""
    
    def __init__(self):
        self.wx = None
        self.reconnect_manager = None
        self.running = False
        self.monitor_thread = None
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，正在停止监控...")
        self.stop()
    
    def initialize_wechat(self):
        """初始化微信实例"""
        try:
            logger.info("正在初始化微信实例...")
            
            # 导入wxauto
            from wxauto import WeChat
            
            # 创建微信实例
            self.wx = WeChat()
            
            # 检查微信是否已登录
            if not self.wx.GetSessionList():
                logger.error("微信未登录或无法获取会话列表")
                return False
            
            logger.info("微信实例初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化微信实例失败: {e}")
            return False
    
    def initialize_reconnect_manager(self):
        """初始化重连管理器"""
        try:
            logger.info("正在初始化重连管理器...")
            
            def on_reconnect_success():
                """重连成功回调"""
                logger.info("微信重连成功，继续监控...")
            
            self.reconnect_manager = WeChatReconnectManager(
                wx_instance=self.wx,
                config=config,
                on_reconnect_success=on_reconnect_success
            )
            
            logger.info("重连管理器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化重连管理器失败: {e}")
            return False
    
    def start(self):
        """启动监控"""
        logger.info("=" * 60)
        logger.info("微信重连监控器启动")
        logger.info("=" * 60)
        
        # 初始化微信
        if not self.initialize_wechat():
            logger.error("微信初始化失败，无法启动监控")
            return False
        
        # 初始化重连管理器
        if not self.initialize_reconnect_manager():
            logger.error("重连管理器初始化失败，无法启动监控")
            return False
        
        # 检查配置
        if not config.wechat_reconnect.enable_auto_reconnect:
            logger.warning("微信自动重连功能已禁用")
            return False
        
        # 启动监控
        self.running = True
        self.reconnect_manager.start_monitoring()
        
        # 启动状态报告线程
        self.monitor_thread = threading.Thread(
            target=self._status_monitor,
            name="StatusMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info("微信重连监控已启动")
        logger.info(f"检查间隔: {config.wechat_reconnect.check_interval}秒")
        logger.info(f"最大重试次数: {config.wechat_reconnect.max_retry_attempts}")
        logger.info(f"邮件功能: {'启用' if config.wechat_reconnect.email_enabled else '禁用'}")
        
        return True
    
    def _status_monitor(self):
        """状态监控线程"""
        last_report_time = 0
        report_interval = 300  # 5分钟报告一次状态
        
        while self.running:
            try:
                current_time = time.time()
                
                # 定期报告状态
                if current_time - last_report_time >= report_interval:
                    status = self.reconnect_manager.get_status()
                    logger.info(f"状态报告 - 微信在线: {status['wechat_online']}, "
                              f"监控中: {status['monitoring']}, "
                              f"重连中: {status['reconnecting']}, "
                              f"二维码重试次数: {status['qrcode_retry_count']}")
                    last_report_time = current_time
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"状态监控线程错误: {e}")
                time.sleep(30)
    
    def stop(self):
        """停止监控"""
        logger.info("正在停止微信重连监控...")
        
        self.running = False
        
        if self.reconnect_manager:
            self.reconnect_manager.stop_monitoring()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("微信重连监控已停止")
    
    def run_forever(self):
        """持续运行监控"""
        if not self.start():
            return False
        
        try:
            # 主循环
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("接收到键盘中断信号")
        except Exception as e:
            logger.error(f"监控运行时发生错误: {e}")
        finally:
            self.stop()
        
        return True
    
    def test_email(self):
        """测试邮件配置"""
        logger.info("测试邮件配置...")
        
        if not self.initialize_wechat():
            logger.error("微信初始化失败")
            return False
        
        if not self.initialize_reconnect_manager():
            logger.error("重连管理器初始化失败")
            return False
        
        success = self.reconnect_manager.test_email_config()
        if success:
            logger.info("邮件配置测试成功")
        else:
            logger.error("邮件配置测试失败")
        
        return success
    
    def force_reconnect(self):
        """强制重连"""
        logger.info("执行强制重连...")
        
        if not self.initialize_wechat():
            logger.error("微信初始化失败")
            return False
        
        if not self.initialize_reconnect_manager():
            logger.error("重连管理器初始化失败")
            return False
        
        success = self.reconnect_manager.force_reconnect()
        if success:
            logger.info("强制重连成功")
        else:
            logger.warning("强制重连失败，可能需要扫描二维码")
        
        return success


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="微信重连监控器")
    parser.add_argument("--test-email", action="store_true", help="测试邮件配置")
    parser.add_argument("--force-reconnect", action="store_true", help="强制重连")
    parser.add_argument("--daemon", action="store_true", help="以守护进程模式运行")
    
    args = parser.parse_args()
    
    monitor = StandaloneWeChatMonitor()
    
    if args.test_email:
        success = monitor.test_email()
        sys.exit(0 if success else 1)
    
    if args.force_reconnect:
        success = monitor.force_reconnect()
        sys.exit(0 if success else 1)
    
    # 默认运行监控
    success = monitor.run_forever()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

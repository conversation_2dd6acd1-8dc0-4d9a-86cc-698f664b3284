#!/usr/bin/env python3
"""
记忆归档器 - 将往期记忆保存到oldmemory文件夹
"""

import os
import json
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class MemoryArchiver:
    """记忆归档器类"""
    
    def __init__(self, root_dir: str, avatar_name: str):
        """
        初始化记忆归档器
        
        Args:
            root_dir: 项目根目录
            avatar_name: 角色名称
        """
        self.root_dir = root_dir
        self.avatar_name = avatar_name
        
        # 设置路径
        self.avatar_dir = os.path.join(root_dir, "data", "avatars", avatar_name)
        self.memory_dir = os.path.join(self.avatar_dir, "oldmemory")
        self.oldmemory_dir = os.path.join(self.avatar_dir, "oldmemory")
        
        # 确保oldmemory目录存在
        os.makedirs(self.oldmemory_dir, exist_ok=True)
        
    def archive_old_memories(self, days_threshold: int = 7) -> int:
        """
        归档超过指定天数的记忆

        Args:
            days_threshold: 天数阈值，超过此天数的记忆将被归档

        Returns:
            归档的记忆条数
        """
        try:
            archived_count = 0
            cutoff_date = datetime.now() - timedelta(days=days_threshold)

            # 检查memory目录是否存在
            if not os.path.exists(self.memory_dir):
                logger.info(f"记忆目录不存在: {self.memory_dir}")
                return 0

            # 处理JSON格式的记忆文件
            archived_count += self._archive_json_memories(cutoff_date)

            # 处理文本格式的记忆文件
            archived_count += self._archive_text_memories(cutoff_date)

            # 处理对话记录文件
            archived_count += self._archive_conversation_logs(cutoff_date)

            logger.info(f"成功归档了 {archived_count} 条记忆")
            return archived_count

        except Exception as e:
            logger.error(f"归档记忆时发生错误: {str(e)}")
            raise

    @staticmethod
    def auto_archive_if_enabled(root_dir: str, avatar_name: str) -> int:
        """
        如果启用了自动归档，则执行自动归档

        Args:
            root_dir: 项目根目录
            avatar_name: 角色名称

        Returns:
            归档的记忆条数
        """
        try:
            # 读取配置
            config_path = os.path.join(root_dir, 'src/config/config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            memory_settings = config_data.get('categories', {}).get('memory_and_story_settings', {}).get('settings', {})

            # 检查是否启用自动归档
            auto_archive_enabled = memory_settings.get('auto_archive_memory', {}).get('value', False)
            if not auto_archive_enabled:
                return 0

            # 获取归档天数阈值
            archive_days = memory_settings.get('memory_archive_days', {}).get('value', 7)

            # 创建归档器并执行归档
            archiver = MemoryArchiver(root_dir, avatar_name)
            archived_count = archiver.archive_old_memories(archive_days)

            if archived_count > 0:
                logger.info(f"自动归档完成: 归档了 {archived_count} 条记忆")

            return archived_count

        except Exception as e:
            logger.error(f"自动归档失败: {str(e)}")
            return 0
    
    def _archive_json_memories(self, cutoff_date: datetime) -> int:
        """归档JSON格式的记忆文件"""
        archived_count = 0
        
        # 查找所有JSON记忆文件
        for filename in os.listdir(self.memory_dir):
            if not filename.endswith('.json'):
                continue
                
            file_path = os.path.join(self.memory_dir, filename)
            
            try:
                # 检查文件修改时间
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                if file_mtime < cutoff_date:
                    # 读取并处理记忆内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        memory_data = json.load(f)
                    
                    # 创建归档文件
                    archive_filename = f"archived_{file_mtime.strftime('%Y%m%d_%H%M%S')}_{filename}"
                    archive_path = os.path.join(self.oldmemory_dir, archive_filename)
                    
                    # 添加归档信息
                    archive_data = {
                        "archived_at": datetime.now().isoformat(),
                        "original_file": filename,
                        "original_mtime": file_mtime.isoformat(),
                        "content": memory_data
                    }
                    
                    # 保存归档文件
                    with open(archive_path, 'w', encoding='utf-8') as f:
                        json.dump(archive_data, f, ensure_ascii=False, indent=2)
                    
                    # 删除原文件
                    os.remove(file_path)
                    archived_count += 1
                    
                    logger.info(f"归档记忆文件: {filename} -> {archive_filename}")
                    
            except Exception as e:
                logger.error(f"处理记忆文件 {filename} 时发生错误: {str(e)}")
                continue
        
        return archived_count
    
    def _archive_text_memories(self, cutoff_date: datetime) -> int:
        """归档文本格式的记忆文件"""
        archived_count = 0
        
        # 查找所有文本记忆文件
        text_extensions = ['.txt', '.md', '.log']
        
        for filename in os.listdir(self.memory_dir):
            if not any(filename.endswith(ext) for ext in text_extensions):
                continue
                
            file_path = os.path.join(self.memory_dir, filename)
            
            try:
                # 检查文件修改时间
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                if file_mtime < cutoff_date:
                    # 创建归档文件名
                    name, ext = os.path.splitext(filename)
                    archive_filename = f"archived_{file_mtime.strftime('%Y%m%d_%H%M%S')}_{name}{ext}"
                    archive_path = os.path.join(self.oldmemory_dir, archive_filename)
                    
                    # 读取原文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 创建带归档信息的内容
                    archive_content = f"""# 归档记忆文件
## 归档信息
- 原文件名: {filename}
- 原修改时间: {file_mtime.strftime('%Y-%m-%d %H:%M:%S')}
- 归档时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 原始内容
{content}
"""
                    
                    # 保存归档文件
                    with open(archive_path, 'w', encoding='utf-8') as f:
                        f.write(archive_content)
                    
                    # 删除原文件
                    os.remove(file_path)
                    archived_count += 1
                    
                    logger.info(f"归档文本记忆: {filename} -> {archive_filename}")
                    
            except Exception as e:
                logger.error(f"处理文本记忆文件 {filename} 时发生错误: {str(e)}")
                continue
        
        return archived_count
    
    def _archive_conversation_logs(self, cutoff_date: datetime) -> int:
        """归档对话记录文件"""
        archived_count = 0
        
        # 查找对话记录目录
        conversations_dir = os.path.join(self.memory_dir, "conversations")
        if not os.path.exists(conversations_dir):
            return 0
        
        for filename in os.listdir(conversations_dir):
            file_path = os.path.join(conversations_dir, filename)
            
            if not os.path.isfile(file_path):
                continue
                
            try:
                # 检查文件修改时间
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                if file_mtime < cutoff_date:
                    # 创建归档目录
                    archive_conversations_dir = os.path.join(self.oldmemory_dir, "conversations")
                    os.makedirs(archive_conversations_dir, exist_ok=True)
                    
                    # 创建归档文件名
                    name, ext = os.path.splitext(filename)
                    archive_filename = f"archived_{file_mtime.strftime('%Y%m%d_%H%M%S')}_{name}{ext}"
                    archive_path = os.path.join(archive_conversations_dir, archive_filename)
                    
                    # 移动文件到归档目录
                    shutil.move(file_path, archive_path)
                    archived_count += 1
                    
                    logger.info(f"归档对话记录: {filename} -> {archive_filename}")
                    
            except Exception as e:
                logger.error(f"处理对话记录文件 {filename} 时发生错误: {str(e)}")
                continue
        
        return archived_count
    
    def get_archive_summary(self) -> Dict[str, Any]:
        """获取归档摘要信息"""
        try:
            if not os.path.exists(self.oldmemory_dir):
                return {"total_files": 0, "total_size": 0, "oldest_archive": None, "newest_archive": None}
            
            files = []
            total_size = 0
            
            for root, dirs, filenames in os.walk(self.oldmemory_dir):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    file_stat = os.stat(file_path)
                    files.append({
                        "name": filename,
                        "path": file_path,
                        "size": file_stat.st_size,
                        "mtime": datetime.fromtimestamp(file_stat.st_mtime)
                    })
                    total_size += file_stat.st_size
            
            if not files:
                return {"total_files": 0, "total_size": 0, "oldest_archive": None, "newest_archive": None}
            
            # 按修改时间排序
            files.sort(key=lambda x: x["mtime"])
            
            return {
                "total_files": len(files),
                "total_size": total_size,
                "oldest_archive": files[0]["mtime"].strftime('%Y-%m-%d %H:%M:%S'),
                "newest_archive": files[-1]["mtime"].strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"获取归档摘要时发生错误: {str(e)}")
            return {"total_files": 0, "total_size": 0, "oldest_archive": None, "newest_archive": None}

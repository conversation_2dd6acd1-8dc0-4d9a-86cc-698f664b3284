"""
微信重连模块集成工具
用于将重连功能集成到主程序中
"""

import logging
from typing import Optional
from .reconnect_manager import WeChatReconnectManager

logger = logging.getLogger(__name__)

# 全局重连管理器实例
_reconnect_manager: Optional[WeChatReconnectManager] = None


def initialize_reconnect_manager(wx_instance, config, on_reconnect_success=None) -> WeChatReconnectManager:
    """
    初始化微信重连管理器
    
    Args:
        wx_instance: 微信实例
        config: 配置对象
        on_reconnect_success: 重连成功回调函数
        
    Returns:
        WeChatReconnectManager: 重连管理器实例
    """
    global _reconnect_manager
    
    try:
        _reconnect_manager = WeChatReconnectManager(
            wx_instance=wx_instance,
            config=config,
            on_reconnect_success=on_reconnect_success
        )
        
        logger.info("微信重连管理器初始化成功")
        return _reconnect_manager
        
    except Exception as e:
        logger.error(f"初始化微信重连管理器失败: {e}")
        raise


def start_reconnect_monitoring():
    """启动微信重连监控"""
    global _reconnect_manager
    
    if _reconnect_manager is None:
        logger.error("重连管理器未初始化，无法启动监控")
        return False
    
    try:
        _reconnect_manager.start_monitoring()
        logger.info("微信重连监控已启动")
        return True
        
    except Exception as e:
        logger.error(f"启动微信重连监控失败: {e}")
        return False


def stop_reconnect_monitoring():
    """停止微信重连监控"""
    global _reconnect_manager
    
    if _reconnect_manager is None:
        logger.warning("重连管理器未初始化")
        return
    
    try:
        _reconnect_manager.stop_monitoring()
        logger.info("微信重连监控已停止")
        
    except Exception as e:
        logger.error(f"停止微信重连监控失败: {e}")


def force_reconnect() -> bool:
    """强制重连微信"""
    global _reconnect_manager
    
    if _reconnect_manager is None:
        logger.error("重连管理器未初始化，无法执行强制重连")
        return False
    
    try:
        return _reconnect_manager.force_reconnect()
        
    except Exception as e:
        logger.error(f"强制重连失败: {e}")
        return False


def test_email_config() -> bool:
    """测试邮件配置"""
    global _reconnect_manager
    
    if _reconnect_manager is None:
        logger.error("重连管理器未初始化，无法测试邮件配置")
        return False
    
    try:
        return _reconnect_manager.test_email_config()
        
    except Exception as e:
        logger.error(f"测试邮件配置失败: {e}")
        return False


def get_reconnect_status() -> dict:
    """获取重连状态"""
    global _reconnect_manager
    
    if _reconnect_manager is None:
        return {
            'enabled': False,
            'monitoring': False,
            'reconnecting': False,
            'email_enabled': False,
            'wechat_online': False,
            'qrcode_retry_count': 0,
            'last_qrcode_time': None,
            'error': '重连管理器未初始化'
        }
    
    try:
        return _reconnect_manager.get_status()
        
    except Exception as e:
        logger.error(f"获取重连状态失败: {e}")
        return {
            'enabled': False,
            'monitoring': False,
            'reconnecting': False,
            'email_enabled': False,
            'wechat_online': False,
            'qrcode_retry_count': 0,
            'last_qrcode_time': None,
            'error': str(e)
        }


def cleanup():
    """清理资源"""
    global _reconnect_manager
    
    if _reconnect_manager is not None:
        try:
            _reconnect_manager.stop_monitoring()
            logger.info("微信重连管理器已清理")
        except Exception as e:
            logger.error(f"清理微信重连管理器失败: {e}")
        finally:
            _reconnect_manager = None


# 为了向后兼容，提供一个简单的装饰器
def with_reconnect_protection(func):
    """
    装饰器：为函数添加重连保护
    如果微信断开连接，会自动尝试重连
    """
    def wrapper(*args, **kwargs):
        global _reconnect_manager
        
        # 检查微信状态
        if _reconnect_manager and not _reconnect_manager._check_wechat_status():
            logger.warning("检测到微信断开连接，尝试重连...")
            if not _reconnect_manager.force_reconnect():
                logger.error("微信重连失败，无法执行操作")
                return None
        
        # 执行原函数
        return func(*args, **kwargs)
    
    return wrapper

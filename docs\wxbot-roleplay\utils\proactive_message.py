#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主动发消息功能模块
定期从监听用户、监听群聊和管理员中随机选择目标进行主动发消息
"""

import random
import threading
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

import config
from utils.logger import log
from utils.favorability import FavorabilityManager
from utils.text_processor import (
    remove_brackets_content,
    format_response,
    split_sentences,
    clean_text_content,
    apply_message_delay
)


class ProactiveMessageManager:
    """主动发消息管理器"""

    def __init__(self, bot_instance=None):
        """
        初始化主动发消息管理器

        Args:
            bot_instance: 机器人实例，用于发送消息
        """
        self.bot = bot_instance
        self.is_running = False
        self.timer_thread = None

        # 主动发消息配置（从配置文件读取）
        self.min_interval = getattr(config, 'PROACTIVE_MESSAGE_MIN_INTERVAL', 30) * 60
        self.max_interval = getattr(config, 'PROACTIVE_MESSAGE_MAX_INTERVAL', 120) * 60
        self.quiet_start_hour = getattr(config, 'PROACTIVE_QUIET_START_HOUR', 23)
        self.quiet_end_hour = getattr(config, 'PROACTIVE_QUIET_END_HOUR', 7)
        self.target_min_interval_hours = getattr(config, 'PROACTIVE_TARGET_MIN_INTERVAL', 6)
        
        # 当前人设名称和内容缓存
        self.current_avatar_name = None
        self.current_avatar_content = None

        # 消息模板
        self.message_templates = {
            'greeting': [
                "嗨～最近怎么样呀？",
                "想你了～在干什么呢？",
                "好久没聊天了，最近过得好吗？",
                "突然想起你，在忙什么呢？",
                "嘿嘿，来找你聊天啦～"
            ],
            'weather': [
                "今天天气不错呢，记得多出去走走哦～",
                "天气变化记得添衣保暖呀！",
                "这个天气很适合出门，要不要一起去玩？",
                "看了看天气预报，记得带伞哦～"
            ],
            'care': [
                "记得按时吃饭哦～",
                "工作累了记得休息一下呢",
                "最近有什么开心的事情吗？",
                "要好好照顾自己呀～",
                "今天有没有遇到什么有趣的事？"
            ],
            'random': [
                "刚刚想到一个有趣的问题想和你分享～",
                "突然很想知道你现在在想什么",
                "有什么想聊的话题吗？",
                "感觉今天是个聊天的好日子呢～",
                "嗯...突然想找你说说话"
            ]
        }

        # 上次发送记录
        self.last_send_times = {}

        log("主动发消息管理器初始化完成")
    
    def update_avatar_content(self, avatar_name: str, avatar_content: str = None):
        """更新主动消息管理器的人设内容
        
        Args:
            avatar_name: 新的人设名称
            avatar_content: 新的人设内容（可选）
        """
        try:
            self.current_avatar_name = avatar_name
            if avatar_content:
                self.current_avatar_content = avatar_content
            else:
                # 如果没有提供内容，从机器人实例获取
                if hasattr(self.bot, 'prompt_content'):
                    self.current_avatar_content = self.bot.prompt_content
            
            log(f"主动消息管理器已更新人设到: {avatar_name}")
        except Exception as e:
            log(f"更新主动消息管理器人设失败: {str(e)}")

    def get_all_targets(self) -> List[Dict[str, Any]]:
        """
        获取所有可能的发送目标

        Returns:
            目标列表，每个目标包含name和type信息
        """
        targets = []

        # 添加监听用户
        for user in config.LISTEN_LIST:
            targets.append({
                'name': user,
                'type': 'user',
                'source': 'listen_list'
            })

        # 添加监听群聊
        for group in config.GROUP_LIST:
            targets.append({
                'name': group,
                'type': 'group',
                'source': 'group_list'
            })

        # 添加管理员（如果不在监听列表中）
        for admin in config.ADMIN_LIST:
            if admin not in config.LISTEN_LIST:
                targets.append({
                    'name': admin,
                    'type': 'admin',
                    'source': 'admin_list'
                })

        return targets

    def is_quiet_time(self) -> bool:
        """
        检查当前是否为静默时间

        Returns:
            是否为静默时间
        """
        current_hour = datetime.now().hour

        if self.quiet_start_hour > self.quiet_end_hour:
            # 跨天的情况，如23点到7点
            return current_hour >= self.quiet_start_hour or current_hour < self.quiet_end_hour
        else:
            # 同一天的情况
            return self.quiet_start_hour <= current_hour < self.quiet_end_hour

    def should_send_to_target(self, target: Dict[str, Any]) -> bool:
        """
        判断是否应该向目标发送消息

        Args:
            target: 目标信息

        Returns:
            是否应该发送
        """
        target_key = f"{target['type']}_{target['name']}"

        # 检查上次发送时间
        if target_key in self.last_send_times:
            last_send = self.last_send_times[target_key]
            time_diff = datetime.now() - last_send

            # 如果距离上次发送不到指定小时数，跳过
            if time_diff < timedelta(hours=self.target_min_interval_hours):
                return False

        # 根据好感度判断发送概率
        if target['type'] in ['user', 'admin']:
            favorability = FavorabilityManager.get_favorability(target['name'])

            # 好感度越高，发送概率越大
            if favorability >= 80:
                send_probability = 0.8
            elif favorability >= 60:
                send_probability = 0.6
            elif favorability >= 40:
                send_probability = 0.4
            elif favorability >= 20:
                send_probability = 0.3
            else:
                send_probability = 0.2

            return random.random() < send_probability

        # 群聊默认较低概率
        return random.random() < 0.3

    def generate_proactive_message_prompt(self, target: Dict[str, Any]) -> str:
        """
        生成主动发消息的AI提示词

        Args:
            target: 目标信息

        Returns:
            AI提示词
        """
        current_hour = datetime.now().hour
        current_time = datetime.now().strftime("%H:%M")

        # 获取当前人设名称
        avatar_name = self.current_avatar_name or "林小葵"
        
        # 获取用户好感度信息
        favorability_info = ""
        if target['type'] in ['user', 'admin']:
            favorability = FavorabilityManager.get_favorability(target['name'])
            if favorability >= 80:
                relationship_level = "非常亲密的朋友"
            elif favorability >= 60:
                relationship_level = "好朋友"
            elif favorability >= 40:
                relationship_level = "普通朋友"
            elif favorability >= 20:
                relationship_level = "认识的人"
            else:
                relationship_level = "刚认识的人"

            favorability_info = f"你们的关系是{relationship_level}（好感度{favorability:.1f}/100）。"

        # 根据时间确定场景
        if 6 <= current_hour < 9:
            time_context = "早上"
            mood_suggestion = "可以问候早安，关心对方是否吃早餐，或分享今天的计划"
        elif 9 <= current_hour < 12:
            time_context = "上午"
            mood_suggestion = "可以聊聊工作学习，分享有趣的想法，或关心对方的状态"
        elif 12 <= current_hour < 14:
            time_context = "中午"
            mood_suggestion = "可以关心对方是否吃午餐，聊聊天气，或分享午休的想法"
        elif 14 <= current_hour < 18:
            time_context = "下午"
            mood_suggestion = "可以聊聊下午的活动，分享有趣的事情，或关心对方的心情"
        elif 18 <= current_hour < 21:
            time_context = "傍晚"
            mood_suggestion = "可以关心对方是否吃晚餐，聊聊今天的收获，或分享晚上的计划"
        elif 21 <= current_hour < 23:
            time_context = "晚上"
            mood_suggestion = "可以聊聊今天的感受，分享轻松的话题，或关心对方的休息"
        else:
            time_context = "深夜"
            mood_suggestion = "简单问候即可，不要太长，避免打扰对方休息"

        # 根据目标类型调整提示
        if target['type'] == 'group':
            target_context = f"这是一个群聊（{target['name']}），你要主动在群里发一条消息。消息要适合群聊环境，不要太私人化。"
        else:
            target_context = f"这是与{target['name']}的私聊，你要主动给TA发一条消息。{favorability_info}"

        # 构建AI提示词
        prompt = f"""你是{avatar_name}，现在时间是{time_context}（{current_time}），你想要主动发一条消息。

{target_context}

场景建议：{mood_suggestion}

请生成一条自然、温馨的主动消息，要求：
1. 消息要符合当前时间和场景
2. 语气要符合{avatar_name}的人设特征
3. 内容要自然，不要显得突兀
4. 长度尽量简短，不要超过20字
5. 可以包含emoji表情，让消息更生动
6. 不要提及这是"主动发消息"或"系统消息"

请直接生成消息内容，不要包含任何解释或说明："""

        return prompt

    def generate_ai_message(self, target: Dict[str, Any]) -> str:
        """
        使用AI生成主动消息内容

        Args:
            target: 目标信息

        Returns:
            AI生成的消息内容
        """
        try:
            # 生成AI提示词
            prompt = self.generate_proactive_message_prompt(target)

            # 调用机器人的AI生成方法
            if hasattr(self.bot, 'api_client') and self.bot.api_client:
                # 使用缓存的完整提示词构建系统消息
                if self.current_avatar_content:
                    system_prompt = self.current_avatar_content
                else:
                    system_prompt = getattr(self.bot, 'prompt_content', f"你是{self.current_avatar_name or '林小葵'}，一个可爱活泼的AI助手。")
                
                system_prompt += f"\n\n[特殊任务]: 请根据用户的要求生成合适的主动消息。要保持{self.current_avatar_name or '林小葵'}的人格特征和语言风格。"

                # 构建消息格式
                messages = [
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]

                # 调用AI API
                response = self.bot.api_client.chat.completions.create(
                    model=self.bot.model,
                    messages=messages,
                    max_tokens=150,  # 限制消息长度
                    temperature=0.8  # 增加一些随机性
                )

                ai_message = response.choices[0].message.content.strip()

                # 清理AI生成的内容
                ai_message = ai_message.replace('"', '').replace("'", "")
                if ai_message.startswith('林小葵：') or ai_message.startswith('林小葵:'):
                    ai_message = ai_message.split('：', 1)[-1].split(':', 1)[-1].strip()

                log(f"AI生成主动消息: {ai_message}")
                return ai_message
            else:
                log("AI客户端未初始化，使用备用消息模板", "WARNING")
                # 如果AI不可用，回退到模板消息
                return self.get_fallback_message(target)

        except Exception as e:
            log(f"AI生成消息失败: {str(e)}, 使用备用消息", "ERROR")
            return self.get_fallback_message(target)

    def get_fallback_message(self, target: Dict[str, Any]) -> str:
        """
        获取备用消息（当AI不可用时）

        Args:
            target: 目标信息

        Returns:
            备用消息内容
        """
        current_hour = datetime.now().hour

        # 根据时间和目标类型选择消息类型
        if target['type'] == 'group':
            # 群聊消息更加随意
            message_types = ['greeting', 'random']
        else:
            # 私聊消息更加关怀
            if 6 <= current_hour < 12:
                # 早上：问候和关怀
                message_types = ['greeting', 'care']
            elif 12 <= current_hour < 18:
                # 下午：天气和随机
                message_types = ['weather', 'random']
            elif 18 <= current_hour < 23:
                # 晚上：关怀和问候
                message_types = ['care', 'greeting']
            else:
                # 深夜：简单问候
                message_types = ['greeting']

        # 随机选择消息类型和具体消息
        message_type = random.choice(message_types)
        message = random.choice(self.message_templates[message_type])

        return message

    def send_proactive_message(self, target: Dict[str, Any]) -> bool:
        """
        向指定目标发送主动消息（支持句子分割）

        Args:
            target: 目标信息

        Returns:
            是否发送成功
        """
        try:
            if not self.bot:
                log("机器人实例未设置，无法发送主动消息", "ERROR")
                return False

            # 使用AI生成消息内容
            raw_message = self.generate_ai_message(target)

            if not raw_message:
                log("无法生成消息内容", "ERROR")
                return False

            # 尝试切换到目标聊天窗口
            try:
                chat_result = self.bot.wx.ChatWith(target['name'])
                if not chat_result:
                    log(f"无法找到聊天窗口: {target['name']}", "WARNING")
                    return False
            except Exception as switch_e:
                log(f"切换到 {target['name']} 聊天窗口失败: {str(switch_e)}", "WARNING")
                return False

            # 处理消息文本：移除括号内容、格式化并分割句子
            from utils.text_processor import remove_brackets_content
            cleaned_message = remove_brackets_content(raw_message)
            formatted_message = format_response(cleaned_message)
            sentences = split_sentences(formatted_message)

            if not sentences:
                # 如果分割失败，发送原始消息
                sentences = [raw_message]

            log(f"主动消息分割为 {len(sentences)} 个句子: {sentences}")

            # 逐句发送消息
            all_success = True
            sent_messages = []
            for i, sentence in enumerate(sentences):
                if not sentence.strip():
                    continue

                try:
                    # 发送单个句子
                    result = self.bot.wx.SendMsg(msg=sentence.strip(), who=target['name'], clear=True)

                    if result:
                        log(f"主动消息句子 {i+1}/{len(sentences)} 发送成功: {sentence.strip()}")
                        sent_messages.append(sentence.strip())

                        # 句子间添加2秒延迟，模拟自然打字
                        # 使用统一的延迟函数
                        apply_message_delay(i, len(sentences))
                    else:
                        log(f"主动消息句子 {i+1} 发送失败: {sentence.strip()}", "ERROR")
                        all_success = False

                except Exception as send_e:
                    log(f"发送句子 {i+1} 异常: {str(send_e)}", "ERROR")
                    all_success = False

            if all_success:
                # 记录发送时间
                target_key = f"{target['type']}_{target['name']}"
                self.last_send_times[target_key] = datetime.now()

                # 保存发送的消息到记忆系统
                if sent_messages and hasattr(self.bot, 'memory_manager'):
                    try:
                        # 合并所有发送的句子
                        full_message = " ".join(sent_messages)

                        # 判断是否为群聊
                        is_group = target['type'] == 'group'

                        # 1. 保存AI发送的消息到chat_messages表
                        self.bot.memory_manager.save_ai_message(
                            sender="林小葵",  # AI的名字
                            message=full_message,
                            chat_name=target['name'],
                            is_group=is_group
                        )

                        # 2. 构建对话格式并保存到Memory表的history字段
                        conversation = [
                            {"role": "assistant", "content": full_message}
                        ]

                        # 构建记忆键
                        if is_group:
                            memory_key = f"group_{target['name']}"
                        else:
                            memory_key = f"user_{target['name']}"

                        # 保存到Memory表，让AI能够获取
                        self.bot.memory_manager.save_memory(memory_key, conversation)

                        log(f"主动发送消息已保存到记忆系统: {target['name']}")

                    except Exception as memory_e:
                        log(f"保存主动发送消息到记忆系统失败: {str(memory_e)}", "ERROR")

                log(f"主动发送AI生成消息给 {target['name']} ({target['type']}) 完成，共 {len(sentences)} 句")
                return True
            else:
                log(f"主动消息部分发送失败: {target['name']}", "WARNING")
                return False

        except Exception as e:
            log(f"发送主动消息失败: {str(e)}", "ERROR")
            return False

    def run_proactive_message_cycle(self):
        """
        运行一次主动发消息循环
        """
        try:
            # 检查是否为静默时间
            if self.is_quiet_time():
                log("当前为静默时间，跳过主动发消息")
                return

            # 获取所有目标
            targets = self.get_all_targets()
            if not targets:
                log("没有可用的发送目标")
                return

            log(f"开始主动发消息循环，共有 {len(targets)} 个目标")

            # 过滤出符合条件的目标
            valid_targets = []
            for target in targets:
                if self.should_send_to_target(target):
                    valid_targets.append(target)

            if not valid_targets:
                log("没有符合发送条件的目标")
                return

            # 随机选择一个目标
            selected_target = random.choice(valid_targets)

            log(f"选择目标: {selected_target['name']} ({selected_target['type']})")

            # 发送消息
            success = self.send_proactive_message(selected_target)

            if success:
                log(f"主动消息发送成功: {selected_target['name']}")
            else:
                log(f"主动消息发送失败: {selected_target['name']}")

        except Exception as e:
            log(f"主动发消息循环异常: {str(e)}", "ERROR")

    def timer_worker(self):
        """
        定时器工作线程
        """
        while self.is_running:
            try:
                # 计算下次执行时间
                next_interval = random.randint(self.min_interval, self.max_interval)
                log(f"下次主动发消息将在 {next_interval // 60} 分钟后执行")

                # 等待指定时间
                for _ in range(next_interval):
                    if not self.is_running:
                        break
                    time.sleep(1)

                if self.is_running:
                    # 执行主动发消息
                    self.run_proactive_message_cycle()

            except Exception as e:
                log(f"定时器工作线程异常: {str(e)}", "ERROR")
                time.sleep(60)  # 出错后等待1分钟再继续

    def start(self):
        """
        启动主动发消息功能
        """
        if self.is_running:
            log("主动发消息功能已在运行中")
            return

        if not self.bot:
            log("机器人实例未设置，无法启动主动发消息功能", "ERROR")
            return

        self.is_running = True
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()

        log("主动发消息功能已启动")

    def stop(self):
        """
        停止主动发消息功能
        """
        if not self.is_running:
            log("主动发消息功能未在运行")
            return

        self.is_running = False

        if self.timer_thread and self.timer_thread.is_alive():
            self.timer_thread.join(timeout=5)

        log("主动发消息功能已停止")

    def get_status(self) -> Dict[str, Any]:
        """
        获取主动发消息功能状态

        Returns:
            状态信息
        """
        targets = self.get_all_targets()

        return {
            'is_running': self.is_running,
            'total_targets': len(targets),
            'targets': targets,
            'last_send_times': self.last_send_times,
            'is_quiet_time': self.is_quiet_time(),
            'quiet_hours': f"{self.quiet_start_hour}:00 - {self.quiet_end_hour}:00",
            'interval_range': f"{self.min_interval // 60} - {self.max_interval // 60} 分钟"
        }
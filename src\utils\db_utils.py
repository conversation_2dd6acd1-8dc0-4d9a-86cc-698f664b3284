#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库工具模块
提供SQLite数据库操作的工具函数
"""

import os
import sqlite3
import logging
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Union

logger = logging.getLogger('main')

class DatabaseManager:
    """
    数据库管理器
    提供SQLite数据库的通用操作方法
    """
    
    def __init__(self, db_path: str = None):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径，如果为None则使用默认路径
        """
        if db_path is None:
            # 默认数据库路径
            self.db_path = os.path.join(os.path.dirname(__file__), "..", "..", "data", "memory.db")
        else:
            self.db_path = db_path
            
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 初始化数据库
        self._initialize_database()
    
    def _initialize_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建核心记忆表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS core_memory (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        avatar_name TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        content TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(avatar_name, user_id)
                    )
                ''')
                
                # 创建短期记忆表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS short_memory (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        avatar_name TEXT NOT NULL,
                        user_id TEXT NOT NULL,
                        user_message TEXT NOT NULL,
                        bot_reply TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        date_key TEXT NOT NULL,
                        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_core_memory_avatar_user ON core_memory(avatar_name, user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_short_memory_avatar_user ON short_memory(avatar_name, user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_short_memory_date ON short_memory(date_key)')
                
                conn.commit()
                logger.info(f"数据库初始化完成: {self.db_path}")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_core_memory(self, avatar_name: str, user_id: str) -> Optional[Dict]:
        """
        获取核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            
        Returns:
            核心记忆字典，如果不存在则返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, avatar_name, user_id, content, timestamp, created_at, updated_at
                    FROM core_memory
                    WHERE avatar_name = ? AND user_id = ?
                ''', (avatar_name, user_id))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'id': row[0],
                        'avatar_name': row[1],
                        'user_id': row[2],
                        'content': row[3],
                        'timestamp': row[4],
                        'created_at': row[5],
                        'updated_at': row[6]
                    }
                return None
                
        except Exception as e:
            logger.error(f"获取核心记忆失败: {e}")
            return None
    
    def save_core_memory(self, avatar_name: str, user_id: str, content: str, timestamp: str = None) -> bool:
        """
        保存核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            content: 记忆内容
            timestamp: 时间戳，如果为None则使用当前时间
            
        Returns:
            是否保存成功
        """
        try:
            if timestamp is None:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO core_memory 
                    (avatar_name, user_id, content, timestamp, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (avatar_name, user_id, content, timestamp))
                
                conn.commit()
                logger.info(f"核心记忆保存成功: {avatar_name}/{user_id}")
                return True
                
        except Exception as e:
            logger.error(f"保存核心记忆失败: {e}")
            return False
    
    def delete_core_memory(self, avatar_name: str, user_id: str) -> bool:
        """
        删除核心记忆
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            
        Returns:
            是否删除成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    DELETE FROM core_memory
                    WHERE avatar_name = ? AND user_id = ?
                ''', (avatar_name, user_id))
                
                conn.commit()
                logger.info(f"核心记忆删除成功: {avatar_name}/{user_id}")
                return True
                
        except Exception as e:
            logger.error(f"删除核心记忆失败: {e}")
            return False
    
    def get_conversations(self, avatar_name: str, user_id: str, limit: int = None) -> List[Dict]:
        """
        获取对话记录
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            limit: 限制返回的记录数
            
        Returns:
            对话记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if limit:
                    cursor.execute('''
                        SELECT id, avatar_name, user_id, user_message, bot_reply, timestamp, date_key, created_at
                        FROM short_memory
                        WHERE avatar_name = ? AND user_id = ?
                        ORDER BY created_at DESC
                        LIMIT ?
                    ''', (avatar_name, user_id, limit))
                else:
                    cursor.execute('''
                        SELECT id, avatar_name, user_id, user_message, bot_reply, timestamp, date_key, created_at
                        FROM short_memory
                        WHERE avatar_name = ? AND user_id = ?
                        ORDER BY created_at DESC
                    ''', (avatar_name, user_id))
                
                rows = cursor.fetchall()
                conversations = []
                for row in rows:
                    conversations.append({
                        'id': row[0],
                        'avatar_name': row[1],
                        'user_id': row[2],
                        'user_message': row[3],
                        'bot_reply': row[4],
                        'timestamp': row[5],
                        'date_key': row[6],
                        'created_at': row[7]
                    })
                
                return conversations
                
        except Exception as e:
            logger.error(f"获取对话记录失败: {e}")
            return []
    
    def add_conversation(self, avatar_name: str, user_id: str, user_message: str, bot_reply: str, timestamp: str = None) -> bool:
        """
        添加对话记录
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            user_message: 用户消息
            bot_reply: 机器人回复
            timestamp: 时间戳，如果为None则使用当前时间
            
        Returns:
            是否添加成功
        """
        try:
            if timestamp is None:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            date_key = datetime.now().strftime("%Y-%m-%d")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO short_memory 
                    (avatar_name, user_id, user_message, bot_reply, timestamp, date_key)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (avatar_name, user_id, user_message, bot_reply, timestamp, date_key))
                
                conn.commit()
                logger.debug(f"对话记录添加成功: {avatar_name}/{user_id}")
                return True
                
        except Exception as e:
            logger.error(f"添加对话记录失败: {e}")
            return False
    
    def delete_conversation(self, avatar_name: str, user_id: str) -> bool:
        """
        删除对话记录
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            
        Returns:
            是否删除成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    DELETE FROM short_memory
                    WHERE avatar_name = ? AND user_id = ?
                ''', (avatar_name, user_id))
                
                conn.commit()
                logger.info(f"对话记录删除成功: {avatar_name}/{user_id}")
                return True
                
        except Exception as e:
            logger.error(f"删除对话记录失败: {e}")
            return False
    
    def get_conversation_count(self, avatar_name: str, user_id: str) -> int:
        """
        获取对话数量
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            
        Returns:
            对话数量
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*)
                    FROM short_memory
                    WHERE avatar_name = ? AND user_id = ?
                ''', (avatar_name, user_id))
                
                row = cursor.fetchone()
                return row[0] if row else 0
                
        except Exception as e:
            logger.error(f"获取对话数量失败: {e}")
            return 0
    
    def cleanup_old_conversations(self, avatar_name: str, user_id: str, keep_days: int = 30) -> bool:
        """
        清理旧的对话记录
        
        Args:
            avatar_name: 角色名称
            user_id: 用户ID
            keep_days: 保留天数
            
        Returns:
            是否清理成功
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            cutoff_str = cutoff_date.strftime("%Y-%m-%d %H:%M:%S")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    DELETE FROM short_memory
                    WHERE avatar_name = ? AND user_id = ? AND created_at < ?
                ''', (avatar_name, user_id, cutoff_str))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理了 {deleted_count} 条旧对话记录: {avatar_name}/{user_id}")
                return True
                
        except Exception as e:
            logger.error(f"清理旧对话记录失败: {e}")
            return False
    
    def get_database_stats(self) -> Dict:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取核心记忆数量
                cursor.execute('SELECT COUNT(*) FROM core_memory')
                core_memory_count = cursor.fetchone()[0]
                
                # 获取短期记忆数量
                cursor.execute('SELECT COUNT(*) FROM short_memory')
                short_memory_count = cursor.fetchone()[0]
                
                # 获取用户数量
                cursor.execute('SELECT COUNT(DISTINCT user_id) FROM core_memory')
                user_count = cursor.fetchone()[0]
                
                # 获取角色数量
                cursor.execute('SELECT COUNT(DISTINCT avatar_name) FROM core_memory')
                avatar_count = cursor.fetchone()[0]
                
                # 获取数据库文件大小
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                
                return {
                    'core_memory_count': core_memory_count,
                    'short_memory_count': short_memory_count,
                    'user_count': user_count,
                    'avatar_count': avatar_count,
                    'database_size': db_size,
                    'database_path': self.db_path
                }
                
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {}


# 为了向后兼容，创建一个全局实例
_db_instance = None

def get_database_manager(db_path: str = None) -> DatabaseManager:
    """
    获取数据库管理器实例（单例模式）
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        DatabaseManager实例
    """
    global _db_instance
    if _db_instance is None or (db_path is not None and _db_instance.db_path != db_path):
        _db_instance = DatabaseManager(db_path)
    return _db_instance


# 向后兼容的函数接口
async def get_core_memory(avatar_name: str, user_id: str) -> Optional[Dict]:
    """
    获取核心记忆（向后兼容接口）
    
    Args:
        avatar_name: 角色名称
        user_id: 用户ID
        
    Returns:
        核心记忆字典，如果不存在则返回None
    """
    db_manager = get_database_manager()
    return db_manager.get_core_memory(avatar_name, user_id)


async def save_core_memory(avatar_name: str, user_id: str, content: str, timestamp: str = None) -> bool:
    """
    保存核心记忆（向后兼容接口）
    
    Args:
        avatar_name: 角色名称
        user_id: 用户ID
        content: 记忆内容
        timestamp: 时间戳
        
    Returns:
        是否保存成功
    """
    db_manager = get_database_manager()
    return db_manager.save_core_memory(avatar_name, user_id, content, timestamp)


async def delete_core_memory(avatar_name: str, user_id: str) -> bool:
    """
    删除核心记忆（向后兼容接口）
    
    Args:
        avatar_name: 角色名称
        user_id: 用户ID
        
    Returns:
        是否删除成功
    """
    db_manager = get_database_manager()
    return db_manager.delete_core_memory(avatar_name, user_id)


async def get_conversations(avatar_name: str, user_id: str, limit: int = None) -> List[Dict]:
    """
    获取对话记录（向后兼容接口）
    
    Args:
        avatar_name: 角色名称
        user_id: 用户ID
        limit: 限制返回的记录数
        
    Returns:
        对话记录列表
    """
    db_manager = get_database_manager()
    return db_manager.get_conversations(avatar_name, user_id, limit)


async def add_conversation(avatar_name: str, user_id: str, user_message: str, bot_reply: str, timestamp: str = None) -> bool:
    """
    添加对话记录（向后兼容接口）
    
    Args:
        avatar_name: 角色名称
        user_id: 用户ID
        user_message: 用户消息
        bot_reply: 机器人回复
        timestamp: 时间戳
        
    Returns:
        是否添加成功
    """
    db_manager = get_database_manager()
    return db_manager.add_conversation(avatar_name, user_id, user_message, bot_reply, timestamp)


async def delete_conversation(avatar_name: str, user_id: str) -> bool:
    """
    删除对话记录（向后兼容接口）
    
    Args:
        avatar_name: 角色名称
        user_id: 用户ID
        
    Returns:
        是否删除成功
    """
    db_manager = get_database_manager()
    return db_manager.delete_conversation(avatar_name, user_id)


if __name__ == "__main__":
    # 测试代码
    db_manager = get_database_manager()
    stats = db_manager.get_database_stats()
    print("数据库统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")